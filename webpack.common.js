const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
require('dotenv').config();
const webpack = require('webpack')

module.exports = {
	entry: {
		app: './src/index.jsx',
		ssr: './src/server/ssr/index.js',
	},
	resolve: {
		modules: [ path.resolve(__dirname, 'src'), 'node_modules' ],
		extensions: [ '.js', '.jsx' ],
		fallback :{ "buffer": false }
	},
	plugins: [
		new webpack.DefinePlugin({
			'process.env': JSON.stringify(process.env)
		 }),
		new CleanWebpackPlugin(),
		new HtmlWebpackPlugin({
			title: 'Pigeon Web',
			favicon: path.resolve(__dirname, 'src/assets/favicon.ico'),
			meta: {
				viewport: 'width=device-width, initial-scale=1.0',
			},
			template: 'index.ejs',
			templateParameters: {
				ANALYTICS_LIB_URL: process.env.ANALYTICS_LIB_URL || '{ANALYTICS_LIB_URL}',
				ANALYTICS_ENV: process.env.ANALYTICS_ENV || '{ANALYTICS_ENV}',
				DEPLOY_REGION: process.env.DEPLOY_REGION || '{DEPLOY_REGION}',
				LAUNCH_DARKLY_CLIENT_SIDE_ID: process.env.LAUNCH_DARKLY_CLIENT_SIDE_ID || '{LAUNCH_DARKLY_CLIENT_SIDE_ID}',
				SOL_ENDPOINTS: process.env.SOL_ENDPOINTS || '{SOL_ENDPOINTS}',
				SCOTIAHOME_ENDPOINTS: process.env.SCOTIAHOME_ENDPOINTS || '{SCOTIAHOME_ENDPOINTS}',
				SCRL_ENDPOINTS: process.env.SCRL_ENDPOINTS || '{SCRL_ENDPOINTS}',
			},
		}),
		new NodePolyfillPlugin(),
	],
	output: {
		path: path.join(__dirname, 'dist'),
		filename: '[name].bundle.js',
		publicPath: '/',
	},
	module: {
		rules: [
			{
				test: /\.jsx?/,
				exclude: /(node_modules)/,
				use: {
					loader: 'babel-loader',
				},
			},
			{
				test: /\.(sa|sc|c)ss$/,
				use: [
					{
						loader: MiniCssExtractPlugin.loader,
						options: {
							publicPath: './',
						},
					},
					'css-loader', // translates CSS into CommonJS
					{ loader: 'sass-loader', options: { implementation: require('sass') } },
				],
			},
			{
				test: /\.(jpe?g$|ico|gif|png)$/,
				use :[
					{loader: 'file-loader' , options:{
						name:'[name].[ext]'
					}}
				]
			},
			{
				test: /\.(eot|svg|ttf|woff|woff2|otf)$/,
				type: 'asset/resource',
    		dependency: { not: ['url'] },
			},
		],
	},
};
