const fs = require('fs');
const {
	ANALYTICS_LIB_URL,
	ANALYTICS_ENV,
	DEPLOY_REGION,
	LAUNCH_DARKLY_CLIENT_SIDE_ID,
	SOL_ENDPOINTS,
	SCOTIAHOME_ENDPOINTS,
	SCRL_ENDPOINTS,
} = process.env;

const indexContent = fs.readFileSync('./dist/index.html').toString();

const patchedIndex = indexContent
	.replace('{ANALYTICS_LIB_URL}', ANALYTICS_LIB_URL)
	.replace('{ANALYTICS_ENV}', ANALYTICS_ENV)
	.replace('{DEPLOY_REGION}', DEPLOY_REGION)
	.replace('{LAUNCH_DARKLY_CLIENT_SIDE_ID}', LAUNCH_DARKLY_CLIENT_SIDE_ID)
	.replace('{SOL_ENDPOINTS}', SOL_ENDPOINTS)
	.replace('{SCOTIAHOME_ENDPOINTS}', SCOTIAHOME_ENDPOINTS)
	.replace('{SCRL_ENDPOINTS}', SCRL_ENDPOINTS);

fs.writeFileSync('./dist/index.html', patchedIndex);
