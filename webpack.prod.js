const fs = require('fs');
const merge = require('webpack-merge');
const common = require('./webpack.common.js');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

// inspired by https://stackoverflow.com/a/59236546
class MetaInfoPlugin {
	constructor(options) {
		this.options = { filename: 'meta.json', ...options };
	}

	apply(compiler) {
		compiler.hooks.done.tap(this.constructor.name, stats => {
			const json = JSON.stringify({ hash: stats.hash });
			fs.writeFileSync(this.options.filename, json, 'utf8');
		});
	}
}

module.exports = merge(common, {
	mode: 'production',
	plugins: [
		new MiniCssExtractPlugin({
			filename: '[name].[fullhash].css',
			chunkFilename: '[id].[fullhash].css',
		}),
		new MetaInfoPlugin(),
	],
	output: {
		filename: '[name].[fullhash].js',
		publicPath: '/static',
	},
});
