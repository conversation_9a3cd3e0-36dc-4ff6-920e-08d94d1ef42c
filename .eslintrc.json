{"env": {"es6": true, "node": true, "jest": true}, "parser": "babel-es<PERSON>", "globals": {"fetch": true}, "extends": ["@scotia/eslint-config-scotiabank", "plugin:react/recommended"], "rules": {"comma-dangle": ["error", "always-multiline"], "indent": [2, "tab", {"SwitchCase": 1}], "no-tabs": 0, "prefer-template": ["error"], "space-before-function-paren": ["error", {"asyncArrow": "always", "anonymous": "never", "named": "never"}]}, "settings": {"react": {"version": "detect"}}}