openapi: 3.0.2
info:
  title: Pigeon Web API
  version: "@PIGEON_WEB_API_VERSION@"
  description: Pigeon Web API
  termsOfService: http://scotiabank.com/terms/
  contact:
    name: Pigeon Team
    url: http://confluence.agile.bns/PIGEON
    email: <EMAIL>
  license:
    name: MIT
    url: http://opensource.org/licenses/MIT
servers:
  - url: /
tags:
  - name: Campaigns
paths:
  /v1/rendered-campaigns:
    get:
      description: Get the list of campaigns available for a customer.
      operationId: getCampaignsV1
      tags:
        - Campaigns
      security:
        - ServiceToken: [cdb.pigeon.campaigns.read]
      parameters:
        - $ref: '#/components/parameters/preferredEnvironment'
        - $ref: '#/components/parameters/xLanguage'
        - $ref: '#/components/parameters/xChannelId'
        - $ref: '#/components/parameters/xMockInsights'
        - $ref: '#/components/parameters/xCustomerScotiacard'
        - $ref: '#/components/parameters/application'
        - $ref: '#/components/parameters/platform'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/container'
        - $ref: '#/components/parameters/insight'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
      responses:
        200:
          $ref: "#/components/responses/v1.RenderedCampaignsResponse"
        400:
          $ref: "#/components/responses/v1.BadRequestResponse"
        401:
          $ref: "#/components/responses/v1.UnauthenticatedResponse"
        403:
          $ref: "#/components/responses/v1.ForbiddenResponse"
        404:
          $ref: "#/components/responses/v1.NotFoundResponse"
        500:
          $ref: "#/components/responses/v1.InternalServerErrorResponse"
  /v1/rendered-campaigns/{ruleId}:
    get:
      description: Get campaign details.
      operationId: getCampaignDetailsV1
      tags:
        - Campaigns
      security:
        - ServiceToken: [cdb.pigeon.campaigns.read]
      parameters:
        - $ref: '#/components/parameters/preferredEnvironment'
        - $ref: '#/components/parameters/xLanguage'
        - $ref: '#/components/parameters/xChannelId'
        - $ref: '#/components/parameters/xMockInsights'
        - $ref: '#/components/parameters/xCustomerScotiacard'
        - $ref: '#/components/parameters/application'
        - $ref: '#/components/parameters/platform'
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/container'
        - $ref: '#/components/parameters/ruleId'
        - $ref: '#/components/parameters/messageId'
        - $ref: '#/components/parameters/insight'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
      responses:
        200:
          $ref: "#/components/responses/v1.RenderedCampaignResponse"
        400:
          $ref: "#/components/responses/v1.BadRequestResponse"
        401:
          $ref: "#/components/responses/v1.UnauthenticatedResponse"
        403:
          $ref: "#/components/responses/v1.ForbiddenResponse"
        404:
          $ref: "#/components/responses/v1.NotFoundResponse"
        500:
          $ref: "#/components/responses/v1.InternalServerErrorResponse"
  /v1/rendered-campaigns/{ruleId}/dispositions:
    post:
      description: Set disposition for a campaign.
      operationId: setCampaignDispositionV1
      tags:
        - Campaigns
      security:
        - ServiceToken: [cdb.pigeon.campaigns.dispositions.write]
      parameters:
        - $ref: '#/components/parameters/preferredEnvironment'
        - $ref: '#/components/parameters/xChannelId'
        - $ref: '#/components/parameters/xMockInsights'
        - $ref: '#/components/parameters/xCustomerScotiacard'
        - $ref: '#/components/parameters/ruleId'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
      requestBody:
        $ref: '#/components/requestBodies/v1.SetDispositionBody'
      responses:
        200:
          $ref: "#/components/responses/v1.RenderedCampaignDispositionResponse"
        400:
          $ref: "#/components/responses/v1.BadRequestResponse"
        401:
          $ref: "#/components/responses/v1.UnauthenticatedResponse"
        403:
          $ref: "#/components/responses/v1.ForbiddenResponse"
        404:
          $ref: "#/components/responses/v1.NotFoundResponse"
        500:
          $ref: "#/components/responses/v1.InternalServerErrorResponse"
security:
  - ServiceToken: []
components:
  securitySchemes:
    ServiceToken:
        type: http
        description: The access token in "Bearer <token>" format.
        scheme: bearer
        bearerFormat: "Bearer <token>"
  parameters:
    preferredEnvironment:
      name: preferred-environment
      description: Optional environment name that will be used to call downstream services in IST/UAT/NFT if specified (e.g. `istgreen`, `uatblack`).
      in: header
      required: false
      schema:
        type: string
        pattern: '^[a-zA-Z]+$'
    xLanguage:
      name: x-language
      description: >
        Return campaigns with content in the specified language. Available options:
         * `en` - English
         * `fr` - French
      in: header
      schema:
        type: string
        enum: [en,fr]
        default: en
    xMockInsights:
      name: x-mock-insight
      description: Optional flag to enable calls to a mock service instead of Insights in IST/UAT/NFT. Any passed value will be treated as `true`.
      in: header
      required: false
      schema:
        type: string
    xCustomerScotiacard:
      name: x-customer-scotiacard
      description: Optional header to pass customer's scotia card number that will be used for targeted and mass campaigns.
      in: header
      required: true
      schema:
        type: string
    xChannelId:
      name: x-channel-id
      description: Channel id
      in: header
      required: true
      schema:
        type: string
        enum: [ABM]
    ruleId:
      name: ruleId
      description: Campaign's rule id
      in: path
      required: true
      schema:
        type: string
        pattern: '^[a-zA-Z0-9]+$'
        minLength: 1
        maxLength: 20
    messageId:
      name: messageId
      description: Personalized message id for targeted campaigns
      in: query
      required: false
      schema:
        type: string
        pattern: '^[a-zA-Z0-9-]+$'
        minLength: 1
        maxLength: 50
    application:
      name: application
      description: Target application name
      in: query
      required: true
      schema:
        type: string
        pattern: '^[a-z]+$'
        minLength: 1
        maxLength: 20
    platform:
      name: platform
      description: Target platform name
      in: query
      required: true
      schema:
        type: string
        pattern: '^[a-z]+$'
        minLength: 1
        maxLength: 20
    page:
      name: page
      description: Return campaigns for the specified page name. When page name is not specified campaigns for all pages of the app and platform will be returned.
      in: query
      required: false
      schema:
        type: string
        pattern: '^[a-zA-Z0-9_-]+$'
        minLength: 1
        maxLength: 20
    container:
      name: container
      description: Return campaigns for the specified container. When specified, `page` parameter should also be present. When not specified campaigns for all containers will be returned.
      in: query
      required: false
      schema:
        type: string
        pattern: '^[a-zA-Z0-9_-]+$'
        minLength: 1
        maxLength: 20
    insight:
      name: insight
      description: Flag that indicates that Insight data should be added to the response. When set to true, 3 additional fileds will be returned in `external_ref` property - `data`, `data_context` and `data_transformed`
      in: query
      required: false
      schema:
        type: boolean
        enum: [true, false]
        default: false
    OriginatingApplicationCodeParam:
      name: x-originating-appl-code
      in: header
      description: 'This is to identify the source Application EPM code which is making
        this request. Originating application is responsible for sending x-originating-appl-code.
        For example, if  application 1 makes call to application 2 and then application
        2 makes a call to application 3, application 1 will populate and send the
        x-originating-appl-code to application 2 and application 2 will propagate
        the same x-originating-appl-code to application 3. example: "BFB6"'
      required: false
      schema:
        type: string
  requestBodies:
    v1.SetDispositionBody:
      description: Disposition payload
      content:
        application/json:
          schema:
            required:
              - message_id
              - application
              - platform
              - page
              - container
              - disposition
            properties:
              message_id:
                description: Message id
                type: string
                pattern: '^[a-zA-Z0-9-]+$'
                minLength: 1
                maxLength: 50
              application:
                description: Application name
                type: string
                pattern: '^[a-z]+$'
                minLength: 1
                maxLength: 20
              platform:
                description: Platform name
                type: string
                pattern: '^[a-z]+$'
                minLength: 1
                maxLength: 20
              page:
                description: Page name
                type: string
                pattern: '^[a-zA-Z0-9_-]+$'
                minLength: 1
                maxLength: 20
              container:
                description: Container name
                type: string
                pattern: '^[a-zA-Z0-9_-]+$'
                minLength: 1
                maxLength: 20
              disposition:
                description: >
                  Disposition value. Available options:
                   * `V` - Viewed
                   * `Y` - TBD
                   * `N` - TBD
                   * ` ` -TBD
                type: string
                enum: ["V", "Y", "N", " "]
  responses:
    v1.RenderedCampaignsResponse:
      description: Successful response
      content:
        application/json:
          schema:
            required:
              - data
            allOf:
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/v1.Campaigns'
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.RenderedCampaignResponse:
      description: Successful response
      content:
        application/json:
          schema:
            required:
              - data
            allOf:
              - type: object
                properties:
                  data:
                    $ref: '#/components/schemas/v1.Campaign'
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.RenderedCampaignDispositionResponse:
      description: Successful response
      content:
        application/json:
          schema:
            required:
              - data
            allOf:
              - type: object
                properties:
                  data:
                    type: object
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.BadRequestResponse:
      description: Bad request error response
      content:
        application/json:
          schema:
            required:
              - notifications
            allOf:
              - type: object
                properties:
                  data:
                    example: {}
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.UnauthenticatedResponse:
      description: Unauthenticated error response
      content:
        application/json:
          schema:
            required:
              - notifications
            allOf:
              - type: object
                properties:
                  data:
                    example: {}
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.ForbiddenResponse:
      description: Forbidden error response
      content:
        application/json:
          schema:
            required:
              - notifications
            allOf:
              - type: object
                properties:
                  data:
                    example: {}
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.NotFoundResponse:
      description: Not found error response
      content:
        application/json:
          schema:
            required:
              - notifications
            allOf:
              - type: object
                properties:
                  data:
                    example: {}
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
    v1.InternalServerErrorResponse:
      description: Internal server error response
      content:
        application/json:
          schema:
            required:
              - notifications
            allOf:
              - type: object
                properties:
                  data:
                    example: {}
              - type: object
                properties:
                  notifications:
                    $ref: '#/components/schemas/v1.Notifications'
  schemas:
    v1.Campaigns:
      description: List of campaigns
      properties:
        total:
          description: Total number of returned campaigns
          type: number
          format: int32
        items:
          description: Campaigns
          type: array
          items:
            $ref: '#/components/schemas/v1.Campaign'
    v1.Campaign:
      description: Campaign
      properties:
        id:
          description: Rule id
          type: string
          example: "Da9yckGAPFL6"
        name:
          description: Name
          type: string
          example: "Pre-Approved VISA Infinite Campaign NOV-19"
        container:
          description: Target container name
          type: string
          example: "priority-box"
        pages:
          description: Target pages
          type: array
          items:
            type: string
          example: ["accounts","my-updates"]
        urgent:
          description: Urgent flag
          type: boolean
          example: true
        dismissable:
          description: Dismissable flag. This value defines if customer can dismiss this campaign.
          type: boolean
          example: false
        viewed:
          description: Viewed status. This value identifies if customer has already seen this campaign.
          type: boolean
          example: false
        external_ref:
          $ref: "#/components/schemas/v1.CampaignExternalRef"
        template:
          description: Template name that describes what template was used to render `content`.
          type: string
          example: 'tplAbmPreview'
        content:
          description: Campaign's content in HTML format.
          type: string
          example: "<div>Campaign's content</div>"
    v1.CampaignExternalRef:
      description: Campaign's external information passed from external sources.
      required:
        - source
        - message_id
        - campaign_type
        - campaign_id
      properties:
        source:
          description: >
            Source of the campaign. Available options:
             * `KT` - KT campaign
             * `PEGA` - Pega campaign
             * `DMS` - Pigeon campaign
          type: string
          enum: ["KT","PEGA","DMS"]
        message_id:
          description: Personalized unique message id for targeted campaigns.
          type: string
          example: ''
        campaign_id:
          description: Campaign ID
          type: string
          example: 'ABC01'
        data_context:
          description: >
            Context name that defines how personalized data is structured and transformed.
          type: string
          example: ["pacc"]
        data:
          description: Personalized data available for this campaign in key-value pairs.
          type: object
          example: { NAME: 'John Doe', PROD: 'VCLAB', OTHER1: '20200101', OTHER2: '0010000' }
        data_transformed:
          description: Personalized data transformed using transformation rules specific to the campaign and variable. Transformed variables may differ from personalized data (non-transformed), as for some campaigns one transformed value can be created using multiple personalized variables.
          type: object
          example: { SOLUI_NAME_END: 'John Doe', SOLUI_PROD_END: 'Visa Passport Infinite', SOLUI_OTHER1_END: 'January 1, 2020', SOLUI_OTHER2_END: '$10,000' }
    v1.Notifications:
      description: List of notifications and errors
      type: array
      items:
        $ref: '#/components/schemas/v1.Notification'
    v1.Notification:
      description: Notification
      properties:
        code:
          description: Code
          type: string
        message:
          description: Message
          type: string
        uuid:
          description: Globally unique identifier of a notification
          type: string
          example: 'e9a2c720-9c12-4984-9e66-a8afbeb6a9a4'
        timestamp:
          description: Timestamp (UTC timezone)
          type: string
          format: date-time
          example: '2020-01-01T22:40:30.123Z'
        metadata:
          description: Optional context specific information
          type: object
