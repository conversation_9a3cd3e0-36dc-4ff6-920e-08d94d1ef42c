# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.27.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.26.3...1.27.0) (2025-06-19)


### Bug Fixes

* **PIGEON-5481:** backupJWKS handling strategy and blackduck issues ([d982b8c](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/d982b8c006153e4025b97628e81024074422c5c3))

### [1.26.3](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.26.2...1.26.3) (2025-05-01)


### Bug Fixes

* uuidv4 error in pigeon-pack and backupJWKS format issue ([e67827c](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/e67827c89663000b973407fbca1ab0de74b451d2))

### [1.26.2](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.23.2...1.26.2) (2025-05-01)


### Features

* **PIGEON-5538:** add backup JWKS as a fallback mechanism when passport request failed ([3bd8e49](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/3bd8e4998996e1fb794f477ea499db53cdeb96fa))


### Bug Fixes

* **PIGEON-5517:** default x-channel-id to mobile if not present in req ([d77b7ec](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/d77b7ec13eb4590761f386a833da208f532fb712))

### [1.26.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.26.0...1.26.1) (2025-04-29)

## [1.26.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.25.1...1.26.0) (2025-04-24)


### Bug Fixes

* **PIGEON-5517:** default x-channel-id to mobile if not present in req ([d77b7ec](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/d77b7ec13eb4590761f386a833da208f532fb712))

### [1.25.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.25.0...1.25.1) (2025-03-21)

## [1.25.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.24.0...1.25.0) (2025-03-14)

## [1.24.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.23.2...1.24.0) (2025-02-28)

### [1.23.2](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.23.1...1.23.2) (2025-01-28)


### Bug Fixes

* cookies.hasOwnProperty is not a function ([0a2bf42](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/0a2bf423fe994a023c8c23454fa80e8cc34bf196))

### [1.23.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.23.0...1.23.1) (2025-01-17)


### Features

* **PIGEON-5412:** add 'x-feature-flag-uid' to passThroughHeaders ([bf0b90b](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/bf0b90b13b0d271c0dd95ed1eb429e6f37d63cc6))
* **PIGEON-5412:** pass 'x-feature-flag-uid' header to Insights ([5bc9d3d](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/5bc9d3dff3a3e93ff640e8f163aa56ca8e2005b3))

## [1.23.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.22.0...1.23.0) (2024-11-27)


### Features

* **PIGEON-4939:** upgrade node to v20.15.1 ([794d10e](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/794d10ef7d840af7ecc21cd5ddc29df81fb2c475))

### [1.21.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.21.0...1.21.1) (2024-10-08)

## [1.22.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.21.0...1.22.0) (2024-10-21)


### Bug Fixes

* add Embedded to setCookiesMiddleware, and setHeadersFromCookies ([191a6b5](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/191a6b5225243be292d3ecf1d00df905175d2134))
### [1.21.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.21.0...1.21.1) (2024-10-08)

## [1.21.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.20.0...1.21.0) (2024-10-07)


### Features

* **PIGEON-5084:** added ld flag for pigeon api atlas ([5f8546e](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/5f8546ebecb8ea89e0dc24a93e143a515d90fe11))

## [1.20.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.19.3...1.20.0) (2024-07-25)


### Features

* **PIGEON-4978:** marvel web track url controlled by LD ([9a33034](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/9a33034cdff76dc8bf9228862438a0570aa5614e))
* **PIGEON-5056:** add Embedded to passthroughHeaders ([471b837](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/471b8373a5594deff5b09052ae23979f3d28f6f1))

### [1.19.3](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.19.2...1.19.3) (2024-05-30)

### [1.19.2](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.19.1...1.19.2) (2024-04-29)

### [1.19.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.19.0...1.19.1) (2024-04-29)

## [1.19.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.18.0...1.19.0) (2024-04-26)


### Features

* **PIGEON-4902:** add service, version to health check ([bb45f5b](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/bb45f5bbd823ca1112832bfb8755e866fb15d3e4))
* **PIGEON-4938:** add support for atlantis preview ([f322aa9](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/f322aa93443b2317b67be42c871ef6a6ca0f895a))
* **PIGEON-4938:** add support for atlantis preview ([a77cf69](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/a77cf69d70aee877e67d6f0f607e07c1532fa7d6))
* **PIGEON-4946:** analytics enhancement for tracking impressions of-offers ([bbcd448](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/bbcd448fc8ba60323c1ce2635921315a3f16070f))


### Bug Fixes

* **PIGEON-4722:** update marvel web track id endpoint to v4 ([4b7b554](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/4b7b554f5c063cf2bbe74056cd9b70a142649c30))

## [1.18.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.17.2...1.18.0) (2024-03-25)


### Features

* **PIGEON-4561:** enforce code coverage min in jest config ([9e03daf](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/9e03dafde94edef5eab693cddc040fd5ebfb5b4d))


### Bug Fixes

* **PIGEON-4914:** update ABM ODP French version - selectedText ([3b52a7e](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/3b52a7e6b2a012ffd89fbfa694c24a9d777dcc38))
* **PIGEON-4914:** update ABM ODP French version - selectedText ([e7d61b9](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/e7d61b9bfff1d531ed8330d5ab83b0ff82d9c09c))

### [1.17.2](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.17.1...1.17.2) (2024-02-20)

### [1.17.1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.17.0...1.17.1) (2024-02-14)


### Bug Fixes

* fix sonar issue ([99128cd](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/99128cdfe98bd4009d293a1d8045894ae00da84a))

## [1.17.0](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/compare/1.16.1...1.17.0) (2024-02-14)


### Features

* **PIGEON-4871:** update node version of build engine ([b833398](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/b833398638ffb77b728cbf61fdac62bbf3adc4db))


### Bug Fixes

* fixed sonar code smells ([831eeb1](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/831eeb1a6c079cdf1b8a3a0099f5e33a3b7e1262))
* **PIGEON-4834:** pass optional query as headers ([c77be97](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/c77be97a1d638f8c582bc0422ea2cd3c72195e12))
* **PIGEON-4887:** use const-common package to add csrf token middleware ([5148d93](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse/commit/5148d93526af44611ddf17d95dad31cedd550f8f))

### [1.16.1](https://bitbucket.agile.bns///compare/1.16.0...1.16.1) (2024-01-09)


### Bug Fixes

* bump package versions to resolve url-parse bd policy violation ([266740d](https://bitbucket.agile.bns///commit/266740dfb0c4f146c22850ae84c33e3264207edd))

## [1.16.0](https://bitbucket.agile.bns///compare/1.15.0...1.16.0) (2024-01-09)


### Bug Fixes

* **PIGEON-4736:** add NeoPreview ([ac59363](https://bitbucket.agile.bns///commit/ac593638274e110b671775db8c477922207019f7))
* pwr version upgrade , added language support for standing campaign preview status badge ([11b8ff0](https://bitbucket.agile.bns///commit/11b8ff020298d54d097923961c14d6ab6a515223))

## [1.15.0](https://bitbucket.agile.bns///compare/1.14.0...1.15.0) (2023-11-29)


### Bug Fixes

* **PIGEON-4838:** ccau add x-langauge for template ([e141cec](https://bitbucket.agile.bns///commit/e141cecb6e0636ff6a6207eec8cb0f1759433c31))
* **PIGEON-4838:** spanish error messages ([780b4f1](https://bitbucket.agile.bns///commit/780b4f1e87efc93bfc68c6e39676b1b9dede277e))
* **PIGEON-4838:** use different method to get url ccau ([351a484](https://bitbucket.agile.bns///commit/351a4840a43885de7a6b627d760bb95bec083361))
* **PIGEON-4769:** add warning for mismatch of country, language ([99e0fc1](https://bitbucket.agile.bns///commit/99e0fc166260d2c484bf7de11856b0466bc45a24))
* **PIGEON-4769:** fix query language ([fa64c31](https://bitbucket.agile.bns///commit/fa64c315262ab1900d6b2c95e5fb8afca6ef2297))


## [1.14.0](https://bitbucket.agile.bns///compare/1.13.7...1.14.0) (2023-11-20)


### Features

* **PIGEON-4770:** add channel param ([f8e412f](https://bitbucket.agile.bns///commit/f8e412f5c7df80d1fd91aaa9844e7b5646150cbf))
* **PIGEON-4770:** add check for channel is empty ([ce68393](https://bitbucket.agile.bns///commit/ce6839342d1b72a5863ab8341a866c3a05cc446c))
* **PIGEON-4770:** use channel query as header ([393f9cf](https://bitbucket.agile.bns///commit/393f9cf0bcace046e39fb811bd1cb5e143dcb4c2))


### Bug Fixes

* **PIGEON-4813:** fix click.name by passing data-link-page attr ([7f2b30e](https://bitbucket.agile.bns///commit/7f2b30eb82974f1b01e41bd60d6a119941a68e7c))

### [1.13.7](https://bitbucket.agile.bns///compare/1.13.6...1.13.7) (2023-10-23)

### [1.13.6](https://bitbucket.agile.bns///compare/1.13.5...1.13.6) (2023-10-16)


### Bug Fixes

* two page load events fired when navigating between details pages ([61e1f2f](https://bitbucket.agile.bns///commit/61e1f2fbdc017da0ceb97763cfe055c3e74d6cda))

### [1.13.5](https://bitbucket.agile.bns///compare/1.13.4...1.13.5) (2023-10-15)


### Bug Fixes

* web track id not sent in analytics event ([26f33c4](https://bitbucket.agile.bns///commit/26f33c4b5d31a9ab929662756953dea116c4c575))

### [1.13.4](https://bitbucket.agile.bns///compare/1.13.0...1.13.4) (2023-10-13)


### Features

* use marvel smartdns endpoints ([e0cbab2](https://bitbucket.agile.bns///commit/e0cbab280321f99e919a3bdbbf6796de7d38bb0d))

### [1.13.3](https://bitbucket.agile.bns///compare/1.13.0...1.13.3) (2023-10-13)


### Features

* use marvel smartdns endpoints ([e0cbab2](https://bitbucket.agile.bns///commit/e0cbab280321f99e919a3bdbbf6796de7d38bb0d))

### [1.13.2](https://bitbucket.agile.bns///compare/1.13.0...1.13.2) (2023-10-12)

## [1.13.0](https://bitbucket.agile.bns///compare/1.11.6...1.13.0) (2023-10-11)


### Features

* **ccau integration:** refactor campaigns headers construction logic BRZ-4664 ([7133fa8](https://bitbucket.agile.bns///commit/7133fa89d6d8769c8f937b0e1fcb756ac8958dd3))
* **ccau routes/ccau sso:** CCAU Integration PIGEON-4640 PIGEON-4639 ([a61189c](https://bitbucket.agile.bns///commit/a61189c7763168ca2074357d3d2bfe3def87c958))
* **ccau sso:** cleanup CCAU SSO integration with pigeon-web BRZ-4663 ([5949344](https://bitbucket.agile.bns///commit/5949344dfdf5ea735645107f8546601156b5d2b2))
* **PIGEON-4519:** update canvas package to v12 ([54f2cbc](https://bitbucket.agile.bns///commit/54f2cbc2c83b7f772c48bdc88d0d28705c58d514))
* **PIGEON-4638:** add height css for fixed footer ([361834b](https://bitbucket.agile.bns///commit/361834b721c1b0607977d4674fd2b55f66d34b9b))
* **PIGEON-4638:** map selectContents by mode ([41a5201](https://bitbucket.agile.bns///commit/41a5201dedcd863bbd515660259bd3fd3f8b09f5))
* **PIGEON-4638:** pass select_contents to pigeon-api ([ba79d2a](https://bitbucket.agile.bns///commit/ba79d2a50823125e90c60ac4cb57dbf79d5c985c))
* **PIGEON-4638:** remove duplicate ([9804e4c](https://bitbucket.agile.bns///commit/9804e4c7735bda2748f43c53a90bd495e0045bce))
* **PIGEON-4638:** trigger build ([3c82101](https://bitbucket.agile.bns///commit/3c821016cabb091df61dadb267f5ecb8864cae58))
* **PIGEON-4638:** trigger build ([365bf56](https://bitbucket.agile.bns///commit/365bf56208c84438748f40b8925478e869fea44d))
* **PIGEON-4638:** use CcauGenericTemplate ([0673c5e](https://bitbucket.agile.bns///commit/0673c5ef85900fba97b35dbdb667f155d21b9502))


### Bug Fixes

* merge release/1.12 with release/1.13 ([b3681fa](https://bitbucket.agile.bns///commit/b3681fa7607d90d358e6fe6b570f7b51aae71d73))

### [1.11.6](https://bitbucket.agile.bns///compare/1.11.5...1.11.6) (2023-08-24)

### [1.11.5](https://bitbucket.agile.bns///compare/1.11.4...1.11.5) (2023-08-21)

### [1.11.4](https://bitbucket.agile.bns///compare/1.11.3...1.11.4) (2023-08-21)

### [1.11.3](https://bitbucket.agile.bns///compare/1.11.2...1.11.3) (2023-08-21)

### [1.11.2](https://bitbucket.agile.bns///compare/1.11.1...1.11.2) (2023-08-18)

### [1.11.1](https://bitbucket.agile.bns///compare/1.11.0...1.11.1) (2023-06-26)

## [1.11.0](https://bitbucket.agile.bns///compare/1.10.2...1.11.0) (2023-06-12)

### [1.10.2](https://bitbucket.agile.bns///compare/1.10.1...1.10.2) (2023-04-17)


### Bug Fixes

* **PIGEON-4670:** dark mode not enabled ([b392051](https://bitbucket.agile.bns///commit/b392051dd3094005d96672a788ce87765f9d6ba9))

### [1.10.1](https://bitbucket.agile.bns///compare/1.9.9...1.10.1) (2023-04-12)


### Features

* **PIGEON-4509:** pentest lack of rate limiting ([e83647b](https://bitbucket.agile.bns///commit/e83647b81169ad9a6bc938c194cf41d4c83da4d4))


### Bug Fixes

* **PIGEON-4630:** add only allowed cookies to the window env ([ae27a35](https://bitbucket.agile.bns///commit/ae27a357be5fd5184ddbfec1e44e7c7fa1f4bfff))
* **PIGEON-4630:** decalre httpOnly explicitly to avoid forify ([b058195](https://bitbucket.agile.bns///commit/b058195d3e5147b735e38ed96972a94d57ed5eb3))
* **PIGEON-4630:** store request headers as window variable ([a4ace81](https://bitbucket.agile.bns///commit/a4ace814c94037dd002ca5df841dde4aaecf63ae))
* fix smell code in sonare ([6abe27e](https://bitbucket.agile.bns///commit/6abe27eb8474fb67a19e33b487277a6f7d632c51))
* fix smell code in sonare ([544bf0c](https://bitbucket.agile.bns///commit/544bf0c481aa88d8944e37ab41376437bd7a647d))
* increase the coverage for sonar ([de81233](https://bitbucket.agile.bns///commit/de812337f258bca4a1dec024343baa4474ec556a))
* increase the coverage for sonar ([17406e4](https://bitbucket.agile.bns///commit/17406e41cfb40433110d336f624793cf3712c113))
* increase the coverage for sonar ([b06eb1b](https://bitbucket.agile.bns///commit/b06eb1b779e76c07de3251c3c7c4689b09326532))
* revert sonar to skip id accp-dev file ([a04f8e2](https://bitbucket.agile.bns///commit/a04f8e2cdb0d40ed39b5b13276d39bbde2846f73))
* skip FORTIFY_SCAN on accp-dev ([40d6e47](https://bitbucket.agile.bns///commit/40d6e47e8f385136d66ba36571d1c03e0dc404de))
* **PIGEON-4045:** properly populate and log errors thrown from failed downstream calls ([81df799](https://bitbucket.agile.bns///commit/81df7999749d2394b939d923a25062d8e53c604f))
* **PIGEON-4484:** add sol and fullfillment variable to setEnvironmentVariables file ([54c2cca](https://bitbucket.agile.bns///commit/54c2cca43939abb68cbf1bcdb4da25ec37dc52a0))
* **PIGEON-4484:** add sol and fullfillment variable to webpack common file ([31bc0b2](https://bitbucket.agile.bns///commit/31bc0b272874c8d73ade72734b56858d86f1894f))
* **PIGEON-4484:** move SOL and FulfillableTarget endpoints to index ejs ([2517a51](https://bitbucket.agile.bns///commit/2517a51a0fa5c577e9715b68f4b1ad2d485c4db0))

## [1.10.0](https://bitbucket.agile.bns///compare/1.9.9...1.10.0) (2023-04-12)


### Features

* **PIGEON-4509:** pentest lack of rate limiting ([e83647b](https://bitbucket.agile.bns///commit/e83647b81169ad9a6bc938c194cf41d4c83da4d4))


### Bug Fixes

* **PIGEON-4630:** add only allowed cookies to the window env ([ae27a35](https://bitbucket.agile.bns///commit/ae27a357be5fd5184ddbfec1e44e7c7fa1f4bfff))
* **PIGEON-4630:** decalre httpOnly explicitly to avoid forify ([b058195](https://bitbucket.agile.bns///commit/b058195d3e5147b735e38ed96972a94d57ed5eb3))
* **PIGEON-4630:** store request headers as window variable ([a4ace81](https://bitbucket.agile.bns///commit/a4ace814c94037dd002ca5df841dde4aaecf63ae))
* fix smell code in sonare ([6abe27e](https://bitbucket.agile.bns///commit/6abe27eb8474fb67a19e33b487277a6f7d632c51))
* fix smell code in sonare ([544bf0c](https://bitbucket.agile.bns///commit/544bf0c481aa88d8944e37ab41376437bd7a647d))
* increase the coverage for sonar ([de81233](https://bitbucket.agile.bns///commit/de812337f258bca4a1dec024343baa4474ec556a))
* increase the coverage for sonar ([17406e4](https://bitbucket.agile.bns///commit/17406e41cfb40433110d336f624793cf3712c113))
* increase the coverage for sonar ([b06eb1b](https://bitbucket.agile.bns///commit/b06eb1b779e76c07de3251c3c7c4689b09326532))
* revert sonar to skip id accp-dev file ([a04f8e2](https://bitbucket.agile.bns///commit/a04f8e2cdb0d40ed39b5b13276d39bbde2846f73))
* skip FORTIFY_SCAN on accp-dev ([40d6e47](https://bitbucket.agile.bns///commit/40d6e47e8f385136d66ba36571d1c03e0dc404de))
* **PIGEON-4045:** properly populate and log errors thrown from failed downstream calls ([81df799](https://bitbucket.agile.bns///commit/81df7999749d2394b939d923a25062d8e53c604f))
* **PIGEON-4484:** add sol and fullfillment variable to setEnvironmentVariables file ([54c2cca](https://bitbucket.agile.bns///commit/54c2cca43939abb68cbf1bcdb4da25ec37dc52a0))
* **PIGEON-4484:** add sol and fullfillment variable to webpack common file ([31bc0b2](https://bitbucket.agile.bns///commit/31bc0b272874c8d73ade72734b56858d86f1894f))
* **PIGEON-4484:** move SOL and FulfillableTarget endpoints to index ejs ([2517a51](https://bitbucket.agile.bns///commit/2517a51a0fa5c577e9715b68f4b1ad2d485c4db0))

### [1.9.9](https://bitbucket.agile.bns///compare/1.9.3...1.9.9) (2023-03-03)

### [1.9.3](https://bitbucket.agile.bns///compare/1.9.2...1.9.3) (2023-03-03)


### Bug Fixes

* **PIGEON-4589:** allow both format for x-device-flags header ([3aed324](https://bitbucket.agile.bns///commit/3aed3246dcc4eea16e74b11585f73e3a85c4ccce))
* **PIGEON-4589:** allow both format for x-device-flags header ([137b558](https://bitbucket.agile.bns///commit/137b558108aff52f787df5450f6f5bc2064f77b8))

### [1.9.2](https://bitbucket.agile.bns///compare/1.9.1...1.9.2) (2023-01-16)

## [1.9.0](https://bitbucket.agile.bns///compare/v1.3.0...v1.9.0) (2022-12-28)


### Features

* support pwr itrade details page dark mode for both platforms ([f123b81](https://bitbucket.agile.bns///commit/f123b81304851d77b9600d2703c8dc15457b4623))
* **PIGEON-3344, PIGEON-3446:** harmony preview components, remove frutiger font ([634bba1](https://bitbucket.agile.bns///commit/634bba14d7e76f64406e0f5deb6cd3e7f850252b))
* **PIGEON-3424:** dark mode toggle via req header or query ([bd7a6f5](https://bitbucket.agile.bns///commit/bd7a6f59430741dc39588dc2dd02ef9b9d8a7b8c))
* **PIGEON-3424:** dark mode toggle via req header or query ([42915e1](https://bitbucket.agile.bns///commit/42915e1b237591438e3f8e57b22f77d0485d904f))
* **PIGEON-3557:** allow independent build id ranges for parallel releases ([a5b312d](https://bitbucket.agile.bns///commit/a5b312d896e03576a026dc2d51cee9d1003009ef))
* **PIGEON-3557:** use dev team version strategy to better support git flow on main branch with accp ([f366480](https://bitbucket.agile.bns///commit/f36648040b69496ab4e7ab1188ae0d1f840d03c9))
* **PIGEON-3584:** use csp1 script policies for better safari support ([8f8a4b8](https://bitbucket.agile.bns///commit/8f8a4b823dc1309d76bd3c62b009960ee4a4d9ef))
* **PIGEON-3658:** pass incoming header nova-device-secure to pigeon api ([f6e7f73](https://bitbucket.agile.bns///commit/f6e7f73e7f65204a5746988220e982e132b3bc3e))
* **PIGEON-3658:** remove unused header from the apis ([cbe84ec](https://bitbucket.agile.bns///commit/cbe84ec337d2abfea5754fdd90f03a968534fac4))
* **PIGEON-3881:** bump pwr to enable auto rendering of product sheets ([72235f5](https://bitbucket.agile.bns///commit/72235f5836cc1c451e1de0aa95d893b71145bb6e))
* **PIGEON-4013:** bump pwr version ([8f9c73f](https://bitbucket.agile.bns///commit/8f9c73fad3294f13eafeb3c0e235537ab6e3d202))
* **PIGEON-4013:** support 3rd cta (continueLink) ([082ed8d](https://bitbucket.agile.bns///commit/082ed8d4cc514ad3fd972159f1bbb5d74bae2773))
* **PIGEON-4030:** convert x application BCCY to N1 or N2 ([d901444](https://bitbucket.agile.bns///commit/d90144400a9aa6b49147c3d0a325a31d584a87c6))
* **PIGEON-4052:** add analytics event to dismiss functionality ([aaad244](https://bitbucket.agile.bns///commit/aaad244537c91c8f73d77fbbda198d70236cfd3f))
* **PIGEON-4217:** explicit jenkins node version to workaround invalid pipeline defaults ([11b71fc](https://bitbucket.agile.bns///commit/11b71fcc21132d1b289ade21f91937b0dc177027))
* bum pwr version ([ffb3c0a](https://bitbucket.agile.bns///commit/ffb3c0a7e21cd4a13832518c0bd436ad2a8693e3))
* bump pwr with non ABM fixes ([ad34f41](https://bitbucket.agile.bns///commit/ad34f41ad447eab319704e486290b2d375bfe683))
* constellation pre-approved flow integration ([b4086c2](https://bitbucket.agile.bns///commit/b4086c2a5f7fa06a5347f3c6c3805a12bdbf5f58))
* fall back to sso ot if auth header ot doesnt exist ([8948205](https://bitbucket.agile.bns///commit/894820546294dbd49735e26e7816641d165d2be0))
* update release 1.7 branch with the main branch ([9e65930](https://bitbucket.agile.bns///commit/9e65930543ecc0fb0ba637b06c841bcae695a84b))


### Bug Fixes

* starburst ios maintain black background when dragging webview up or down ([4339ca3](https://bitbucket.agile.bns///commit/4339ca3a771161d63cfe3f130cc343d4b7e96dae))
* **PIFEON-4093:** add more test cases ([147525c](https://bitbucket.agile.bns///commit/147525cb17ff065fe3a48554db078ebaaa437d57))
* **PIFEON-4093:** add more test cases ([4cbbcad](https://bitbucket.agile.bns///commit/4cbbcadf299d1c17faef040af8d6468b12e10686))
* **PIGEON-3472:** fix lodash vulnerability blackduck ([3ce80b8](https://bitbucket.agile.bns///commit/3ce80b85fda0baab0896310d52b9c21713299399))
* **PIGEON-3524:** for TRA issue, add secure flag and max age to all cookies ([54a2459](https://bitbucket.agile.bns///commit/54a245958683b14b2a04817a625c2b5fe603f1e3))
* **PIGEON-3539:** restore query param access to fetch offer request ([5073a88](https://bitbucket.agile.bns///commit/5073a88fdc6fa458c2839fb672fbde276779fb6a))
* **PIGEON-3559:** fix acceptence tests ([6cc4582](https://bitbucket.agile.bns///commit/6cc458234568df3eeb5b855ec97f845f30de8d0b))
* **PIGEON-3576:** include akyc ux fix from pwr ([a39abd1](https://bitbucket.agile.bns///commit/a39abd104b1f4f7f2481a15d407108fab3a1eaff))
* **PIGEON-3590:** pw dark mode styles and images ([1863052](https://bitbucket.agile.bns///commit/1863052a349320a09b860918c10f43a763cacef9))
* **PIGEON-3591:** pass xOriginatingApplCode to pigeon-api ([2e181f9](https://bitbucket.agile.bns///commit/2e181f9187edeba2e75d1967cd1757226ee55aa8))
* **PIGEON-3635:** fix eexp link inline display ([3397b7f](https://bitbucket.agile.bns///commit/3397b7fb3520f1f41740a6da3e75439d1e5ad4e9))
* **PIGEON-3637:** restore to using nova ot due to phoenix sso ot missing client metadata ([d7beda9](https://bitbucket.agile.bns///commit/d7beda99c039821c98c78547c1ab9fb0ecb83eb7))
* **PIGEON-3644:** add canvas dark icons for standing campaign previews ([b83d13b](https://bitbucket.agile.bns///commit/b83d13b89e2ea443208c08ce1a4c8ec05cf288d5))
* **PIGEON-3644:** dark theme support for contentful selectable icons ([8a74f8c](https://bitbucket.agile.bns///commit/8a74f8c80cbefd5d64d4a6adc1c92e1462d8da6a))
* **PIGEON-3644:** resolve canvas icons interfering with svg badges ([226f9a9](https://bitbucket.agile.bns///commit/226f9a93ba8bc4620d9201f5892fa74c2dfd14df))
* **PIGEON-3665:** pigeon web pwr bump ([782cf04](https://bitbucket.agile.bns///commit/782cf044ec0205c0eb44951d01174829b6a153ad))
* **PIGEON-3671:** ios friendly onclick handler for links ([26575ef](https://bitbucket.agile.bns///commit/26575ef3d1b7a2ed7d9cb76fcaf12e94c8c8f965))
* **PIGEON-3671:** remove unnecessary side effect ([c4e96cd](https://bitbucket.agile.bns///commit/c4e96cd3bfdd167f9331bf09a2809158380f0792))
* **PIGEON-3674:** pigeon web pwr bump ([e6d8dd2](https://bitbucket.agile.bns///commit/e6d8dd2f971c3a11944232bd91012b85056318ee))
* **PIGEON-3676:** ios nova wrong scroll height ([ee6248f](https://bitbucket.agile.bns///commit/ee6248f7923b8b339ad78530d2d717b08363cd46))
* **PIGEON-3730:** update heimdall library to fix vulnerability NRLW-6627 ([a86e518](https://bitbucket.agile.bns///commit/a86e51840b77cff1d81e7f113e9e464a3090d5c5))
* **PIGEON-3745:** ios dark mode reveals light edges ([be06f22](https://bitbucket.agile.bns///commit/be06f22e304735d57edbce4b1a4920e542b216a5))
* **PIGEON-3904:** update ejs version to 3.1.8 ([abbba55](https://bitbucket.agile.bns///commit/abbba55444813efbe4c70c9e6c3de5ef2ec9e409))
* **PIGEON-3904:** update webpack-bundle-analyzer to 4.3.0 ([e3465a0](https://bitbucket.agile.bns///commit/e3465a01f0ed6147e1c3bcf0cb7bf2cd967e2350))
* **PIGEON-3912:** bump pwr to fix long url ([33163e5](https://bitbucket.agile.bns///commit/33163e5a42411a005144143967257c59f18b5552))
* **PIGEON-3987:** pwr bump ([38db435](https://bitbucket.agile.bns///commit/38db4351f9b869d6d8ccd0938aec5b639cbec0b1))
* **PIGEON-4093:** fix missing platform for android ([067d520](https://bitbucket.agile.bns///commit/067d52032f2fab09c747b77e6bd0ba25905537be))
* **PIGEON-4093:** fix missing platform for android ([fba4130](https://bitbucket.agile.bns///commit/fba413024634358aaa573c3fef178514fcc27cca))
* **PIGEON-4236:** fix black duck policy violation for undefsafe-2.0.3 ([06d44aa](https://bitbucket.agile.bns///commit/06d44aa72e92f713af70f169a6300779a022bc9e))
* add defaultCookieOpts to res.cookie ([9ac6561](https://bitbucket.agile.bns///commit/9ac6561d1dd37ad95fd0a481da884f17f63752c3))
* add toLowerCase() to the env logic ([f5c7782](https://bitbucket.agile.bns///commit/f5c7782d3ea2a193a431200485d041d551a91660))
* Adding back httpOnly flag ([62ee1f1](https://bitbucket.agile.bns///commit/62ee1f1bd78815f10d090fdd50169530ada062f3))
* blackduck low severity fixes ([b8409e0](https://bitbucket.agile.bns///commit/b8409e0039f69c1143c9e224fc3600a0fe86787a))
* bump pwr ([290da7d](https://bitbucket.agile.bns///commit/290da7d9a31431909837fd17437222cc6133d145))
* clean up the logic ([485fd0f](https://bitbucket.agile.bns///commit/485fd0fd502831b7c99a7b7a3593b7c98ab6ddcc))
* cleanup ([2021030](https://bitbucket.agile.bns///commit/2021030d88c56719de2d4a8acad4313335491d3f))
* fix broken abm images as a result of CSP ([9597d5e](https://bitbucket.agile.bns///commit/9597d5e8a420ddb6f37662e9cfc30204fa8f08d9))
* fix typo ([b1a2aa9](https://bitbucket.agile.bns///commit/b1a2aa93a941a6dac02afe59f3ee18ef30834fbb))
* fortify issue - missing httpOnly and secure flags for cookies ([c0875a6](https://bitbucket.agile.bns///commit/c0875a62556bdaf5fb29e36812ba80a9cfc76878))
* fortify issue test ([0b8d737](https://bitbucket.agile.bns///commit/0b8d737ade266b71422214a1ca9baebc3d4b0294))
* icreate minor version in case of build out of the main branch ([3fb4ff4](https://bitbucket.agile.bns///commit/3fb4ff49ac9fb423bf6e617f966446403cf14ad3))
* interim fix to remove ld dependency for token call for ios ([1d95237](https://bitbucket.agile.bns///commit/1d952370ecc6fc5d25063cf933d25ed24ceef0e7))
* move ot call to seperate code block to ensure it is called independently of content ([07617fa](https://bitbucket.agile.bns///commit/07617facc72ddc16ac4619a983710ce4652f4f55))
* pass device query as params ([70b4d8d](https://bitbucket.agile.bns///commit/70b4d8dcbaec64408a180b31e32fdd4b6911118e))
* pipeline node versions ([6cff3e9](https://bitbucket.agile.bns///commit/6cff3e9f9c92caabd1bd3b4744d1ce1a81a966ca))
* refactor code ([56dc18e](https://bitbucket.agile.bns///commit/56dc18e7a1342b397f2dc796ec4f85b4bd382fdf))
* remove --updateSnapsot ([ad9e63f](https://bitbucket.agile.bns///commit/ad9e63f2795e3e99a396838f35e12cdec4d5ad2f))
* remove `preferred-environment` header in production environment ([05249d5](https://bitbucket.agile.bns///commit/05249d5e9d16f4907080b5e8f08fde2bf1623423))
* remove httpOnly when setting language cookie ([e3deacb](https://bitbucket.agile.bns///commit/e3deacbaa2de2c9e1501f9a48a549a942ceba7f5))
* remove optional chaining ([4d8e1ea](https://bitbucket.agile.bns///commit/4d8e1ea435c2f086d8829a94b2da21373e492390))
* skip ot call if no offers are returned ([ab5b7ca](https://bitbucket.agile.bns///commit/ab5b7ca5e6e2abc4cd9dd2f8ca774efcd8b2acc7))
* upgrade webpack-dev-server package ([59da96a](https://bitbucket.agile.bns///commit/59da96a486b72cada097c6d945c7baa99e8ffe5e))

## [1.8.0](https://bitbucket.agile.bns///compare/1.7.0...1.8.0) (2022-10-13)


### Features

* **PIGEON-4052:** add analytics event to dismiss functionality ([aaad244](https://bitbucket.agile.bns///commit/aaad244537c91c8f73d77fbbda198d70236cfd3f))
* bum pwr version ([ffb3c0a](https://bitbucket.agile.bns///commit/ffb3c0a7e21cd4a13832518c0bd436ad2a8693e3))
* update release 1.7 branch with the main branch ([9e65930](https://bitbucket.agile.bns///commit/9e65930543ecc0fb0ba637b06c841bcae695a84b))
* **PIGEON-4013:** bump pwr version ([8f9c73f](https://bitbucket.agile.bns///commit/8f9c73fad3294f13eafeb3c0e235537ab6e3d202))
* **PIGEON-4013:** support 3rd cta (continueLink) ([082ed8d](https://bitbucket.agile.bns///commit/082ed8d4cc514ad3fd972159f1bbb5d74bae2773))


### Bug Fixes

* **PIFEON-4093:** add more test cases ([147525c](https://bitbucket.agile.bns///commit/147525cb17ff065fe3a48554db078ebaaa437d57))
* **PIFEON-4093:** add more test cases ([4cbbcad](https://bitbucket.agile.bns///commit/4cbbcadf299d1c17faef040af8d6468b12e10686))
* **PIGEON-3671:** ios friendly onclick handler for links ([26575ef](https://bitbucket.agile.bns///commit/26575ef3d1b7a2ed7d9cb76fcaf12e94c8c8f965))
* **PIGEON-3671:** remove unnecessary side effect ([c4e96cd](https://bitbucket.agile.bns///commit/c4e96cd3bfdd167f9331bf09a2809158380f0792))
* **PIGEON-4093:** fix missing platform for android ([067d520](https://bitbucket.agile.bns///commit/067d52032f2fab09c747b77e6bd0ba25905537be))
* **PIGEON-4093:** fix missing platform for android ([fba4130](https://bitbucket.agile.bns///commit/fba413024634358aaa573c3fef178514fcc27cca))

## [1.7.0](https://bitbucket.agile.bns///compare/v1.3.0...v1.7.0) (2022-08-03)


### Features

* **PIGEON-4030:** convert x application BCCY to N1 or N2 ([d901444](https://bitbucket.agile.bns///commit/d90144400a9aa6b49147c3d0a325a31d584a87c6))
* bump pwr with non ABM fixes ([ad34f41](https://bitbucket.agile.bns///commit/ad34f41ad447eab319704e486290b2d375bfe683))
* constellation pre-approved flow integration ([b4086c2](https://bitbucket.agile.bns///commit/b4086c2a5f7fa06a5347f3c6c3805a12bdbf5f58))
* **PIGEON-3344, PIGEON-3446:** harmony preview components, remove frutiger font ([634bba1](https://bitbucket.agile.bns///commit/634bba14d7e76f64406e0f5deb6cd3e7f850252b))
* **PIGEON-3424:** dark mode toggle via req header or query ([bd7a6f5](https://bitbucket.agile.bns///commit/bd7a6f59430741dc39588dc2dd02ef9b9d8a7b8c))
* **PIGEON-3424:** dark mode toggle via req header or query ([42915e1](https://bitbucket.agile.bns///commit/42915e1b237591438e3f8e57b22f77d0485d904f))
* **PIGEON-3557:** allow independent build id ranges for parallel releases ([a5b312d](https://bitbucket.agile.bns///commit/a5b312d896e03576a026dc2d51cee9d1003009ef))
* **PIGEON-3557:** use dev team version strategy to better support git flow on main branch with accp ([f366480](https://bitbucket.agile.bns///commit/f36648040b69496ab4e7ab1188ae0d1f840d03c9))
* **PIGEON-3584:** use csp1 script policies for better safari support ([8f8a4b8](https://bitbucket.agile.bns///commit/8f8a4b823dc1309d76bd3c62b009960ee4a4d9ef))
* **PIGEON-3658:** pass incoming header nova-device-secure to pigeon api ([f6e7f73](https://bitbucket.agile.bns///commit/f6e7f73e7f65204a5746988220e982e132b3bc3e))
* **PIGEON-3658:** remove unused header from the apis ([cbe84ec](https://bitbucket.agile.bns///commit/cbe84ec337d2abfea5754fdd90f03a968534fac4))
* **PIGEON-3881:** bump pwr to enable auto rendering of product sheets ([72235f5](https://bitbucket.agile.bns///commit/72235f5836cc1c451e1de0aa95d893b71145bb6e))
* fall back to sso ot if auth header ot doesnt exist ([8948205](https://bitbucket.agile.bns///commit/894820546294dbd49735e26e7816641d165d2be0))


### Bug Fixes

* icreate minor version in case of build out of the main branch ([3fb4ff4](https://bitbucket.agile.bns///commit/3fb4ff49ac9fb423bf6e617f966446403cf14ad3))
* **PIGEON-3676:** ios nova wrong scroll height ([ee6248f](https://bitbucket.agile.bns///commit/ee6248f7923b8b339ad78530d2d717b08363cd46))
* remove --updateSnapsot ([ad9e63f](https://bitbucket.agile.bns///commit/ad9e63f2795e3e99a396838f35e12cdec4d5ad2f))
* upgrade webpack-dev-server package ([59da96a](https://bitbucket.agile.bns///commit/59da96a486b72cada097c6d945c7baa99e8ffe5e))
* **PIGEON-3472:** fix lodash vulnerability blackduck ([3ce80b8](https://bitbucket.agile.bns///commit/3ce80b85fda0baab0896310d52b9c21713299399))
* **PIGEON-3524:** for TRA issue, add secure flag and max age to all cookies ([54a2459](https://bitbucket.agile.bns///commit/54a245958683b14b2a04817a625c2b5fe603f1e3))
* **PIGEON-3539:** restore query param access to fetch offer request ([5073a88](https://bitbucket.agile.bns///commit/5073a88fdc6fa458c2839fb672fbde276779fb6a))
* **PIGEON-3559:** fix acceptence tests ([6cc4582](https://bitbucket.agile.bns///commit/6cc458234568df3eeb5b855ec97f845f30de8d0b))
* **PIGEON-3576:** include akyc ux fix from pwr ([a39abd1](https://bitbucket.agile.bns///commit/a39abd104b1f4f7f2481a15d407108fab3a1eaff))
* **PIGEON-3590:** pw dark mode styles and images ([1863052](https://bitbucket.agile.bns///commit/1863052a349320a09b860918c10f43a763cacef9))
* **PIGEON-3591:** pass xOriginatingApplCode to pigeon-api ([2e181f9](https://bitbucket.agile.bns///commit/2e181f9187edeba2e75d1967cd1757226ee55aa8))
* **PIGEON-3635:** fix eexp link inline display ([3397b7f](https://bitbucket.agile.bns///commit/3397b7fb3520f1f41740a6da3e75439d1e5ad4e9))
* **PIGEON-3637:** restore to using nova ot due to phoenix sso ot missing client metadata ([d7beda9](https://bitbucket.agile.bns///commit/d7beda99c039821c98c78547c1ab9fb0ecb83eb7))
* **PIGEON-3644:** add canvas dark icons for standing campaign previews ([b83d13b](https://bitbucket.agile.bns///commit/b83d13b89e2ea443208c08ce1a4c8ec05cf288d5))
* **PIGEON-3644:** dark theme support for contentful selectable icons ([8a74f8c](https://bitbucket.agile.bns///commit/8a74f8c80cbefd5d64d4a6adc1c92e1462d8da6a))
* **PIGEON-3644:** resolve canvas icons interfering with svg badges ([226f9a9](https://bitbucket.agile.bns///commit/226f9a93ba8bc4620d9201f5892fa74c2dfd14df))
* **PIGEON-3665:** pigeon web pwr bump ([782cf04](https://bitbucket.agile.bns///commit/782cf044ec0205c0eb44951d01174829b6a153ad))
* **PIGEON-3674:** pigeon web pwr bump ([e6d8dd2](https://bitbucket.agile.bns///commit/e6d8dd2f971c3a11944232bd91012b85056318ee))
* **PIGEON-3730:** update heimdall library to fix vulnerability NRLW-6627 ([a86e518](https://bitbucket.agile.bns///commit/a86e51840b77cff1d81e7f113e9e464a3090d5c5))
* **PIGEON-3745:** ios dark mode reveals light edges ([be06f22](https://bitbucket.agile.bns///commit/be06f22e304735d57edbce4b1a4920e542b216a5))
* **PIGEON-3904:** update ejs version to 3.1.8 ([abbba55](https://bitbucket.agile.bns///commit/abbba55444813efbe4c70c9e6c3de5ef2ec9e409))
* **PIGEON-3904:** update webpack-bundle-analyzer to 4.3.0 ([e3465a0](https://bitbucket.agile.bns///commit/e3465a01f0ed6147e1c3bcf0cb7bf2cd967e2350))
* **PIGEON-3912:** bump pwr to fix long url ([33163e5](https://bitbucket.agile.bns///commit/33163e5a42411a005144143967257c59f18b5552))
* **PIGEON-3987:** pwr bump ([38db435](https://bitbucket.agile.bns///commit/38db4351f9b869d6d8ccd0938aec5b639cbec0b1))
* add defaultCookieOpts to res.cookie ([9ac6561](https://bitbucket.agile.bns///commit/9ac6561d1dd37ad95fd0a481da884f17f63752c3))
* add toLowerCase() to the env logic ([f5c7782](https://bitbucket.agile.bns///commit/f5c7782d3ea2a193a431200485d041d551a91660))
* Adding back httpOnly flag ([62ee1f1](https://bitbucket.agile.bns///commit/62ee1f1bd78815f10d090fdd50169530ada062f3))
* blackduck low severity fixes ([b8409e0](https://bitbucket.agile.bns///commit/b8409e0039f69c1143c9e224fc3600a0fe86787a))
* bump pwr ([290da7d](https://bitbucket.agile.bns///commit/290da7d9a31431909837fd17437222cc6133d145))
* clean up the logic ([485fd0f](https://bitbucket.agile.bns///commit/485fd0fd502831b7c99a7b7a3593b7c98ab6ddcc))
* cleanup ([2021030](https://bitbucket.agile.bns///commit/2021030d88c56719de2d4a8acad4313335491d3f))
* fix broken abm images as a result of CSP ([9597d5e](https://bitbucket.agile.bns///commit/9597d5e8a420ddb6f37662e9cfc30204fa8f08d9))
* fix typo ([b1a2aa9](https://bitbucket.agile.bns///commit/b1a2aa93a941a6dac02afe59f3ee18ef30834fbb))
* fortify issue - missing httpOnly and secure flags for cookies ([c0875a6](https://bitbucket.agile.bns///commit/c0875a62556bdaf5fb29e36812ba80a9cfc76878))
* fortify issue test ([0b8d737](https://bitbucket.agile.bns///commit/0b8d737ade266b71422214a1ca9baebc3d4b0294))
* interim fix to remove ld dependency for token call for ios ([1d95237](https://bitbucket.agile.bns///commit/1d952370ecc6fc5d25063cf933d25ed24ceef0e7))
* move ot call to seperate code block to ensure it is called independently of content ([07617fa](https://bitbucket.agile.bns///commit/07617facc72ddc16ac4619a983710ce4652f4f55))
* pass device query as params ([70b4d8d](https://bitbucket.agile.bns///commit/70b4d8dcbaec64408a180b31e32fdd4b6911118e))
* pipeline node versions ([6cff3e9](https://bitbucket.agile.bns///commit/6cff3e9f9c92caabd1bd3b4744d1ce1a81a966ca))
* refactor code ([56dc18e](https://bitbucket.agile.bns///commit/56dc18e7a1342b397f2dc796ec4f85b4bd382fdf))
* remove `preferred-environment` header in production environment ([05249d5](https://bitbucket.agile.bns///commit/05249d5e9d16f4907080b5e8f08fde2bf1623423))
* remove httpOnly when setting language cookie ([e3deacb](https://bitbucket.agile.bns///commit/e3deacbaa2de2c9e1501f9a48a549a942ceba7f5))
* remove optional chaining ([4d8e1ea](https://bitbucket.agile.bns///commit/4d8e1ea435c2f086d8829a94b2da21373e492390))
* skip ot call if no offers are returned ([ab5b7ca](https://bitbucket.agile.bns///commit/ab5b7ca5e6e2abc4cd9dd2f8ca774efcd8b2acc7))

### [1.5.1](https://bitbucket.agile.bns///compare/1.5.0...1.5.1) (2022-07-08)


### Features

* constellation pre-approved flow integration ([b4086c2](https://bitbucket.agile.bns///commit/b4086c2a5f7fa06a5347f3c6c3805a12bdbf5f58))

## [1.5.0](https://bitbucket.agile.bns///compare/1.4.23...1.5.0) (2022-06-20)


### Features

* bump pwr with non ABM fixes ([ad34f41](https://bitbucket.agile.bns///commit/ad34f41ad447eab319704e486290b2d375bfe683))
* **PIGEON-3557:** allow independent build id ranges for parallel releases ([a5b312d](https://bitbucket.agile.bns///commit/a5b312d896e03576a026dc2d51cee9d1003009ef))
* **PIGEON-3881:** bump pwr to enable auto rendering of product sheets ([72235f5](https://bitbucket.agile.bns///commit/72235f5836cc1c451e1de0aa95d893b71145bb6e))


### Bug Fixes

* upgrade webpack-dev-server package ([59da96a](https://bitbucket.agile.bns///commit/59da96a486b72cada097c6d945c7baa99e8ffe5e))
* **PIGEON-3745:** ios dark mode reveals light edges ([be06f22](https://bitbucket.agile.bns///commit/be06f22e304735d57edbce4b1a4920e542b216a5))
* **PIGEON-3904:** update ejs version to 3.1.8 ([abbba55](https://bitbucket.agile.bns///commit/abbba55444813efbe4c70c9e6c3de5ef2ec9e409))
* **PIGEON-3904:** update webpack-bundle-analyzer to 4.3.0 ([e3465a0](https://bitbucket.agile.bns///commit/e3465a01f0ed6147e1c3bcf0cb7bf2cd967e2350))
* **PIGEON-3912:** bump pwr to fix long url ([33163e5](https://bitbucket.agile.bns///commit/33163e5a42411a005144143967257c59f18b5552))
* **PIGEON-3987:** pwr bump ([38db435](https://bitbucket.agile.bns///commit/38db4351f9b869d6d8ccd0938aec5b639cbec0b1))

### [1.4.15](https://bitbucket.agile.bns///compare/1.4.7...1.4.1) (2022-02-24)


### Bug Fixes

* **PIGEON-3730:** update heimdall library to fix vulnerability NRLW-6627 ([3737eb7](https://bitbucket.agile.bns///commit/3737eb779593edc1bb1413d9f519b50fa711131d))

## [1.4.0](https://bitbucket.agile.bns///compare/1.3.50...1.4.0) (2022-01-19)


### Features

* fall back to sso ot if auth header ot doesnt exist ([8948205](https://bitbucket.agile.bns///commit/894820546294dbd49735e26e7816641d165d2be0))
* **PIGEON-3424:** dark mode toggle via req header or query ([bd7a6f5](https://bitbucket.agile.bns///commit/bd7a6f59430741dc39588dc2dd02ef9b9d8a7b8c))
* **PIGEON-3424:** dark mode toggle via req header or query ([42915e1](https://bitbucket.agile.bns///commit/42915e1b237591438e3f8e57b22f77d0485d904f))
* **PIGEON-3584:** use csp1 script policies for better safari support ([8f8a4b8](https://bitbucket.agile.bns///commit/8f8a4b823dc1309d76bd3c62b009960ee4a4d9ef))


### Bug Fixes

* **PIGEON-3472:** fix lodash vulnerability blackduck ([3ce80b8](https://bitbucket.agile.bns///commit/3ce80b85fda0baab0896310d52b9c21713299399))
* **PIGEON-3576:** include akyc ux fix from pwr ([a39abd1](https://bitbucket.agile.bns///commit/a39abd104b1f4f7f2481a15d407108fab3a1eaff))
* **PIGEON-3590:** pw dark mode styles and images ([1863052](https://bitbucket.agile.bns///commit/1863052a349320a09b860918c10f43a763cacef9))
* **PIGEON-3591:** pass xOriginatingApplCode to pigeon-api ([2e181f9](https://bitbucket.agile.bns///commit/2e181f9187edeba2e75d1967cd1757226ee55aa8))
* **PIGEON-3637:** restore to using nova ot due to phoenix sso ot missing client metadata ([d7beda9](https://bitbucket.agile.bns///commit/d7beda99c039821c98c78547c1ab9fb0ecb83eb7))
* **PIGEON-3644:** dark theme support for contentful selectable icons ([8a74f8c](https://bitbucket.agile.bns///commit/8a74f8c80cbefd5d64d4a6adc1c92e1462d8da6a))

## [1.3.0](https://bitbucket.agile.bns///compare/v1.2.6...v1.3.0) (2021-11-11)


### Features

* null ([b1e9319](https://bitbucket.agile.bns///commit/b1e931915f12e7ce37f701c8182433d12da95339))
* **PIGEON-2941, PIGEON-2655:** migrate to new canvas - sept ([e3ab5d2](https://bitbucket.agile.bns///commit/e3ab5d2504a9e73c221669f39b65f918e5e0fefa))
* **PIGEON-3237:** bump pwr version ([e4d54d3](https://bitbucket.agile.bns///commit/e4d54d336241ce002cf128a3fb36a3d3802d9d0f))
* **PIGEON-3237:** implement snooze via generic disposition submit ([2837939](https://bitbucket.agile.bns///commit/2837939c0e27553fe1563c55e91485cdf7eb31ee))
* **PIGEON-3304:** amlkyc changes in PW ([8350dd2](https://bitbucket.agile.bns///commit/8350dd28bf7eda9131ed09d39151e70707006234))
* **PIGEON-3304:** bump pwr and regen lock file ([54a9c9e](https://bitbucket.agile.bns///commit/54a9c9e6280e889f296aa75ac4535b2f4b64e2d8))
* **PIGEON-3316:** forward headers required for pref mgt ([5d780b0](https://bitbucket.agile.bns///commit/5d780b0cdea72603f990973275f49ab17f370c7f))
* **PIGEON-3316:** forward headers required for pref mgt ([28d6722](https://bitbucket.agile.bns///commit/28d67221ac88f22ebc49c55bac3b651940a950eb))
* **PIGEON-3351:** add content security policies ([85aadc3](https://bitbucket.agile.bns///commit/85aadc3c7b829037203d921760472656fd97235c))
* **PIGEON-3393:** use ld flag to switch between old opaque token auth and oauth2 sso auth ([344347e](https://bitbucket.agile.bns///commit/344347e6f3d23676bf0a7273af8f933614446fa6))
* **PIGEON-3465:** upgrade to docker based pipelines and run BD as part of pipeline ([2db3b40](https://bitbucket.agile.bns///commit/2db3b4051734b6d31a6854ae86c35c8b080eb665))
* TDSv2 compatible changes in PW ([dce2ffa](https://bitbucket.agile.bns///commit/dce2ffab14a9a07874446f266644f8aa62774dc3))
* TDSv2 compatible changes in PW ([2d09caa](https://bitbucket.agile.bns///commit/2d09caa5f0b60231be8553bf2d2f3d7943df0645))


### Bug Fixes

* [PIGEON-3442] Added 404 response for missing assets ([8bb3f7d](https://bitbucket.agile.bns///commit/8bb3f7d0a4680c45a443e2707f4f1bc2e0ee7652))
* [PIGEON-3469] Changed secure attributes to true for cookie ([cedf787](https://bitbucket.agile.bns///commit/cedf787f953f8eba8b9b7e5fffa11c540db494d7))
* passing spandid and traceid to pigeon service ([c109eea](https://bitbucket.agile.bns///commit/c109eeae6dc1b28cdf06fbb5f127fe51e45042e4))
* pwr bump for legal footnotes fix ([c0459ff](https://bitbucket.agile.bns///commit/c0459ff00dbe0720a0627d29bdd0a5db03124abb))
* snapshot test update ([7ea238a](https://bitbucket.agile.bns///commit/7ea238a30d1f4063f13b8b239954669a9b6a7b63))
* **PIGEON-3237:** fix field casing bug ([17b12a3](https://bitbucket.agile.bns///commit/17b12a31dca5dabc49259f099c8b609c5aab8247))
* **PIGEON-3237:** kyc snooze enhancements ([e2b99e4](https://bitbucket.agile.bns///commit/e2b99e4b09e827ac4841839be0bdf33f0012dd29))
* **PIGEON-3295:** bump pwr version to include fix for list align ux ([5fc61b9](https://bitbucket.agile.bns///commit/5fc61b9fbc0a7e84a5045e937a2b90caa381e91b))
* **PIGEON-3322:** fix spelling ([5e39160](https://bitbucket.agile.bns///commit/5e39160e0fee30d8586f9dc5a4adb05ffdd25d2b))
* **PIGEON-3322:** fix spelling ([5fef65f](https://bitbucket.agile.bns///commit/5fef65f1f180c9deb8a0072b7948d511c0ff0c6f))
* **PIGEON-3375:** fix set-value, color-string, redis vulnerabilities ([c6bba58](https://bitbucket.agile.bns///commit/c6bba583b952bbd57501733ba8248383ee5bfe52))
* **PIGEON-3423:** add required headers to the cookie missed during sso ([ae4cb3c](https://bitbucket.agile.bns///commit/ae4cb3c8ce62bf6a1dfb00905bbc40cf1a63682f))
* **PIGEON-3423:** adding x-channel-id and x-application to the cookie and header ([76b6d9e](https://bitbucket.agile.bns///commit/76b6d9e6e839251a30719412b1e4b63c80cd8674))
* **PIGEON-3423:** cleanup ([c4a2aef](https://bitbucket.agile.bns///commit/c4a2aef27a118798eb5a657f1c9e8c20919eeb4a))
* [PIGEON-3387] removed need for a particular content structure to fetch opaque token for eexperience ctas ([3c511e5](https://bitbucket.agile.bns///commit/3c511e508ca16ecae98c99024ed9eb0a8f0214fa))
* [PIGEON-3933] Added LD Flag Check on each Server Request ([ccf5dfb](https://bitbucket.agile.bns///commit/ccf5dfbda7ac09de46e0127b884e7b48e8e533bc))
* PIGEON-3417 kyc fixes and headers forwarding ([fe08a0f](https://bitbucket.agile.bns///commit/fe08a0f7cec1b4105fd783d32d07585ae45d9d2b))
* removed console.log ([3ac862d](https://bitbucket.agile.bns///commit/3ac862d1c81d16616f2df80e59a5e434c8dbf0c9))
* retrieve ot from headers instead of session if legacy auth ([5e00bcc](https://bitbucket.agile.bns///commit/5e00bcc1718bd152593e6afb42968a0ec43053f7))

## [1.2.0](https://bitbucket.agile.bns///compare/v1.1.113...v1.2.0) (2021-09-02)


### Features

* [PIGEON-3068][PIGEON-2894]-not logging /health and ignore PII handling ([98a5780](https://bitbucket.agile.bns///commit/98a5780922f96ebdc60b7a381534a36ed72eb80b))
* Adding Coverage PIGEON-3068 PIGEON-2984 ([54019fa](https://bitbucket.agile.bns///commit/54019faaf9915eedbcf74a7a10ac53ca15d3d3cb))
* PIGEON-2918 passing traceId to downstream requests ([957f5e5](https://bitbucket.agile.bns///commit/957f5e5bb7ae91cbb4f3d4d239a2cbef80ee58b1))
* PIGEON-3293 update build pack ([44ffa4c](https://bitbucket.agile.bns///commit/44ffa4c3b75a76a7f79568014f4e7771c0d52c24))
* PIGEON-3293 update node engine version ([0b062d6](https://bitbucket.agile.bns///commit/0b062d6be42a9ae98876218a3a6392da855c9606))
* PR feedback [PIGEON-3068][PIGEON-2984] ([ccf8b4d](https://bitbucket.agile.bns///commit/ccf8b4d8791f8372809b31e41bf7582ffd62c0e5))
* PWR bump for PIGEON-3124 ([e2a5d1c](https://bitbucket.agile.bns///commit/e2a5d1ce2e36b21652f984ae7fa1950cd52372f0))
* **PIGEON-2941, PIGEON-2655:** migrate to new canvas ([3ff4e29](https://bitbucket.agile.bns///commit/3ff4e291106bb8313563b1bc8f2210ce3f2c465b))
* **PIGEON-2954:** implemented heimdall standalone sso authentication ([21bfb0f](https://bitbucket.agile.bns///commit/21bfb0f86b8f792b997f256c929a997ae2dede2f))
* **PIGEON-3024:** added support for eexp ld flag - update start script to ref renamed file ([1fa58bf](https://bitbucket.agile.bns///commit/1fa58bfb3aa6ef182246ff10eb84989efbdb9612))
* re-writing if condition PR feedback PIGEON-2918 ([e442cb0](https://bitbucket.agile.bns///commit/e442cb05090834f22c57af62c511557305400d1e))
* removed redundant code PIGEON-3068 PIGEON-2984 ([b46ca40](https://bitbucket.agile.bns///commit/b46ca4028e5770cd352d38a318d468d63a17a738))
* update manifest file [PIGEON-3068][PIGEON-2984] ([9377385](https://bitbucket.agile.bns///commit/9377385ad7914dae23de9e13cd42864a40a4ba29))
* **PIGEON-3024:** added support for eexp ld flag - when flag is true opaque token isnt sent to eexp ([d0081f5](https://bitbucket.agile.bns///commit/d0081f547d4ada3e24090f10f8e8defe52d1aa71))
* **PIGEON-3115:** pwr version bump ([2ede302](https://bitbucket.agile.bns///commit/2ede302c28c7301c8f7b800995cff587f7b3c0b2))
* **PIGEON-3211:** render itrade details ([edf67a2](https://bitbucket.agile.bns///commit/edf67a2e5121bd64f5a86d36d9ee8f07c21c6aa7))


### Bug Fixes

* [PIGEON-3024] Fixed ld env variable name ([7f712dd](https://bitbucket.agile.bns///commit/7f712dd485187288ad9e0ef6e2288f9fb1e064f2))
* add client id to higher envs for sso ([25720ff](https://bitbucket.agile.bns///commit/25720ff5ff607e9a4ff32950f42f227ab45436a0))
* add http no proxy to higher envs ([91bd048](https://bitbucket.agile.bns///commit/91bd048836912bb1db2de84bbb5be3f8736b104d))
* blackduck remediations for pigeon-web PIGEON-3166 ([33cdaa6](https://bitbucket.agile.bns///commit/33cdaa6fb1406e3965161b55afbee4fdd9a0b465))
* ODP bugfixes pwr bump ([b074c26](https://bitbucket.agile.bns///commit/b074c265a373a0e625a750e9e7b0ec17d435498d))
* PIGEON-3181 PIGEON-3182 ODP bugfixes ([1cc4bcb](https://bitbucket.agile.bns///commit/1cc4bcb8ad8aa76c4a6b10e9b938ede247e142bf))
* PWR bump [PIGEON-3094][PIGEON-3093][PIGEON-2895][PIGEON-3106] ([74b4ad3](https://bitbucket.agile.bns///commit/74b4ad374f9b94916266a51069c4824dc982a3fb))
* PWR bump for [PIGEON-3178][PIGEON-3179][PIGEON-3180] ([d66a564](https://bitbucket.agile.bns///commit/d66a564705b2397226cc8023be7e1fa34d0d124a))
* PWR bump for PIGEON-3188 ([8f8bd5c](https://bitbucket.agile.bns///commit/8f8bd5ce617a3aec379f337cd0ce606cfb4a820a))
* pwr bump PIGEON-2866 ([31546f5](https://bitbucket.agile.bns///commit/31546f51bbfa4e9ec1f4470018cfdeb155787881))
* pwr bump PIGEON-2866 ([a8a0efb](https://bitbucket.agile.bns///commit/a8a0efb30b834002ec0fd89e9078678e1b93bb14))
* PWR bump PIGEON-3099 ([b9b98b4](https://bitbucket.agile.bns///commit/b9b98b465b670172a2dbe946ac7fa1daa0dd3c3a))
* PWR bump PIGEON-3185 PIGEON-3186 ([f118b26](https://bitbucket.agile.bns///commit/f118b26e101f33050689fee8b643e061c3875ac0))
* remove csrf token middleware from rendered-campaigns endpoints ([cc0d274](https://bitbucket.agile.bns///commit/cc0d27487430d5dd245ab63067a661d684d2fee6))
* remove vulnerable versions of redis and color-string ([02ee812](https://bitbucket.agile.bns///commit/02ee812f5541223cfc8d6c75b113ca4313f61553))
* **PIGEON-2954:** read card from decoded jwt instead of req session since sub dne after refresh ([6fdf938](https://bitbucket.agile.bns///commit/6fdf93873db3a8a1d7b093b709a1d25e6e2a5d20))
* set offer's FE viewed property only if offers are loaded in state ([ff5eab3](https://bitbucket.agile.bns///commit/ff5eab3f6a14d1d953ebd1bcc19f405d8ed46f6c))
* **PIGEON-3113:** send viewed disposition if offer cta does not call view campaign details endpoint ([f2dc628](https://bitbucket.agile.bns///commit/f2dc6287e07aa96d5c397c93d4eaecacfc681d84))
* **PIGEON-3357:** pass opaque token to eexp - return res ([bde8200](https://bitbucket.agile.bns///commit/bde820019378307a7b73aaa211f62851f32fbb09))
* **PIGEON-3357:** pass opaque token to eexp if using legacy authentication ([e653173](https://bitbucket.agile.bns///commit/e653173868e43663a8440b68696dfe752ae912af))
* spelling typo fix for legal footnotes ABM PACC PIGEON-3200 ([bddfdb3](https://bitbucket.agile.bns///commit/bddfdb3c4e0249ec34dc012999865fe7a89aeaff))
* switch from bns to public akamai hostname for sso in uat and prod ([77a746e](https://bitbucket.agile.bns///commit/77a746e0a2e98fc2342830366b12b1e5f322cf09))

### [1.1.113](https://bitbucket.agile.bns///compare/v1.1.104...v1.1.113) (2021-04-28)
