const { getResponse, pickReqHeaders, mapBnsApiError } = require('./index');

describe('common utilities', () => {
	test('getResponse - should handle non JSON response', async () => {
		try {
			const res = await getResponse({
				status: 200,
				text: jest.fn().mockResolvedValueOnce('sample text'),
			});
			expect(res).toBeUndefined();
		} catch (err) {
			expect(err).toHaveProperty('response');
			expect(err.response).toEqual('sample text');
		}
	});

	test('getResponse - pass invalid attribute value to getResponse function and expect a validation error', async () => {
		try {
			const res = await getResponse({
				status: 400,
				message: 'disposition must be one of [A, V]',
				text: jest.fn().mockResolvedValueOnce('Validation Error'),
			});
			expect(res.status).toEqual(400);
			expect(res.text).toEqual('Validation Error');
		} catch (err) {
		}
	});

	test('mapBnsApiError - 400', async () => {
		const request = { method: 'POST', url: 'http://localhost/v1/myapi', headers: { header1: 'v1', header2: 'v2' } };
		const response = { status: 400, text: () => Promise.resolve('{ "notifications": [ { "code": 123 } ] }') };
		const result = await mapBnsApiError({ message: 'Failed to set campaign disposition via insights.', request, response });
		expect(result.statusCode).toBe(400);
		expect(result.code).toBe('HTTP_BAD_REQUEST');
		expect(result.message).toBe('Bad request');
		expect(result.metadata.request).toStrictEqual(request);
		expect(result.metadata.response.body.notifications).toStrictEqual([ { code: 123 } ]);
	});

	test('mapBnsApiError - 404', async () => {
		const request = { method: 'POST', url: 'http://localhost/v1/myapi', headers: { header1: 'v1', header2: 'v2' } };
		const response = { status: 404, text: () => Promise.resolve('{ "notifications": [ { "code": "not_found" } ] }') };
		const result = await mapBnsApiError({ message: 'Failed to set campaign disposition via insights.', request, response });
		expect(result.statusCode).toBe(404);
		expect(result.code).toBe('HTTP_NOT_FOUND');
		expect(result.message).toBe('Not found');
		expect(result.metadata.request).toStrictEqual(request);
		expect(result.metadata.response.body.notifications).toStrictEqual([ { code: 'not_found' } ]);
	});

	test('mapBnsApiError - html response from proxy layers', async () => {
		const request = { method: 'POST', url: 'http://localhost/v1/myapi', headers: { header1: 'v1', header2: 'v2' } };
		const response = { status: 502, text: () => Promise.resolve('<html>Proxy is not in a good mood. Apache.</html>') };
		const result = await mapBnsApiError({ message: 'Failed to set campaign disposition via insights.', request, response });
		expect(result.statusCode).toBe(502);
		expect(result.code).toBe('HTTP_BAD_GATEWAY');
		expect(result.message).toMatch(/Could not parse response to JSON/i);
		expect(result.metadata.request).toStrictEqual(request);
		expect(result.metadata.response.body).toBe('<html>Proxy is not in a good mood. Apache.</html>');
	});

	test('pickReqHeaders', () => {
		const reqHeaders = {
			'authorization': 'TOPSECRET',
			'preferred-environment': 'uatred',
			'x-language': 'en',
			'x-mock-insight': 'true',
			'x-channel-id': 'mobile',
			'x-application': 'N2',
			'x-session-id': 'session-123',
			'x-b3-spanid': '1234-1234',
			'x-b3-traceid': '1234-1234-1234-1234',
			'x-originating-appl-code': 'ABC123',
			'country': 'JP',
		};
		const req = {
			get: key => reqHeaders[key],
		};
		const result = pickReqHeaders(req, [
			'preferred-environment',
			'x-language',
			'x-mock-insight',
			'x-channel-id',
			'x-application',
			'x-session-id',
			'x-b3-spanid',
			'x-b3-traceid',
			'x-originating-appl-code',
		]);
		const expected = { ...reqHeaders };
		delete expected.authorization;
		delete expected.country;
		expect(result).toStrictEqual(expected);
	});
});
