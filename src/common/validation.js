const Joi = require('@hapi/joi');

const ruleId = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9]+$/);
const messageId = Joi.string().min(1).max(50).regex(/^[a-zA-Z0-9-]+$/);
const messageCategory = Joi.string().min(1).max(50).regex(/^[a-zA-Z0-9]+$/);
const application = Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/);
const platform = Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/);
const page = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/);
const container = Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/);
const insight = Joi.boolean().default(false);
const scotiaCard = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/);
const channelId = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/);
const xApplication = Joi.string().valid('N1', 'N2', ' ');
const sessionId = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/);
const traceId = Joi.string();
const spanId = Joi.string();
const xOriginatingApplCode = Joi.string();
const xlanguage = Joi.string();
const country = Joi.string();
const preferredEnv = Joi.string().min(1).max(15).regex(/^[a-zA-Z]+$/);
const xMockInsight = Joi.string().max(5);

const dispositionPathSchema = Joi.object()
	.keys({ ruleId })
	.requiredKeys([ 'ruleId' ]);

const dispositionBodySchema = Joi.object()
	.keys({
		rule_id: ruleId,
		message_id: messageId,
		message_category: messageCategory,
		disposition: Joi.string().valid('A', 'V', 'Y', 'N', 'C', 'D', 'S', ' '),
		page,
		container,
		platform,
		application,
	})
	.requiredKeys([ 'application', 'platform', 'disposition' ]);

const renderedCampaignsSchema = Joi.object()
	.keys({
		application,
		platform,
		page: page.allow(''),
		container: container.allow(''),
		insight,
		'x-channel-id': channelId,
		'x-customer-scotiacard': scotiaCard,
		'x-application': xApplication,
		'x-b3-traceid': traceId,
		'x-b3-spanid': spanId,
		'x-originating-appl-code': xOriginatingApplCode,
		'x-mock-insight': xMockInsight,
		'Preferred-Environment': preferredEnv,
		'x-language': xlanguage,
	})
	.requiredKeys([ 'application', 'platform', 'x-channel-id', 'x-customer-scotiacard' ]);

const renderedCampaignSchema = Joi.object()
	.keys({
		ruleId,
		message_id: messageId,
		application,
		platform,
		page: page.allow(''),
		container: container.allow(''),
		insight,
		'x-channel-id': channelId,
		'x-customer-scotiacard': scotiaCard,
		'x-application': xApplication,
		'x-b3-traceid': traceId,
		'x-b3-spanid': spanId,
		'x-originating-appl-code': xOriginatingApplCode,
		'x-mock-insight': xMockInsight,
		'Preferred-Environment': preferredEnv,
		'x-language': xlanguage,
	})
	.requiredKeys([ 'ruleId', 'application', 'platform', 'x-channel-id', 'x-customer-scotiacard' ]);

const webTrackIdSchema = Joi.object()
	.keys({
		'x-b3-traceid': traceId,
		'x-b3-spanid': spanId,
		'x-channel-id': channelId,
		'x-originating-appl-code': xOriginatingApplCode,
		'x-country-code': country,
		'x-application': xApplication,
		'x-session-id': sessionId,
		'x-language': xlanguage,
		'x-customer-scotiacard': scotiaCard,
	})
	.requiredKeys([ 'x-b3-traceid', 'x-b3-spanid', 'x-channel-id', 'x-originating-appl-code', 'x-country-code', 'x-customer-scotiacard' ]);

const allowedCookiesSchema = Joi.object()
	.keys({
		'x-channel-id': channelId,
		'x-application': xApplication,
		'preferred-environment': preferredEnv,
		'x-language': Joi.string().min(1).max(15).regex(/^[a-zA-Z_-]+$/),
		'dark-mode': Joi.string().default('false'),
		'deploy-environment': Joi.string().min(1).max(15).regex(/^[a-zA-Z_-]+$/),
	});

module.exports = {
	dispositionPathSchema,
	dispositionBodySchema,
	renderedCampaignSchema,
	renderedCampaignsSchema,
	webTrackIdSchema,
	allowedCookiesSchema,
};
