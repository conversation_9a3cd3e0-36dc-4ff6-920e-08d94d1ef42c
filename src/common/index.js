const qs = require('querystring');
const HttpError = require('../server/errors/http-error');

const getResponse = async (result) => {
	const response = await result.text();
	try {
		const json = JSON.parse(response);
		return Promise.resolve(json);
	} catch (err) {
		err.response = response;
		return Promise.reject(err);
	}
};

/**
 * Call this function when response.ok is falsy for fetch requests to BNS APIs.
 * This helper function will attempt to map to the most specific HttpError object available
 * based on contents found in the response body. If API called follows BNS API standards,
 * even on failure, there are specific fields in the response body that will provide
 * useful details we can log for the purpose of troubleshooting. This function expects
 * the caller to pass along the error thrown back, into the next() handler,
 * for global error middleware to consume.
 *
 * @param options - options to customize behaviour of error handler for BNS API failures
 * @param options.message - primary error message
 * @param options.request - request object for logging purpose, it's recommended to include distributed trace ids, do not include PII
 * @param options.response - response object used to extract response stream, depends on node-fetch library, do not include PII
 */
const mapBnsApiError = async ({
	message,
	request,
	response,
} = {}) => {
	let errMsg = `Bad response from downstream service. ${message}`;
	let clientRes;
	let clientResJson;
	try {
		clientRes = await response.text();
		clientResJson = JSON.parse(clientRes);
	} catch (e) {
		errMsg += ` Could not parse response to JSON due to error caught. ${e.toString()}`;
	}

	const metadata = {
		request,
		response: {
			status: response.status,
			body: clientResJson
				? { data: '***REDACTED***', notifications: clientResJson.notifications }
				: `${clientRes}`,
		},
	};

	switch (response.status) {
		case 400: return HttpError.badRequest('Bad request', metadata);
		case 404: return HttpError.notFound('Not found', metadata);
		default: return HttpError.badGateway(errMsg, metadata);
	}
};

/**
 * Return only defined properties (that has not `null` or `undefined` values) of an object
 * @param {object} o Object
 * @returns {object} Object with only defined properties
 */
const pickTruthy = (o) => JSON.parse(JSON.stringify(o, (k, v) => v === null || v === undefined ? undefined : v));

/**
 * Returns an object as a string that can be used in HTTP requests, example: key1=value1&key2=value2
 * @param {object} query Query object
 * @returns {string} Query string
 */
const getQueryString = (query) => Object.keys(query).length > 0 ? `?${qs.stringify(query)}` : '';

/**
 * Returns an object with defined request header name as keys, and header values retrieved
 * from request object via express.js req.get(key). Useful as syntax sugar for picking
 * passthrough request headers for downstream fetch requests.
 * @param {*} req - request
 * @param {*} keys - request header names
 * @returns {object} object with defined request headers
 */
const pickReqHeaders = (req, keys) => keys
	.map(k => { return { [k]: req.get(k) }; })
	.reduce((o, i) => Object.assign(o, i));

module.exports = {
	getResponse,
	mapBnsApiError,
	pickTruthy,
	getQueryString,
	pickReqHeaders,
};
