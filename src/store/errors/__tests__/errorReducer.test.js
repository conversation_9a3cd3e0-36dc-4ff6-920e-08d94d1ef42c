import errorReducer from 'store/errors/errorReducer';
import { addError, resetErrors } from 'store/errors/errorActions';

describe('errorReducer', () => {
	const initialState = {};

	it('returns initialState on unknown action', () => {
		const actual = errorReducer(undefined, {});
		const expected = initialState;
		expect(actual).toEqual(expected);
	});

	it('adds an error', () => {
		const actual = errorReducer(initialState, addError({ errorCode: 401, errorMessage: 'unauthorized' }));
		const expected = { 401: 'unauthorized' };
		expect(actual).toEqual(expected);
	});

	it('resets all errors', () => {
		const actual = errorReducer({ 401: 'unauthorized' }, resetErrors());
		const expected = initialState;
		expect(actual).toEqual(expected);
	});
});
