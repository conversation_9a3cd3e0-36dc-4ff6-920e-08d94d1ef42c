import { errorSelector } from 'store/errors/errorsSelectors';
import { createMockStore } from 'redux-test-utils';

describe('errorsSelector', () => {
	it('returns an error in the expected format', () => {
		const errors = {
			410: 'she gone',
		};

		const store = createMockStore({ errors });

		const expected = { errorCode: '410', errorMessage: 'she gone' };
		const actual = errorSelector(store.getState());
		expect(actual).toEqual(expected);
	});

	it('returns null when there are no errors', () => {
		const store = createMockStore({});
		const actual = errorSelector(store.getState());
		expect(actual).toBe(null);
	});
});
