import { expectSaga } from 'redux-saga-test-plan';

import { dispositionHandler } from 'store/dispositions/dispositionsSaga';
import { setDisposition } from 'store/dispositions/dispositionsActions';

describe('dispositionsSaga', () => {
	const emptyState = {
		offers: {
			offersList: null,
		},
		content: {
			contentList: null,
		},
	};

	const mockedState = {
		preview: {
			...emptyState,
			offers: {
				offersList: {
					'test-id:test-id': {
						id: 'test-id',
						messageId: 'test-id',
						aaplication: 'nova',
						container: 'offers-and-programs',
						page: 'accounts',
					},
				},
			},
		},
		details: {
			...emptyState,
			content: {
				id: 'test-id',
				contentList: {
					'test-id': {
						type: 'nova__soft-messaging-details',
						application: 'nova',
						container: 'priority-box',
						metadata: {
							ruleName: 'test rule',
							ruleId: 'test-id',
							messageId: 'test-id',
							campaignId: 'MESSAGE',
						},
					},
				},
			},
		},
	};

	it('sets a dismissed disposition', () => {
		// calls dispositions handler with the action as a parameter to it
		return expectSaga(dispositionHandler, setDisposition({ id: 'test-id', messageId: 'test-id', disposition: 'D', platform: 'ios' }))
			.withState(mockedState.preview)
			.run();
	});

	it('sets a snoozed disposition', () => {
		// calls dispositions handler with the action as a parameter to it
		return expectSaga(dispositionHandler, setDisposition({ id: 'test-id', messageId: 'test-id', disposition: 'S', platform: 'ios' }))
			.withState(mockedState.preview)
			.run();
	});

	it('sets a snoozed disposition when loading content container directly', () => {
		// calls dispositions handler with the action as a parameter to it
		return expectSaga(dispositionHandler, setDisposition({ id: 'test-id', messageId: 'test-id', disposition: 'S', platform: 'ios' }))
			.withState(mockedState.details)
			.run();
	});

	it('sets a viewed disposition', () => {
		// calls dispositions handler with the action as a parameter to it
		return expectSaga(dispositionHandler, setDisposition({ id: 'test-id', messageId: 'test-id', disposition: 'V', platform: 'ios' }))
			.withState(mockedState.preview)
			.run();
	});

	it('sets the disposition', () => {
		// calls dispositions handler with the action as a parameter to it
		return expectSaga(dispositionHandler, setDisposition({ id: 'test-id', platform: 'ios' }))
			.withState(emptyState)
			.run();
	});
});
