import { takeLatest, select, put, call } from 'redux-saga/effects';
import { implementPromiseAction } from '@adobe/redux-saga-promise';

import { postDisposition } from '../../api/dispositions';
import { setDisposition } from './dispositionsActions';
import { setOfferDismissed, setOfferSnoozed } from '../offers/offersActions';
import { getOffersList } from '../offers/offersSelectors';
import { activeContentSelector } from '../content/contentSelectors';

export function * dispositionHandler(action) {
	const { id, messageId, disposition, messageCategory, ...queryParams } = action.payload;
	try {
		const offersList = yield select(getOffersList);
		const activeOffer = Object.values(offersList || {}).find(offer => offer.id === id && offer.messageId === messageId);
		const activeContent = yield select(activeContentSelector);
		const campaign = activeOffer || activeContent || {};
		const { page, container, application } = campaign;

		const body = {
			rule_id: id,
			message_id: messageId || queryParams.message_id,
			message_category: messageCategory || queryParams.message_category,
			disposition,
			page,
			container,
			platform: queryParams.platform,
			application,
		};

		// save disposition to backend, return promise to allow any front-end caller to wait for completion
		if ([ 'D', 'S', 'V' ].includes(disposition)) {
			yield call(implementPromiseAction, action, function * () {
				yield call(postDisposition, id, body);
			});
		}

		// update state store for any disposition that affect front-end logic
		if (disposition === 'D') {
			yield put(setOfferDismissed({ id, messageId }));
		} else if (disposition === 'S') {
			yield put(setOfferSnoozed({ id, messageId }));
		}
	} catch (error) {
		console.error('failed to set disposition');
	}
}

export function * dispositionsSaga() {
	yield takeLatest([ setDisposition ], dispositionHandler);
}

export default dispositionsSaga;
