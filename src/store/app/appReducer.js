import { handleActions } from 'redux-actions';

import {
	setPage,
	setPlatform,
	setAppLoading,
	setAppLoaded } from 'store/app/appActions';

const initialState = {
	page: null,
	isLoading: false,
};

const appReducer = handleActions({
	[setPage]: (state, action) => ({ ...state, page: action.payload }),
	[setPlatform]: (state, action) => ({ ...state, platform: action.payload }),
	[setAppLoading]: (state) => ({ ...state, isLoading: true }),
	[setAppLoaded]: (state) => ({ ...state, isLoading: false }),
}, initialState);

export default appReducer;
