import appReducer from 'store/app/appReducer';
import {
	setPage,
	setPlatform,
	setAppLoading,
	setAppLoaded } from 'store/app/appActions';

describe('appReducer tests', () => {
	const initialState = {
		page: null,
		platform: null,
		isLoading: false,
	};

	it('sets the page', () => {
		const expected = {
			...initialState,
			page: 'fake-page',
		};

		const actual = appReducer(initialState, setPage('fake-page'));
		expect(actual).toEqual(expected);
	});

	it('sets the platform', () => {
		const expected = {
			...initialState,
			platform: 'android',
		};

		const actual = appReducer(initialState, setPlatform('android'));
		expect(actual).toEqual(expected);
	});

	it('sets the app to be loading and not loaded', () => {
		const expected = {
			...initialState,
			isLoading: true,
		};

		let actual = appReducer(initialState, setAppLoading());
		expect(actual).toEqual(expected);

		expected.isLoading = false;
		actual = appReducer(actual, setAppLoaded());
		expect(actual).toEqual(expected);
	});
});
