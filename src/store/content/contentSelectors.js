import { createSelector } from 'reselect';

const contentListSelector = state => state.content.contentList;
const activeContentIdSelector = state => state.content.id;

export const activeContentSelector = createSelector(
	contentListSelector,
	activeContentIdSelector,
	(contentList, id) => contentList ? contentList[id] : null,
);

const contentListKeys = state => Object.keys(state.content.contentList || {});
export const getLoadedContentIds = createSelector(
	contentListKeys,
	(keys) => keys,
);
