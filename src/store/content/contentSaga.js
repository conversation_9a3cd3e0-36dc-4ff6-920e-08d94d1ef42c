import {
	all,
	cancel,
	delay,
	fork,
	select,
	put,
	takeLatest } from 'redux-saga/effects';

import { getCampaignWithId, getSAMLToken } from 'api/content';
import { showLoadingThreshold } from 'store/content/constants';
import { setAppLoading, setAppLoaded } from 'store/app/appActions';
import { getLoadedContentIds } from 'store/content/contentSelectors';
import { setOfferViewed } from 'store/offers/offersActions';
import { offersNeverLoaded } from 'store/offers/offersSelectors';
import { addError } from 'store/errors/errorActions';
import ErrorThrowable from 'store/errors/errorThrowable';
import {
	fetchContent,
	loadedContent,
	requestFinished,
	loadSAMLToken,
	SAMLToken,
	fulfillCampaign } from 'store/content/contentActions';

function * setLoading() {
	yield delay(showLoadingThreshold);
	yield put(setAppLoading());
}

export function * contentHandler(action) {
	const { id, messageId, selectContents, channel } = action.payload;

	// in order to show a loading spinner, we first get all the content ids of the actively loaded contents in the redux
	// store. If this content has never been loaded (i.e. its not in the array) only then will we show a spinner...
	const loadedContentIds = yield select(getLoadedContentIds);
	const offersNotLoaded = yield select(offersNeverLoaded);
	// we've seen the offers screen and we haven't loaded this offer yet
	const shouldShowSpinner = !offersNotLoaded && !loadedContentIds.includes(id);
	const loadingTask = shouldShowSpinner ? yield fork(setLoading) : undefined;
	try {
		const campaign = yield getCampaignWithId(id, messageId, selectContents, channel);
		if (campaign && campaign.id) {
			if (!offersNotLoaded) {
				yield put(setOfferViewed({ id, messageId }));
			}
			// cancel the loading task if this request finished faster than our loading timeout
			yield all([
				put(loadedContent({ ...campaign, messageId })),
				put(setAppLoaded()),
				put(requestFinished()),
			]);
		} else {
			throw new ErrorThrowable({
				errorCode: 503,
				errorMessage: 'CAMPAIGN_NOT_FOUND',
			});
		}
	} catch (error) {
		yield all([
			put(addError(error)),
			put(setAppLoaded()),
			put(requestFinished()),
		]);
	}

	yield cancel(loadingTask);
}

export function * fulfillableCampaignHandler(action) {
	// redirectCampaignType is an enum of scotiahome and SCRL
	const { id, messageId, campaignType } = action.payload;
	yield put(loadSAMLToken());
	const samlToken = yield getSAMLToken(id, messageId);
	if (samlToken && samlToken.saml_token) {
		yield put(SAMLToken({
			token: samlToken.saml_token,
			campaignType,
		}));
	}
}

export function * contentSaga() {
	yield takeLatest([ fetchContent ], contentHandler);
	yield takeLatest([ fulfillCampaign ], fulfillableCampaignHandler);
}

export default contentSaga;
