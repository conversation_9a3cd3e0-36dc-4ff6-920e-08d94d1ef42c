import { handleActions } from 'redux-actions';

import { loadedContent, setContentId, SAMLToken } from 'store/content/contentActions';

const initialState = {
	contentList: null,
	id: null,
	SAMLToken: null,
};

const contentReducer = handleActions({
	[loadedContent]: (state, action) => (
		{
			...state,
			contentList: {
				...state.contentList,
				[action.payload.id]: {
					type: action.payload.type,
					application: action.payload.application,
					container: action.payload.container,
					metadata: {
						ruleName: action.payload.name,
						ruleId: action.payload.id,
						messageId: action.payload.external_ref.message_id,
						messageCategory: (action.payload.external_ref.data && action.payload.external_ref.data.message_category),
						campaignId: action.payload.external_ref.campaign_id,
					},
					...action.payload.content,
				},
			},
		}
	),
	[setContentId]: (state, action) => ({ ...state, id: action.payload }),
	[SAMLToken]: (state, action) => ({ ...state, SAMLToken: action.payload }),
}, initialState);

export default contentReducer;
