import { expectSaga } from 'redux-saga-test-plan';

import { content<PERSON><PERSON><PERSON> } from 'store/content/contentSaga';
import { fetchContent, loadedContent, requestFinished } from 'store/content/contentActions';
import { addError } from 'store/errors/errorActions';

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

describe('contentSaga', () => {
	const mock = new MockAdapter(axios);

	const mockedState = {
		content: {
			contentList: {
				'a': { title: 'hey' },
			},
		},
		offers: {
			offersList: null,
		},
	};

	const fakeContentResponse = {
		data: {
			name: 'someting',
			id: 'fake-id',
			type: 'campaign',
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				name: 'Fake-campaign',
				preview: { name: 'preview' },
				details: { name: 'details' },
			},
		},
		notifications: [],
	};

	it('loads the content', () => {
		mock.onGet().replyOnce(200, fakeContentResponse);

		// calls content handler with the action as a parameter to it
		return expectSaga(contentHand<PERSON>, fetchContent({ id: 'fake-id', messageId: 'fake-message-id' }))
			.withState(mockedState)
			// we expect the response.data since that is what the the content api file returns
			.put(loadedContent({ ...fakeContentResponse.data, messageId: 'fake-message-id' }))
			.put(requestFinished())
			.run();
	});

	it('handles a failed request', () => {
		const error = new Error('fake error message');
		mock.onGet().replyOnce(503, error);
		return expectSaga(contentHandler, fetchContent('error-id'))
			.withState(mockedState)
			.put(addError({ errorCode: 503, errorMessage: 'CAMPAIGN_NOT_FOUND' }))
			.run();
	});
});
