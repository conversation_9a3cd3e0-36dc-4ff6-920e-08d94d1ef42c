import contentReducer from '../contentReducer';
import { setContentId, loadedContent } from '../contentActions';

describe('contentReducer', () => {
	const initialState = { contentList: null, id: null, SAMLToken: null };
	it('returns its initial state', () => {
		const expected = initialState;
		const actual = contentReducer(undefined, {});
		expect(actual).toEqual(expected);
	});

	it('sets the content id', () => {
		const expected = {
			contentList: null,
			id: 'testId',
			SAMLToken: null,
		};

		const actual = contentReducer(initialState, setContentId('testId'));
		expect(actual).toEqual(expected);
	});

	it('sets loaded content', () => {
		const expected = {
			contentList: {
				firstContentId: {
					type: 'a',
					fakeKey: 'myContent',
					metadata: {
						messageId: 'D54D007049AE0741',
						campaignId: 'CLI01',
						ruleName: 'fake rule name 1',
					},
				},
				secondContentId: {
					type: 'b',
					fakeKey: 'secondContent',
					metadata: {
						messageId: 'D54D007049AE0742',
						campaignId: 'CLI02',
						ruleName: 'fake rule name 2',
					},
				},
				thirdContentId: {
					application: 'my-application',
					container: 'my-container',
					type: 'c',
					fakeKey: 'thirdContent',
					metadata: {
						messageId: 'D54D007049AE0743',
						messageCategory: 'test-category',
						ruleId: 'thirdContentId',
						campaignId: 'CLI03',
						ruleName: 'fake rule name 3',
					},
				},
			},
			id: null,
			SAMLToken: null,
		};

		const state = {
			contentList: {
				firstContentId: {
					type: 'a',
					fakeKey: 'myContent',
					metadata: {
						messageId: 'D54D007049AE0741',
						campaignId: 'CLI01',
						ruleName: 'fake rule name 1',
					},
				},
				secondContentId: {
					type: 'b',
					fakeKey: 'secondContent',
					metadata: {
						messageId: 'D54D007049AE0742',
						campaignId: 'CLI02',
						ruleName: 'fake rule name 2',
					},
				},
			},
			id: null,
			SAMLToken: null,
		};

		const contentPayload = {
			id: 'thirdContentId',
			type: 'c',
			name: 'fake rule name 3',
			application: 'my-application',
			container: 'my-container',
			external_ref: {
				campaign_id: 'CLI03',
				message_id: 'D54D007049AE0743',
				message_source: 'KT',
				data: {
					message_category: 'test-category',
				},
			},
			content: {
				fakeKey: 'thirdContent',
			},
		};

		const actual = contentReducer(state, loadedContent(contentPayload));
		expect(actual).toEqual(expected);
	});

	it.todo('sets loaded content for KT campaign with data attribute');
});
