export const showLoadingThreshold = 100; // the time that something is delayed before we show any loading spinners

const campaignPreviewTypes = {
	standingCampaign: 'standingCampaignPreview',
	targetedCampaign: 'targetedCampaignPreview',
	iTradePriorityBoxPreview: 'itrade__priority-preview',
	iTradePriorityBoxDetails: 'itrade__priority-details',
	NovaSoftMsgDetails: 'nova__soft-messaging-details',
	harmonyReward: 'nova__rewards-reward-preview',
	harmonyOffer: 'nova__rewards-offer-preview',
	harmonyCampaign: 'nova__rewards-campaign-preview',
	atlantisPriorityPreview: 'atlantis__priority-preview',
};

const SOLEndpoints = window.solendpoint ? JSON.parse(window.solendpoint.replace(/&quot;/ig, '"')) : {};

const fulfillableTargetEndpoints = {
	scotiahome: window.scotiahome ? JSON.parse(window.scotiahome.replace(/&quot;/ig, '"')) : {},
	scrl: window.scrl ? JSON.parse(window.scrl.replace(/&quot;/ig, '"')) : {},
};

const campaignTypes = {
	standingCampaign: 'standingCampaign',
	targetedCampaign: 'targetedCampaign',
};

const iconTypes = [
	'alert',
	'announcement',
	'bills',
	'credit-card',
	'loans',
	'overdraft-protection',
	'other-products',
	'statement',
	'statement-ready',
];

const badgeTypes = [
	'new',
	'approved',
	'updated',
	'pending',
	'declined',
	'deleted',
];

const pageNames = {
	myActivity: [ 'my-activity', 'activities' ],
	offersAndPrograms: [ 'offers-and-programs', 'accounts', 'portfolio' ],
};

const selectContentsMappedByMode = {
	intercept: 'interceptDetails',
	default: 'details',
};

export {
	campaignPreviewTypes,
	campaignTypes,
	iconTypes,
	badgeTypes,
	pageNames,
	SOLEndpoints,
	fulfillableTargetEndpoints,
	selectContentsMappedByMode,
};
