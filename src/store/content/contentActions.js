import { createAction } from 'redux-actions';

export const fetchContent = createAction('FETCH_CONTENT');
export const loadedContent = createAction('LOADED_CONTENT');
export const contentLoadFailed = createAction('CONTENT_LOAD_FAILED');
export const setContentId = createAction('SET_CONTENT_ID');
export const requestFinished = createAction('REQUEST_FINISHED');
export const fulfillCampaign = createAction('FULFILL_CAMPAIGN');
export const loadSAMLToken = createAction('LOAD_SAML_TOKEN');
export const SAMLToken = createAction('SAML_TOKEN');
