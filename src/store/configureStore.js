/**
 * Create the store with dynamic reducers
 */

import { createStore, applyMiddleware, compose } from 'redux';
import { routerMiddleware } from 'react-router-redux';
import createSagaMiddleware from 'redux-saga';
import { promiseMiddleware } from '@adobe/redux-saga-promise';
import interceptor from 'api/interceptor';
import createReducer from 'store/rootReducers';
import rootSaga from 'store/rootSagas';

const sagaMiddleware = createSagaMiddleware();

export default function configureStore(state, history) {
	// Create the store with middlewares
	// 1. sagaMiddleware: Makes redux-sagas work
	// 2. routerMiddleware: Syncs the location/URL path to the state
	// 3. promiseMiddleware: allow saga to be wrapped in promise and waited for
	const middlewares = [ promiseMiddleware, sagaMiddleware, routerMiddleware(history) ];

	const enhancers = [ applyMiddleware(...middlewares) ];

	// If Redux DevTools Extension is installed use it, otherwise use Redux compose
	/* eslint-disable no-underscore-dangle, indent */
	/* istanbul ignore next */
	const composeEnhancers =
		process.env.NODE_ENV !== 'production' &&
		typeof window === 'object' &&
		window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__
			? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({
				shouldHotReload: false,
			})
			: compose;
	/* eslint-enable */
	const initialState = state || {};
	const store = createStore(
		createReducer(),
		initialState,
		composeEnhancers(...enhancers),
	);

	// Extensions
	store.runSaga = sagaMiddleware.run;
	store.injectedReducers = {}; // Reducer registry
	store.injectedSagas = [ // Saga registry
		rootSaga,
	];

	store.injectedSagas.forEach((saga) => store.runSaga(saga));

	// Make reducers hot reloadable
	/* istanbul ignore if */
	if (module.hot) {
		module.hot.accept('./rootReducers', () => {
			store.replaceReducer(createReducer(store.injectedReducers));
		});
	}
	interceptor(store);
	return store;
}
