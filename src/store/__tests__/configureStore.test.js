import configureStore from 'store/configureStore';

describe('configureStore', () => {
	it('returns a store object from an initialState with the correct properties', () => {
		const actual = configureStore();
		expect(actual).toHaveProperty('dispatch');
		expect(actual).toHaveProperty('getState');
		expect(actual).toHaveProperty('injectedReducers');
		expect(actual).toHaveProperty('runSaga');
		expect(actual).toHaveProperty('injectedSagas');
	});
});
