import { LOCATION_CHANGE } from 'react-router-redux';
import { routeReducer } from 'store/rootReducers';

describe('routeReducer', () => {
	it('sets the new location', () => {
		const initialState = {
			location: null,
		};

		const expected = {
			location: 'newlocation',
		};

		const actual = routeReducer(initialState, { type: LOCATION_CHANGE, payload: 'newlocation' });
		expect(actual).toEqual(expected);
	});
});
