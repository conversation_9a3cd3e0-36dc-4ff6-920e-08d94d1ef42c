import { all, call, put, takeLatest } from 'redux-saga/effects';
import { implementPromiseAction } from '@adobe/redux-saga-promise';

import { getOffers } from '../../api/offers';
import { fetchOffers, fetchOffersSync, loadedOffers } from './offersActions';
import { setPage, setPlatform } from '../app/appActions';

const loadOffers = (page, queryParams) => {
	return function * loadOffers() {
		const offers = yield getOffers({ page, ...queryParams });
		yield all([
			put(setPage(page)),
			put(setPlatform(queryParams.platform)),
			put(loadedOffers({ offers: offers ? offers.items : [] })),
		]);
	};
};

export function * offerHandler(action) {
	const {
		page,
		...queryParams
	} = action.payload;
	try {
		if (action.type === 'FETCH_OFFERS_SYNCHRONOUS.TRIGGER') {
			yield call(implementPromiseAction, action, loadOffers(page, queryParams));
		} else {
			yield loadOffers(page, queryParams)();
		}
	} catch (error) {
		console.error('offerHandler failed to reload offers, offers state will be reset');
		yield put(loadedOffers({ offers: [] }));
	}
}

export function * offersSaga() {
	yield takeLatest([ fetchOffers, fetchOffersSync ], offerHandler);
}

export default offersSaga;
