import offersReducer from 'store/offers/offersReducer';
import { loadedOffers, setOfferDismissed, setOfferViewed, setOfferSnoozed } from '../offersActions';

import { campaignPreviewTypes } from 'store/content/constants';

describe('offerReducer', () => {
	const initialState = {
		offersList: null,
		offersSortOrder: [],
	};

	test('the store returns its default', () => {
		const expected = initialState;
		const actual = offersReducer(undefined, {});
		expect(actual).toEqual(expected);
	});

	test('the store sets the offers in offersList', () => {
		const fakeOffer = (uniqueId) => ({
			id: uniqueId,
			type: campaignPreviewTypes.targetedCampaign,
			application: 'nova',
			container: 'offers-and-programs',
			pages: [ 'accounts' ],
			dismissable: true,
			viewed: false,
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				name: `name-${uniqueId}`,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				image: {
					image: {
						file: {
							url: `image-${uniqueId}`,
						},
					},
					imageDark: {
						file: {
							url: `imageDark-${uniqueId}`,
						},
					},
					altText: `altText-${uniqueId}`,
				},
				ctaLink: {
					accessibilityText: `ctaLink-accessibilityText-${uniqueId}`,
					linkAction: {
						url: `ctaLink-linkAction-url-${uniqueId}`,
					},
					linkText: `ctaLink-linkText-${uniqueId}`,
				},
			},
		});
		const offers = [
			fakeOffer('2fake'),
			fakeOffer('creditCard'),
			fakeOffer('bnsOffer'),
		];

		const expectedOffer = (uniqueId) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				messageId: 'fake-message-id',
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				image: `image-${uniqueId}`,
				imageDark: `imageDark-${uniqueId}`,
				imageAltText: `altText-${uniqueId}`,
				type: campaignPreviewTypes.targetedCampaign,
				application: 'nova',
				container: 'offers-and-programs',
				page: 'accounts',
				ctaLink: {
					accessibilityText: `ctaLink-accessibilityText-${uniqueId}`,
					linkAction: {
						url: `ctaLink-linkAction-url-${uniqueId}`,
					},
					linkText: `ctaLink-linkText-${uniqueId}`,
				},
				hasBeenDismissed: false,
				snoozed: false,
				dismissable: true,
				viewed: false,
			},
		});

		const expected = {
			offersList: {
				...expectedOffer('2fake'),
				...expectedOffer('creditCard'),
				...expectedOffer('bnsOffer'),
			},
			offersSortOrder: [ '2fake:fake-message-id', 'creditCard:fake-message-id', 'bnsOffer:fake-message-id' ],
		};

		const actual = offersReducer(initialState, loadedOffers({ offers }));
		expect(actual).toEqual(expected);
	});

	test('the store sets the standingCampaign in offersList', () => {
		const fakeOffer = (uniqueId) => ({
			id: uniqueId,
			type: campaignPreviewTypes.standingCampaign,
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				name: `name-${uniqueId}`,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				icon: 'credit-card',
				badge: 'approved',
				linkAction: {
					url: 'http://mock.com/',
				},
			},
			application: 'nova',
			platform: 'ios',
		});
		const offers = [
			fakeOffer('2fake'),
		];

		const expectedOffer = (uniqueId) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				icon: 'credit-card',
				badge: 'approved',
				description: `description-${uniqueId}`,
				messageId: 'fake-message-id',
				type: campaignPreviewTypes.standingCampaign,
				linkUrl: 'http://mock.com/',
			},
		});

		const expected = {
			offersList: {
				...expectedOffer('2fake'),
			},
			offersSortOrder: [ '2fake:fake-message-id' ],
		};

		const actual = offersReducer(initialState, loadedOffers({ offers }));
		expect(actual).toEqual(expected);
	});

	test('null linkAction', () => {
		const fakeOffer = (uniqueId) => ({
			id: uniqueId,
			type: campaignPreviewTypes.standingCampaign,
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				name: `name-${uniqueId}`,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				icon: 'credit-card',
				badge: 'approved',
			},
		});
		const offers = [
			fakeOffer('2fake'),
		];

		const expectedOffer = (uniqueId) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				icon: 'credit-card',
				badge: 'approved',
				description: `description-${uniqueId}`,
				messageId: 'fake-message-id',
				type: campaignPreviewTypes.standingCampaign,
				linkUrl: null,
			},
		});

		const expected = {
			offersList: {
				...expectedOffer('2fake'),
			},
			offersSortOrder: [ '2fake:fake-message-id' ],
		};

		const actual = offersReducer(initialState, loadedOffers({ offers }));
		expect(actual).toEqual(expected);
	});

	test('should generate a unique id if a message_id is supplied', () => {
		const fakeOffer = {
			id: 'totally-unique-id',
			type: campaignPreviewTypes.standingCampaign,
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				description: `description`,
				icon: 'credit-card',
				badge: 'approved',
				linkAction: {
					url: 'https://mock.com/',
				},
			},
		};
		const actual = offersReducer(initialState, loadedOffers({ offers: [ fakeOffer ] }));
		const expected = {
			offersList: {
				'totally-unique-id:fake-message-id': {
					id: 'totally-unique-id',
					messageId: 'fake-message-id',
					type: campaignPreviewTypes.standingCampaign,
					description: 'description',
					icon: 'credit-card',
					badge: 'approved',
					linkUrl: 'https://mock.com/',
				},
			},
			offersSortOrder: [ 'totally-unique-id:fake-message-id' ],
		};
		expect(actual).toEqual(expected);
	});

	test('multiple standing campaigns with same id and different message ids should not collide', () => {
		const fakeOffer = (id, messageId) => ({
			id,
			type: campaignPreviewTypes.standingCampaign,
			external_ref: {
				message_id: messageId,
			},
			content: {
				description: `description`,
				icon: 'credit-card',
				badge: 'approved',
				linkAction: {
					url: 'https://mock.com/',
				},
			},
		});

		const actual = offersReducer(
			initialState,
			loadedOffers({
				offers: [
					fakeOffer('first-id', 'first-message-id'),
					fakeOffer('first-id', 'another-message-id'),
					fakeOffer('second-id', 'first-message-id'),
					fakeOffer('last-id', null),
				],
			}));

		const innerContent = (id, messageId) => ({
			id,
			messageId,
			type: 'standingCampaignPreview',
			description: 'description',
			icon: 'credit-card',
			badge: 'approved',
			linkUrl: 'https://mock.com/',
		});

		const expected = {
			offersList: {
				'first-id:first-message-id': { ...innerContent('first-id', 'first-message-id') },
				'first-id:another-message-id': { ...innerContent('first-id', 'another-message-id') },
				'second-id:first-message-id': { ...innerContent('second-id', 'first-message-id') },
				'last-id': { ...innerContent('last-id', null) },
			},
			offersSortOrder: [
				'first-id:first-message-id',
				'first-id:another-message-id',
				'second-id:first-message-id',
				'last-id:null',
			],
		};
		expect(actual).toEqual(expected);
	});

	test('returns null image and altText if there is no content preview image', () => {
		const fakeOffer = (uniqueId) => ({
			id: uniqueId,
			type: campaignPreviewTypes.targetedCampaign,
			application: 'nova',
			container: 'offers-and-programs',
			pages: [ ],
			hasBeenDismissed: false,
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				name: `name-${uniqueId}`,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
			},
		});

		const expectedOffer = (uniqueId) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				messageId: 'fake-message-id',
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				image: null,
				imageDark: null,
				imageAltText: null,
				type: campaignPreviewTypes.targetedCampaign,
				application: 'nova',
				container: 'offers-and-programs',
				page: null,
				hasBeenDismissed: false,
				snoozed: false,
			},
		});

		const expected = {
			offersList: {
				...expectedOffer('fakeid-no-icon'),
			},
			offersSortOrder: [ 'fakeid-no-icon:fake-message-id' ],
		};

		const offers = [
			fakeOffer('fakeid-no-icon'),
		];

		const actual = offersReducer(initialState, loadedOffers({ offers }));
		expect(actual).toEqual(expected);
	});

	test('returns nothing with a non valid template type', () => {
		const fakeOffer = (uniqueId) => ({
			id: uniqueId,
			type: 'bs-never-gonna-be-a-type',
			content: {
				name: `name-${uniqueId}`,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
			},
		});

		const expected = {
			offersList: {},
			offersSortOrder: [ 'fakeid-no-icon:undefined' ],
		};

		const offers = [
			fakeOffer('fakeid-no-icon'),
		];

		const actual = offersReducer(initialState, loadedOffers({ offers }));
		expect(actual).toEqual(expected);
	});

	test('sets the offers dismissed flag', () => {
		const offer = (uniqueId, dispositionFlag) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				messageId: 'fake-message-id',
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				image: null,
				imageAltText: null,
				type: campaignPreviewTypes.targetedCampaign,
				application: 'nova',
				container: 'offers-and-programs',
				page: 'accounts',
				hasBeenDismissed: dispositionFlag,
			},
		});

		const initialState = {
			offersList: offer('fakeid-disposition', false),
		};

		const expected = {
			offersList: offer('fakeid-disposition', true),
		};

		const actual = offersReducer(initialState, setOfferDismissed({ id: 'fakeid-disposition', messageId: 'fake-message-id' }));
		expect(actual).toEqual(expected);
	});

	test('sets the offers snoozed flag', () => {
		const offer = (uniqueId, dispositionFlag) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				messageId: 'fake-message-id',
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				image: null,
				imageAltText: null,
				type: campaignPreviewTypes.targetedCampaign,
				application: 'nova',
				container: 'offers-and-programs',
				page: 'accounts',
				snoozed: dispositionFlag,
			},
		});

		const initialState = {
			offersList: offer('fakeid-disposition', false),
		};

		const expected = {
			offersList: offer('fakeid-disposition', true),
		};

		const actual = offersReducer(initialState, setOfferSnoozed({ id: 'fakeid-disposition', messageId: 'fake-message-id' }));
		expect(actual).toEqual(expected);
	});

	test('sets the offers viewed flag', () => {
		const offer = (uniqueId, viewedFlag) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				messageId: 'fake-message-id',
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				image: null,
				imageAltText: null,
				type: campaignPreviewTypes.targetedCampaign,
				application: 'nova',
				container: 'offers-and-programs',
				page: 'accounts',
				hasBeenDismissed: false,
				viewed: viewedFlag,
			},
		});

		const initialState = {
			offersList: offer('fakeid-disposition', false),
		};

		const expected = {
			offersList: offer('fakeid-disposition', true),
		};

		const actual = offersReducer(initialState, setOfferViewed({ id: 'fakeid-disposition', messageId: 'fake-message-id' }));
		expect(actual).toEqual(expected);
	});

	test('loads harmony offers', () => {
		const fakeOffer = (uniqueId, type, container) => ({
			id: uniqueId,
			type,
			application: 'nova',
			container: container,
			pages: [ 'harmony-rewards' ],
			hasBeenDismissed: false,
			dismissable: true,
			external_ref: {
				message_id: 'fake-message-id',
			},
			content: {
				name: `name-${uniqueId}`,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				validity: `validity-${uniqueId}`,
				icon: {
					image: {
						file: { url: `icon-${uniqueId}` },
					},
					altText: `iconAltText-${uniqueId}`,
				},
				image: {
					image: {
						file: { url: `image-${uniqueId}` },
					},
					imageDark: {
						file: { url: `imageDark-${uniqueId}` },
					},
					altText: `imageAltText-${uniqueId}`,
				},
			},
		});
		const offers = [
			fakeOffer('test-harmony-campaign', campaignPreviewTypes.harmonyCampaign, 'harmony-campaigns'),
			fakeOffer('test-harmony-reward', campaignPreviewTypes.harmonyReward, 'harmony-rewards'),
			fakeOffer('test-harmony-offer', campaignPreviewTypes.harmonyOffer, 'harmony-offers'),
		];

		const expectedOffer = (uniqueId, type, container) => ({
			[`${uniqueId}:fake-message-id`]: {
				id: uniqueId,
				messageId: 'fake-message-id',
				type,
				title: `title-${uniqueId}`,
				description: `description-${uniqueId}`,
				icon: `icon-${uniqueId}`,
				iconAltText: `iconAltText-${uniqueId}`,
				image: `image-${uniqueId}`,
				imageDark: `imageDark-${uniqueId}`,
				imageAltText: `imageAltText-${uniqueId}`,
				validity: `validity-${uniqueId}`,
				application: 'nova',
				container: container,
				page: 'harmony-rewards',
				hasBeenDismissed: false,
				dismissable: true,
			},
		});

		const expected = {
			offersList: {
				...expectedOffer('test-harmony-campaign', campaignPreviewTypes.harmonyCampaign, 'harmony-campaigns'),
				...expectedOffer('test-harmony-reward', campaignPreviewTypes.harmonyReward, 'harmony-rewards'),
				...expectedOffer('test-harmony-offer', campaignPreviewTypes.harmonyOffer, 'harmony-offers'),
			},
			offersSortOrder: [
				'test-harmony-campaign:fake-message-id',
				'test-harmony-reward:fake-message-id',
				'test-harmony-offer:fake-message-id',
			],
		};

		const actual = offersReducer(initialState, loadedOffers({ offers }));
		expect(actual).toEqual(expected);
	});
});
