import { expectSaga } from 'redux-saga-test-plan';

import { offersSaga, offerHandler } from '../offersSaga';
import { fetchOffers, fetchOffersSync, loadedOffers } from '../offersActions';

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

describe('offersSaga', () => {
	const mock = new MockAdapter(axios);
	const queryParams = { platform: 'ios', page: 'accounts' };

	it('loads offers', () => {
		const fakeOffer = (uniqueId) => ({
			id: uniqueId,
			type: 'campaign',
			content: {
				name: `name-${uniqueId}`,
				preview: {
					title: `title-${uniqueId}`,
					description: `description-${uniqueId}`,
					icon: {
						productImage: {
							name: `name-${uniqueId}`,
							image: `image-${uniqueId}`,
						},
					},
				},
			},
		});

		const offers = [
			fakeOffer('3fake'),
			fakeOffer('3creditCard'),
			fakeOffer('3bnsOffer'),
		];

		const offersResponse = {
			data: {
				items: offers,
			},
		};

		mock.onGet().replyOnce(200, JSON.stringify(offersResponse));

		return expectSaga(offersSaga)
			.dispatch({ type: 'FETCH_OFFERS', payload: { page: 'fake', container: 'faker', limit: '3', ...queryParams } })
			.take([ fetchOffers, fetchOffersSync ])
			.put(loadedOffers({ offers: offersResponse.data.items }))
			.silentRun();
	});

	it('returns empty array', () => {
		const offersResponse = {};

		mock.onGet().replyOnce(200, JSON.stringify(offersResponse));

		return expectSaga(offersSaga)
			.dispatch({ type: 'FETCH_OFFERS', payload: { page: 'fake', container: 'faker', limit: '3' } })
			.take([ fetchOffers, fetchOffersSync ])
			.put(loadedOffers({ offers: [] }))
			.silentRun();
	});

	it('handles a failed request', () => {
		const error = new Error('fake error message');
		mock.onGet().replyOnce(500, error);
		// fetch.mockReject(error);
		return expectSaga(offerHandler, fetchOffers('fake-page'))
			.put(loadedOffers({ offers: [] }))
			.run();
	});
});
