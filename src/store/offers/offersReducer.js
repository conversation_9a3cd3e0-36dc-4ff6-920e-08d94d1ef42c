import { handleActions } from 'redux-actions';

import { loadedOffers, setOfferDismissed, setOfferViewed, setOfferSnoozed } from './offersActions';

import { campaignPreviewTypes } from 'store/content/constants';

const initialState = {
	offersList: null,
	offersSortOrder: [],
};

const offersReducer = handleActions({
	[loadedOffers]: (state, action) => {
		const { payload: { offers } } = action;
		const order = state.offersSortOrder.length ? state.offersSortOrder : offers.map(offer => `${offer.id}:${offer.external_ref && offer.external_ref.message_id}`);
		return {
			...state,
			offersList: createOffersList(offers, order),
			offersSortOrder: order,
		};
	},
	[setOfferDismissed]: (state, action) => {
		const { id, messageId } = action.payload;
		return {
			...state,
			offersList: {
				...state.offersList,
				[`${id}:${messageId}`]: {
					...state.offersList[`${id}:${messageId}`],
					hasBeenDismissed: true,
				},
			},
		};
	},
	[setOfferSnoozed]: (state, action) => {
		const { id, messageId } = action.payload;
		return {
			...state,
			offersList: {
				...state.offersList,
				[`${id}:${messageId}`]: {
					...state.offersList[`${id}:${messageId}`],
					snoozed: true,
				},
			},
		};
	},
	[setOfferViewed]: (state, action) => {
		const { id, messageId } = action.payload;
		return {
			...state,
			offersList: {
				...state.offersList,
				[`${id}:${messageId}`]: {
					...state.offersList[`${id}:${messageId}`],
					viewed: true,
				},
			},
		};
	},
}, initialState);

const createOffersList = (offers, order) => {
	const offersList = {};
	const sortedOffers = order
		.map(uniqueId => offers.find(offer => `${offer.id}:${offer.external_ref && offer.external_ref.message_id}` === uniqueId))
		.filter(offer => offer);

	sortedOffers.forEach((offer) => {
		const { content } = offer;

		const messageId = offer.external_ref && offer.external_ref.message_id;
		let offerIdentifier = offer.id;
		if (messageId) {
			offerIdentifier += `:${messageId}`;
		}

		switch (offer.type) {
			case campaignPreviewTypes.targetedCampaign:
			case campaignPreviewTypes.iTradePriorityBoxPreview:
				offersList[offerIdentifier] = {
					id: offer.id,
					name: offer.name,
					messageId,
					title: content.title,
					description: content.description,
					image: content.image && content.image.image ? content.image.image.file.url : null,
					imageDark: content.image && content.image.imageDark ? content.image.imageDark.file.url : null,
					imageAltText: content.image ? content.image.altText : null,
					type: offer.type,
					ctaLink: content.ctaLink,
					application: offer.application,
					container: offer.container,
					page: offer.pages && offer.pages.length ? offer.pages[0] : null,
					dismissable: offer.dismissable,
					hasBeenDismissed: false,
					snoozed: false,
					viewed: offer.viewed,
				};
				break;
			case campaignPreviewTypes.atlantisPriorityPreview:
				offersList[offerIdentifier] = {
					id: offer.id,
					name: offer.name,
					messageId,
					title: content.title,
					description: content.description,
					image: content.image && content.image.image ? content.image.image.file.url : null,
					imageDark: content.image && content.image.imageDark ? content.image.imageDark.file.url : null,
					imageAltText: content.image ? content.image.altText : null,
					type: offer.type,
					ctaLink: content.ctaLink,
					application: offer.application,
					container: offer.container,
					page: offer.pages && offer.pages.length ? offer.pages[0] : null,
					dismissable: offer.dismissable,
					hasBeenDismissed: false,
					snoozed: false,
					viewed: offer.viewed,
				};
				break;
			case campaignPreviewTypes.standingCampaign:
				offersList[offerIdentifier] = {
					id: offer.id,
					name: offer.name,
					messageId,
					description: content.description,
					icon: content.icon,
					badge: content.badge,
					type: offer.type,
					linkUrl: content.linkAction ? content.linkAction.url : null,
				};
				break;
			case campaignPreviewTypes.harmonyReward:
			case campaignPreviewTypes.harmonyOffer:
			case campaignPreviewTypes.harmonyCampaign:
				offersList[offerIdentifier] = {
					id: offer.id,
					messageId,
					type: offer.type,
					application: offer.application,
					container: offer.container,
					page: offer.pages && offer.pages.length ? offer.pages[0] : null,
					title: content.title,
					description: content.description,
					validity: content.validity,
					icon: content.icon ? content.icon.image.file.url : null,
					iconAltText: content.icon ? content.icon.altText : null,
					image: content.image && content.image.image ? content.image.image.file.url : null,
					imageDark: content.image && content.image.imageDark ? content.image.imageDark.file.url : null,
					imageAltText: content.image ? content.image.altText : null,
					ctaLink: content.ctaLink,
					dismissable: offer.dismissable,
					hasBeenDismissed: false,
				};
				break;
		}
	});

	return offersList;
};

export default offersReducer;
