import { createAction } from 'redux-actions';
import { createPromiseAction } from '@adobe/redux-saga-promise';

export const fetchOffers = createAction('FETCH_OFFERS');
export const fetchOffersSync = createPromiseAction('FETCH_OFFERS_SYNCHRONOUS');
export const loadedOffers = createAction('LOADED_OFFERS');
export const offersLoadedFailed = createAction('OFFERS_LOADED_FAILED');
export const setOfferDismissed = createAction('SET_OFFER_DISMISSED');
export const setOfferViewed = createAction('SET_OFFER_VIEWED');
export const setOfferSnoozed = createAction('SET_OFFER_SNOOZED');
