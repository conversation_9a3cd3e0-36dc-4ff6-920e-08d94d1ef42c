/**
 * Combine all reducers in this file and export the combined reducers.
 */

import { combineReducers } from 'redux';
import { LOCATION_CHANGE } from 'react-router-redux';

import appReducer from 'store/app/appReducer';
import contentReducer from 'store/content/contentReducer';
import offersReducer from 'store/offers/offersReducer';
import errorReducer from 'store/errors/errorReducer';

/*
 * routeReducer
 *
 * The appReducer merges route location changes into our immutable state.
 * The change is necessitated by moving to react-router-redux@5
 *
 */

// Initial routing state
const routeInitialState = {
	location: null,
};

/**
 * Merge route into the global application state
 */
export function routeReducer(state, action) {
	const routeState = state || routeInitialState;
	return action.type === LOCATION_CHANGE ? { ...routeState, location: action.payload } : routeState;
}

/**
 * Creates the main appReducer with the dynamically injected ones
 */
export default function createReducer(injectedReducers) {
	return combineReducers({
		route: routeReducer,
		app: appReducer,
		content: contentReducer,
		offers: offersReducer,
		errors: errorReducer,
		...injectedReducers,
	});
}
