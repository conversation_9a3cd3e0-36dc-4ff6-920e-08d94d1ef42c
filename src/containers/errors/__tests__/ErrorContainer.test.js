import React from 'react';
import { shallow } from 'enzyme';
import { createMockStore } from 'redux-test-utils';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';

import { mountWithStore } from 'utils/testUtils';
import ErrorContainer, { Error } from 'containers/errors/ErrorContainer';

const sampleErrorMessages = (errorType) => ({
	[`errors.${errorType}.heading`]: `Test heading for ${errorType} error`,
	[`errors.${errorType}.description`]: `Test description for ${errorType} error`,
	'errors.cta.tryAgain': 'Try again',
});

describe('ErrorContainer.jsx', () => {
	it('should match the snapshot', () => {
		const errorStoreState = {
			errors: {
				'401': 'sample error',
			},
		};
		const store = createMockStore(errorStoreState);
		const wrapper = mountWithStore(
			<Provider store={store}>
				<IntlProvider
					locale="en"
					messages={sampleErrorMessages(401)}
				>
					<ErrorContainer />
				</IntlProvider>
			</Provider>, store);
		expect(wrapper).toMatchSnapshot();
	});

	it('should match the offline error snapshot', () => {
		const errorStoreState = {
			errors: {
				'offline': 'you are offline',
			},
		};
		const store = createMockStore(errorStoreState);
		const wrapper = mountWithStore(
			<Provider store={store}>
				<IntlProvider
					locale="en"
					messages={sampleErrorMessages('offline')}
				>
					<ErrorContainer />
				</IntlProvider>
			</Provider>, store);
		expect(wrapper).toMatchSnapshot();
	});

	it('reloads the page when the button is clicked', () => {
		// found this way to mock window properties: https://gist.github.com/remarkablemark/5cb571a13a6635ab89cf2bb47dc004a3
		// found this way to migrate the hack from jest 24 to 25 https://github.bns/facebook/jest/issues/9471
		Object.defineProperty(window, 'location', {
			writable: true,
			value: { assign: jest.fn() },
		});

		Object.defineProperty(window.location, 'reload', {
			writable: true,
			value: { assign: jest.fn() },
		});

		window.location.reload = jest.fn();

		const wrapper = shallow(<Error errorCode={'401'} />);

		wrapper.find('.errors__button').simulate('click');
		expect(window.location.reload).toHaveBeenCalled();
	});
});
