import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { FormattedMessage } from 'react-intl';
import SecondaryButton from 'canvas-core-react/lib/SecondaryButton';
import TextBody from 'canvas-core-react/lib/TextBody';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';

import { errorSelector } from 'store/errors/errorsSelectors';
import { UnauthorizedIcon } from 'components/icons';
import { trackError } from 'analytics';
import { getGUIDFromCookie } from 'utils/cookieUtils';

const mapStateToProps = (state) => ({
	error: errorSelector(state),
});

export const ErrorShape = PropTypes.shape({
	errorCode: PropTypes.string.isRequired,
});

@connect(mapStateToProps, null)
class ErrorContainer extends React.Component {
	static propTypes = {
		error: ErrorShape.isRequired,
	};

	async componentDidMount() {
		const { errorCode } = this.props.error;
		let guid;
		try {
			guid = await getGUIDFromCookie();
		} catch (err) {
			guid = 'guid not available';
		}
		trackError(errorCode, guid, document?.URL, window.analyticsEnv);
	}

	render() {
		const { errorCode } = this.props.error;
		return <Error errorCode={errorCode} />;
	}
}

export const Error = ({ errorCode }) => {
	const renderDescription = () => {
		if (errorCode === 'offline') {
			return (
				<FormattedMessage
					id="errors.offline.description"
					values={{
						br: <br />,
						tip: <strong><FormattedMessage id="errors.offline.descriptionHeading" /></strong>,
					}}
				/>
			);
		}

		return <FormattedMessage id={`errors.${errorCode}.description`} />;
	}

	return (
		<div className="errors">
			<TextSubtitle component="h1" color="black" type="1" className="errors__heading">
				<FormattedMessage id={`errors.${errorCode}.heading`}/>
			</TextSubtitle>
			<TextBody component="p" color="gray" type="2" className="errors__description">
				{ renderDescription() }
			</TextBody>
			{ errorCode !== '404' && (
				<SecondaryButton
					className="errors__button"
					onClick={() => window.navigator.onLine && window.location.reload()}
				>
					<FormattedMessage id={`errors.cta.tryAgain`}/>
				</SecondaryButton>
			)}
			<UnauthorizedIcon className="errors__image" />
		</div>
	);
}

Error.propTypes = {
	errorCode: PropTypes.string,
}

export default ErrorContainer;
