// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProtectedRoute matches the snapshot 1`] = `
<IntlProvider
  locale="en"
  messages={
    Object {
      "errors.401.description": "We couldn’t verify your credentials or your session has expired. Please try signing in again with a valid username and password.",
      "errors.401.heading": "Oops, you don’t have access to view this page.",
      "errors.404.description": "It may have been moved or is temporarily unavailable.",
      "errors.404.heading": "Sorry, we couldn’t find this page.",
      "errors.503.description": "We're working on getting it back up and running.",
      "errors.503.heading": "Sorry, this service is currently unavailable",
      "errors.cta.tryAgain": "Try again",
      "errors.offline.description": "{tip}{br}Turn on mobile data or Wi-Fi.{br}Turn off airplane mode",
      "errors.offline.descriptionHeading": "Tips:",
      "errors.offline.heading": "You're currently offline",
      "offers.activeOffersSubheading": "You have {offerText}",
      "offers.footNotesTitle": "View legal footnotes",
      "offers.learnMore": "Learn more",
      "offers.learnMoreAriaDescription": "Learn more about this offer",
      "offers.noOffers": "Check back soon for recommended offers and programs for you.",
      "offers.oneActiveOffer": "1 active offer.",
      "offers.otherActiveOffers": "{num} active offers.",
      "offers.recommendedOffers": "Here are offers and programs recommended for you.",
    }
  }
>
  <Provider
    store={
      Object {
        "dispatch": [Function],
        "getAction": [Function],
        "getActions": [Function],
        "getState": [Function],
        "isActionDispatched": [Function],
        "isActionTypeDispatched": [Function],
        "subscribe": [Function],
      }
    }
  >
    <BrowserRouter>
      <Router
        history={
          Object {
            "action": "POP",
            "block": [Function],
            "createHref": [Function],
            "go": [Function],
            "goBack": [Function],
            "goForward": [Function],
            "length": 1,
            "listen": [Function],
            "location": Object {
              "hash": "",
              "pathname": "/",
              "search": "",
              "state": undefined,
            },
            "push": [Function],
            "replace": [Function],
          }
        }
      >
        <Connect(ProtectedRoute)
          component={[Function]}
        >
          <ProtectedRoute
            component={[Function]}
            dispatch={[Function]}
            error={null}
            isLoading={false}
          >
            <Route
              dispatch={[Function]}
              render={[Function]}
            >
              <Connect(LoadingContainer)>
                <LoadingContainer
                  dispatch={[Function]}
                  isLoading={false}
                />
              </Connect(LoadingContainer)>
              <component
                history={
                  Object {
                    "action": "POP",
                    "block": [Function],
                    "createHref": [Function],
                    "go": [Function],
                    "goBack": [Function],
                    "goForward": [Function],
                    "length": 1,
                    "listen": [Function],
                    "location": Object {
                      "hash": "",
                      "pathname": "/",
                      "search": "",
                      "state": undefined,
                    },
                    "push": [Function],
                    "replace": [Function],
                  }
                }
                location={
                  Object {
                    "hash": "",
                    "pathname": "/",
                    "search": "",
                    "state": undefined,
                  }
                }
                match={
                  Object {
                    "isExact": true,
                    "params": Object {},
                    "path": "/",
                    "url": "/",
                  }
                }
              >
                <div>
                  Hello
                </div>
              </component>
            </Route>
          </ProtectedRoute>
        </Connect(ProtectedRoute)>
      </Router>
    </BrowserRouter>
  </Provider>
</IntlProvider>
`;

exports[`ProtectedRoute matches the snapshot with an error 1`] = `
<IntlProvider
  locale="en"
  messages={
    Object {
      "errors.401.description": "We couldn’t verify your credentials or your session has expired. Please try signing in again with a valid username and password.",
      "errors.401.heading": "Oops, you don’t have access to view this page.",
      "errors.404.description": "It may have been moved or is temporarily unavailable.",
      "errors.404.heading": "Sorry, we couldn’t find this page.",
      "errors.503.description": "We're working on getting it back up and running.",
      "errors.503.heading": "Sorry, this service is currently unavailable",
      "errors.cta.tryAgain": "Try again",
      "errors.offline.description": "{tip}{br}Turn on mobile data or Wi-Fi.{br}Turn off airplane mode",
      "errors.offline.descriptionHeading": "Tips:",
      "errors.offline.heading": "You're currently offline",
      "offers.activeOffersSubheading": "You have {offerText}",
      "offers.footNotesTitle": "View legal footnotes",
      "offers.learnMore": "Learn more",
      "offers.learnMoreAriaDescription": "Learn more about this offer",
      "offers.noOffers": "Check back soon for recommended offers and programs for you.",
      "offers.oneActiveOffer": "1 active offer.",
      "offers.otherActiveOffers": "{num} active offers.",
      "offers.recommendedOffers": "Here are offers and programs recommended for you.",
    }
  }
>
  <Provider
    store={
      Object {
        "dispatch": [Function],
        "getAction": [Function],
        "getActions": [Function],
        "getState": [Function],
        "isActionDispatched": [Function],
        "isActionTypeDispatched": [Function],
        "subscribe": [Function],
      }
    }
  >
    <BrowserRouter>
      <Router
        history={
          Object {
            "action": "POP",
            "block": [Function],
            "createHref": [Function],
            "go": [Function],
            "goBack": [Function],
            "goForward": [Function],
            "length": 1,
            "listen": [Function],
            "location": Object {
              "hash": "",
              "pathname": "/",
              "search": "",
              "state": undefined,
            },
            "push": [Function],
            "replace": [Function],
          }
        }
      >
        <Connect(ProtectedRoute)
          component={[Function]}
        >
          <ProtectedRoute
            component={[Function]}
            dispatch={[Function]}
            error={
              Object {
                "errorCode": "401",
                "errorMessage": "sample error",
              }
            }
            isLoading={false}
          >
            <Route
              dispatch={[Function]}
              render={[Function]}
            >
              <Connect(ErrorContainer)>
                <ErrorContainer
                  dispatch={[Function]}
                  error={
                    Object {
                      "errorCode": "401",
                      "errorMessage": "sample error",
                    }
                  }
                >
                  <_Error
                    errorCode="401"
                  >
                    <div
                      className="errors"
                    >
                      <f
                        className="errors__heading"
                        color="black"
                        component="h1"
                        italic={false}
                        type="1"
                      >
                        <TextSubtitlestyle__Text
                          as="h1"
                          className="TextSubtitle__text errors__heading"
                          color="black"
                          italic={false}
                          theme={
                            Object {
                              "breakPoints": Object {
                                "values": Object {
                                  "lg": 1025,
                                  "md": 768,
                                  "sm": 481,
                                  "xs": 0,
                                },
                              },
                              "palette": Object {
                                "actionMenuList": Object {
                                  "item": Object {
                                    "background": Object {
                                      "hover": "#F6F7FC",
                                    },
                                    "text": "#333",
                                  },
                                  "menuButton": Object {
                                    "background": Object {
                                      "hover": "#F6F7FC",
                                    },
                                    "border": Object {
                                      "focus": "#007EAB",
                                    },
                                    "text": "#333",
                                  },
                                },
                                "alertBanner": Object {
                                  "alert": Object {
                                    "background": "#FFF3EF",
                                    "border": "#FDB197",
                                  },
                                  "error": Object {
                                    "background": "#FEECED",
                                    "border": "#F5888C",
                                  },
                                  "info": Object {
                                    "background": "#EBF7FC",
                                    "border": "#7FCEEA",
                                  },
                                  "success": Object {
                                    "background": "#ECF5F3",
                                    "border": "#89C1B3",
                                  },
                                },
                                "backToTop": Object {
                                  "background": "#FFFFFF",
                                  "border": "#E2E8EE",
                                  "hover": "#949494",
                                },
                                "background": Object {
                                  "card": Object {
                                    "hover": "#949494",
                                    "keyline": "#E2E8EE",
                                    "primary": "#FFFFFF",
                                    "secondary": "#FFFFFF",
                                  },
                                  "modal": Object {
                                    "keyline": "#E2E8EE",
                                    "overlay": "rgba(0, 0, 0, 0.5)",
                                    "primary": "#FFFFFF",
                                  },
                                  "primary": "#FFFFFF",
                                },
                                "brandColors": Object {
                                  "dark": Object {
                                    "black": "#757575",
                                    "blue": "#91ddf8",
                                    "green": "#84d9c6",
                                    "orange": "#ffba8e",
                                    "pink": "#fda8de",
                                    "purple": "#aea9f4",
                                    "red": "#ff969c",
                                    "white": "#333333",
                                    "yellow": "#ffeaa5",
                                  },
                                  "light": Object {
                                    "black": "#333333",
                                    "blue": "#009dd6",
                                    "darkBlue": "#007EAB",
                                    "darkRed": "#BE061B",
                                    "green": "#138468",
                                    "orange": "#fb6330",
                                    "pink": "#f2609e",
                                    "purple": "#7849b8",
                                    "red": "#ec111a",
                                    "white": "#FFFFFF",
                                    "yellow": "#ffd42f",
                                  },
                                },
                                "button": Object {
                                  "close": Object {
                                    "color": "#333333",
                                    "hover": "#949494",
                                  },
                                  "disabled": Object {
                                    "background": "#F6F6F6",
                                    "border": "#D6D6D6",
                                    "color": "#757575",
                                  },
                                  "navigationButtons": Object {
                                    "backButtonColor": "#333333",
                                    "continueButtonColor": "#ec111a",
                                  },
                                  "pillButton": Object {
                                    "background": "transparent",
                                    "border": "#333333",
                                    "caution": Object {
                                      "color": "#ec111a",
                                    },
                                    "color": "#333333",
                                  },
                                  "primary": Object {
                                    "background": "#ec111a",
                                    "border": "#ec111a",
                                    "color": "#FFFFFF",
                                  },
                                  "secondary": Object {
                                    "background": "transparent",
                                    "border": "#ec111a",
                                    "color": "#ec111a",
                                  },
                                },
                                "canvasGrey": Object {
                                  "100": "#FAFBFD",
                                  "200": "#F6F7FC",
                                  "300": "#F6F6F6",
                                  "400": "#E2E8EE",
                                  "500": "#D6D6D6",
                                  "600": "#757575",
                                  "700": "#949494",
                                },
                                "canvasShadow": "rgba(0, 34, 91, 0.11)",
                                "charts": Object {
                                  "lineChart": Object {
                                    "axisLineColor": "#757575",
                                    "gridLineColor": "#D6D6D6",
                                    "tooltip": Object {
                                      "borderColor": "#949494",
                                    },
                                  },
                                  "stackedColumn": Object {
                                    "axisLineColor": "#757575",
                                    "dataSetColors": Object {
                                      "limitedRetail": Array [
                                        "#333333",
                                        "#009DD6",
                                        "#7849B8",
                                        "#138468",
                                        "#FB6330",
                                      ],
                                      "retail": Array [
                                        "#009DD6",
                                        "#7849B8",
                                        "#138468",
                                        "#F2609E",
                                        "#F2609E",
                                      ],
                                    },
                                    "gridLineColor": "#D6D6D6",
                                    "tooltip": Object {
                                      "borderColor": "#383838",
                                    },
                                  },
                                },
                                "checkbox": Object {
                                  "border": "#757575",
                                  "checked": "#007EAB",
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                  "indeterminate": "#949494",
                                },
                                "common": Object {
                                  "black": "#333333",
                                  "white": "#FFFFFF",
                                },
                                "error": "#be061b",
                                "focusOutline": "#007EAB",
                                "footer": Object {
                                  "base": Object {
                                    "background": "#F6F7FC",
                                  },
                                  "border": "#E2E8EE",
                                  "link": Object {
                                    "hover": "#333333",
                                  },
                                  "logo": Object {
                                    "fill": "#ec111a",
                                  },
                                },
                                "hamburger": Object {
                                  "menu": Object {
                                    "background": "#FFFFFF",
                                  },
                                },
                                "icons": Object {
                                  "category": Object {
                                    "circle": "#E2E8EE",
                                    "path": "#757575",
                                  },
                                  "functional": Object {
                                    "black": "#333333",
                                    "blue": "#007EAB",
                                    "darkBlue": "#007EAB",
                                    "darkRed": "#BE061B",
                                    "green": "#138468",
                                    "orange": "#fb6330",
                                    "pink": "#f2609e",
                                    "purple": "#7849b8",
                                    "red": "#ec111a",
                                    "white": "#FFFFFF",
                                    "yellow": "#ffd42f",
                                  },
                                },
                                "link": Object {
                                  "emphasis": Object {
                                    "color": "#007EAB",
                                    "hover": "#005E80",
                                  },
                                },
                                "loadingIndicator": Object {
                                  "background": "#949494",
                                },
                                "meter": Object {
                                  "borderColor": "#949494",
                                  "complete": "#138468",
                                  "incomplete": "#F6F6F6",
                                },
                                "pagination": Object {
                                  "disabled": "#949494",
                                  "selected": "#ec111a",
                                },
                                "pinTextField": Object {
                                  "pinItem": Object {
                                    "backgroundGray": "#F6F7FC",
                                  },
                                },
                                "primary": "#009dd6",
                                "quickActionCard": Object {
                                  "chevron": Object {
                                    "black": "#333333",
                                  },
                                },
                                "radio": Object {
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                },
                                "search": Object {
                                  "border": "#757575",
                                },
                                "snackBar": Object {
                                  "background": "#2c2c2e",
                                  "button": Object {
                                    "color": "#009DD6",
                                    "hover": "#05BCFF",
                                  },
                                  "color": "#FFFFFF",
                                },
                                "stepTracker": Object {
                                  "border": "#949494",
                                  "incompleteBackground": "#D6D6D6",
                                },
                                "tabs": Object {
                                  "border": "#383838",
                                  "hover": "#757575",
                                },
                                "text": Object {
                                  "disabled": "#949494",
                                  "highEmphasis": "#333333",
                                  "mediumEmphasis": "#757575",
                                  "placeholderText": "#949494",
                                },
                                "textArea": Object {
                                  "border": "#757575",
                                },
                                "toggleSwitch": Object {
                                  "iconOff": "#949494",
                                  "iconOn": "#007EAB",
                                },
                                "tooltip": Object {
                                  "color": Object {
                                    "blue": "#009dd6",
                                    "default": "#333333",
                                  },
                                },
                              },
                              "tokens": Object {
                                "action": Object {
                                  "focus": Object {
                                    "default": "#007EAB",
                                    "outline": "#009DD6",
                                  },
                                  "hover": Object {
                                    "background": Object {
                                      "actionMenuList": Object {
                                        "default": "rgba(226,232,238,0.5)",
                                      },
                                      "pillButton": Object {
                                        "caution": "#BE061B",
                                        "default": "#333333",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#BE061B",
                                        "inverted": "#333333",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#BE061B",
                                        "textOnly": "#333333",
                                      },
                                    },
                                    "border": Object {
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#BE061B",
                                        "textOnly": "transparent",
                                      },
                                    },
                                    "default": "#005E80",
                                    "text": Object {
                                      "pillButton": Object {
                                        "caution": "#FFFFFF",
                                        "default": "#FFFFFF",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#FFFFFF",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#FFFFFF",
                                        "red": "#FFFFFF",
                                        "textOnly": "#FFFFFF",
                                      },
                                      "snackbar": Object {
                                        "button": "#05BCFF",
                                      },
                                    },
                                  },
                                },
                                "background": Object {
                                  "alertBanner": Object {
                                    "alert": "rgba(251,99,48,0.08)",
                                    "error": "rgba(236,17,26,0.08)",
                                    "info": "rgba(0,157,214,0.08)",
                                    "new": "rgba(120,73,184,0.08)",
                                    "success": "rgba(19,132,104,0.08)",
                                  },
                                  "base": "#FFFFFF",
                                  "comboBox": Object {
                                    "default": "#FFFFFF",
                                    "listBoxItem": Object {
                                      "hover": "#F6F6F6",
                                    },
                                  },
                                  "footer": Object {
                                    "base": Object {
                                      "grey": "#F6F7FC",
                                      "white": "#FFFFFF",
                                    },
                                    "logo": Object {
                                      "default": "#EC111A",
                                    },
                                    "text": Object {
                                      "hover": "#333333",
                                    },
                                  },
                                  "header": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "white": "#FFFFFF",
                                  },
                                  "meter": Object {
                                    "default": "#138468",
                                    "incomplete": "#F6F6F6",
                                  },
                                  "modal": "#FFFFFF",
                                  "navigation": Object {
                                    "default": "#FFFFFF",
                                  },
                                  "overlay": "rgba(0, 0, 0, 0.5)",
                                  "pillButtonPrimary": Object {
                                    "default": "#EC111A",
                                    "inverted": "#FFFFFF",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "transparent",
                                    "red": "transparent",
                                    "textOnly": "transparent",
                                  },
                                  "primary": "#FFFFFF",
                                  "progressIndicator": Object {
                                    "bar": Object {
                                      "default": "#138468",
                                    },
                                    "container": Object {
                                      "default": "#F6F6F6",
                                    },
                                  },
                                  "secondary": "#FFFFFF",
                                  "skeleton": "#949494",
                                  "snackbar": "#2C2C2C",
                                  "statusBadge": Object {
                                    "default": "transparent",
                                    "emphasis": "transparent",
                                    "error": "transparent",
                                    "new": "#7849B8",
                                    "success": "#138468",
                                    "success-emphasis": "transparent",
                                  },
                                  "table": Object {
                                    "row": Object {
                                      "alternate": "#FAFBFD",
                                      "hover": "rgba(226, 232, 238, 0.4)",
                                      "selected": "#F7F7F8",
                                    },
                                  },
                                },
                                "border": Object {
                                  "alertBanner": Object {
                                    "alert": "rgba(251,99,48,0.5)",
                                    "error": "rgba(236,17,26,0.5)",
                                    "info": "rgba(0,157,214,0.5)",
                                    "new": "rgba(120,73,184,0.5)",
                                    "success": "rgba(19,132,104,0.5)",
                                  },
                                  "card": "#E2E8EE",
                                  "default": "#707070",
                                  "header": Object {
                                    "white": "#E2E8EE",
                                  },
                                  "meter": Object {
                                    "default": "#949494",
                                  },
                                  "pillButton": Object {
                                    "caution": "#EC111A",
                                    "default": "#333333",
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": "#EC111A",
                                    "inverted": "#FFFFFF",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "textOnly": "transparent",
                                  },
                                  "progressIndicator": Object {
                                    "container": Object {
                                      "default": "#949494",
                                    },
                                  },
                                  "skeleton": "#949494",
                                  "statusBadge": Object {
                                    "default": "#707070",
                                    "emphasis": "#007EAB",
                                    "error": "#BE061B",
                                    "new": "#7849B8",
                                    "success": "#138468",
                                    "success-emphasis": "#138468",
                                  },
                                },
                                "checkbox": Object {
                                  "border": "#757575",
                                  "card": Object {
                                    "border": Object {
                                      "focus": "#007EAB",
                                      "hover": "#949494",
                                    },
                                  },
                                  "checked": "#007EAB",
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                  "indeterminate": "#949494",
                                },
                                "closeButton": Object {
                                  "border": "#949494",
                                  "color": "#333333",
                                },
                                "fileUpload": Object {
                                  "background": "#007EAB",
                                  "border": "#007EAB",
                                  "color": "#007EAB",
                                },
                                "grid": Object {
                                  "breakPoints": Object {
                                    "lg": Object {
                                      "gutter": 3,
                                      "margin": "3.6rem",
                                      "maxCols": 12,
                                      "maxWidth": "4000px",
                                      "minWidth": "1025px",
                                      "rowMargin": "-1.8rem",
                                      "size": "lg",
                                    },
                                    "md": Object {
                                      "gutter": 3,
                                      "margin": Array [
                                        "3.6rem",
                                        "4.2rem",
                                        "4.8rem",
                                        "5.4rem",
                                      ],
                                      "maxCols": 12,
                                      "maxWidth": "1024px",
                                      "minWidth": "768px",
                                      "rowMargin": "-1.5rem",
                                      "size": "md",
                                    },
                                    "sm": Object {
                                      "gutter": 1.8,
                                      "margin": Array [
                                        "2.4rem",
                                        "3.0rem",
                                        "3.6rem",
                                        "4.2rem",
                                      ],
                                      "maxCols": 8,
                                      "maxWidth": "767px",
                                      "minWidth": "481px",
                                      "rowMargin": "-1.2rem",
                                      "size": "sm",
                                    },
                                    "xs": Object {
                                      "gutter": 1.8,
                                      "margin": Array [
                                        "1.8rem",
                                        "2.4rem",
                                        "3.0rem",
                                        "3.6rem",
                                      ],
                                      "maxCols": 4,
                                      "maxWidth": "480px",
                                      "minWidth": "0px",
                                      "rowMargin": "-0.6rem",
                                      "size": "xs",
                                    },
                                  },
                                  "containerMaxWidth": 1200,
                                  "containerMaxWidthMargins": 1272,
                                  "containerMinWidth": 320,
                                  "gridColumns": 12,
                                },
                                "icons": Object {
                                  "functional": Object {
                                    "black": "#333333",
                                    "blue": "#007EAB",
                                    "darkBlue": "#007EAB",
                                    "darkGray": "#707070",
                                    "darkRed": "#BE061B",
                                    "gray": "#949494",
                                    "green": "#138468",
                                    "orange": "#FB6330",
                                    "pink": "#F2609E",
                                    "purple": "#7849B8",
                                    "red": "#EC111A",
                                    "white": "#FFFFFF",
                                    "yellow": "#FFD42F",
                                  },
                                },
                                "loadingIndicator": Object {
                                  "darkGray": "#949494",
                                  "red": "#EC111A",
                                },
                                "mode": "light",
                                "palette": Object {
                                  "color": Object {
                                    "base": "#FFFFFF",
                                    "black": "#333333",
                                    "blue": "#009DD6",
                                    "brandRed": "#EC111A",
                                    "darkBlue": "#007EAB",
                                    "darkBlue100": "#005E80",
                                    "darkGreen": "#117E63",
                                    "darkRed": "#BE061B",
                                    "darkRed100": "#BE061B",
                                    "green": "#138468",
                                    "green100": "rgb(132,217,198,0.15)",
                                    "orange": "#FB6330",
                                    "pink": "#F2609E",
                                    "primaryRed": "#EC111A",
                                    "purple": "#7849B8",
                                    "yellow": "#FFD42F",
                                  },
                                  "gray": Object {
                                    "0": "#FFFFFF",
                                    "100": "#FAFBFD",
                                    "200": "#F6F7FC",
                                    "300": "#F6F6F6",
                                    "400": "#E2E8EE",
                                    "500": "#D6D6D6",
                                    "550": "#949494",
                                    "600": "#707070",
                                    "700": "#333333",
                                    "800": "#2C2C2C",
                                  },
                                },
                                "pillButtonPrimary": Object {
                                  "default": Object {
                                    "backgroundColor": "#EC111A",
                                    "borderColor": "#EC111A",
                                    "textColor": "#FFFFFF",
                                  },
                                  "hover": Object {
                                    "default": Object {
                                      "backgroundColor": "#BE061B",
                                      "borderColor": "#BE061B",
                                      "textColor": "#FFFFFF",
                                    },
                                    "inverted": Object {
                                      "backgroundColor": "#333333",
                                      "borderColor": "#333333",
                                      "textColor": "#FFFFFF",
                                    },
                                  },
                                  "inverted": Object {
                                    "backgroundColor": "#FFFFFF",
                                    "borderColor": "#FFFFFF",
                                    "textColor": "#333333",
                                  },
                                },
                                "primary": Object {
                                  "dark": "#BE061B",
                                  "main": "#EC111A",
                                },
                                "secondary": Object {
                                  "dark": "#005E80",
                                  "main": "#007EAB",
                                },
                                "secondaryButton": Object {
                                  "default": Object {
                                    "backgroundColor": "#FFFFFF",
                                    "borderColor": "#EC111A",
                                    "textColor": "#EC111A",
                                  },
                                  "hover": Object {
                                    "default": Object {
                                      "backgroundColor": "#BE061B",
                                      "borderColor": "#BE061B",
                                      "textColor": "#FFFFFF",
                                    },
                                  },
                                },
                                "sidesheet": Object {
                                  "overlay": Object {
                                    "background": "#FFFFFF",
                                  },
                                  "persistent": Object {
                                    "background": "#FFFFFF",
                                  },
                                },
                                "size": Object {
                                  "borderRadius": Object {
                                    "0": "10rem",
                                    "100": "0.4rem",
                                    "200": "0.8rem",
                                    "300": "1.2rem",
                                    "50": "0.2rem",
                                  },
                                  "borderWidth": Object {
                                    "1": "0.1rem",
                                    "2": "0.2rem",
                                    "3": "0.3rem",
                                    "5": "0.5rem",
                                  },
                                  "breakPoints": Object {
                                    "lg": "1024px",
                                    "md": "768px",
                                    "sm": "375px",
                                    "xs": "0px",
                                  },
                                  "sizing": Object {
                                    "24": "2.4rem",
                                    "32": "3.2rem",
                                    "36": "3.6rem",
                                    "42": "4.2rem",
                                    "44": "4.4rem",
                                    "48": "4.8rem",
                                    "54": "5.4rem",
                                    "60": "6.0rem",
                                    "72": "7.2rem",
                                  },
                                  "spacing": Object {
                                    "1": "0.6rem",
                                    "10": "6.0rem",
                                    "11": "6.6rem",
                                    "12": "7.2rem",
                                    "13": "7.8rem",
                                    "14": "8.4rem",
                                    "15": "9.0rem",
                                    "16": "9.6rem",
                                    "17": "10.2rem",
                                    "18": "10.8rem",
                                    "19": "11.4rem",
                                    "2": "1.2rem",
                                    "20": "12.0rem",
                                    "3": "1.8rem",
                                    "4": "2.4rem",
                                    "5": "3.0rem",
                                    "6": "3.6rem",
                                    "7": "4.2rem",
                                    "8": "4.8rem",
                                    "9": "5.4rem",
                                  },
                                },
                                "state": Object {
                                  "disabled": Object {
                                    "background": "#F6F6F6",
                                    "border": "#D6D6D6",
                                    "text": "#949494",
                                  },
                                  "error": Object {
                                    "default": "#BE061B",
                                  },
                                },
                                "text": Object {
                                  "highEmphasis": "#333333",
                                  "medEmphasis": "#707070",
                                  "pillButton": Object {
                                    "caution": "#EC111A",
                                    "default": "#333333",
                                    "inverted": "#333333",
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": "#FFFFFF",
                                    "inverted": "#333333",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "textOnly": "#333333",
                                  },
                                  "placeholder": "#707070",
                                  "secondaryButton": "#EC111A",
                                  "snackbar": Object {
                                    "button": "#009DD6",
                                    "content": "#E0E0E0",
                                  },
                                  "statusBadge": Object {
                                    "default": "#707070",
                                    "emphasis": "#007EAB",
                                    "error": "#BE061B",
                                    "new": "#FFFFFF",
                                    "success": "#FFFFFF",
                                    "success-emphasis": "#138468",
                                  },
                                },
                                "textArea": Object {
                                  "border": "#757575",
                                },
                                "textButton": Object {
                                  "color": Object {
                                    "focus": "#007EAB",
                                    "hover": "#005E80",
                                  },
                                },
                                "theme": "default",
                                "tooltip": Object {
                                  "color": Object {
                                    "blue": "#009DD6",
                                    "default": "#333333",
                                  },
                                },
                                "transform": Object {
                                  "elevation": Object {
                                    "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
                                  },
                                  "motion": Object {
                                    "100": "200ms",
                                    "150": "350ms",
                                    "200": "500ms",
                                    "300": "0.1s",
                                    "400": "0.2s",
                                    "500": "0.3s",
                                    "600": "0.4s",
                                    "700": "0.5s",
                                    "800": "0.6s",
                                  },
                                  "opacity": Object {
                                    "0": 0,
                                    "100": 1,
                                    "11": 0.11,
                                    "15": 0.15,
                                    "16": 0.16,
                                    "2": 0.02,
                                    "3": 0.03,
                                    "38": 0.38,
                                    "5": 0.05,
                                    "50": 0.5,
                                    "60": 0.6,
                                    "87": 0.87,
                                  },
                                },
                                "type": "light",
                                "typography": Object {
                                  "lineHeight": Object {
                                    "100": "1.8rem",
                                    "1000": "5.4rem",
                                    "200": "2.0rem",
                                    "300": "2.1rem",
                                    "400": "2.2rem",
                                    "500": "2.4rem",
                                    "600": "2.6rem",
                                    "700": "2.7rem",
                                    "800": "3.0rem",
                                    "850": "3.5rem",
                                    "900": "4.0rem",
                                    "910": "4.1rem",
                                  },
                                  "size": Object {
                                    "100": "1.2rem",
                                    "1000": "3.6rem",
                                    "1100": "4.8rem",
                                    "200": "1.4rem",
                                    "300": "1.6rem",
                                    "400": "1.8rem",
                                    "500": "2.0rem",
                                    "600": "2.1rem",
                                    "700": "2.4rem",
                                    "800": "2.8rem",
                                    "900": "3.2rem",
                                  },
                                  "weight": Object {
                                    "bold": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "boldItalics": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "headline": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "italic": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "legal": "\\"Scotia Legal\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "legalItalic": "\\"Scotia Legal Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "regular": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  },
                                },
                              },
                              "transitions": Object {
                                "duaration": Object {
                                  "sideSheetMainWrapper": "600ms",
                                },
                                "effect": Object {
                                  "easing": Object {
                                    "easeInOut": "ease-in-out",
                                  },
                                },
                              },
                              "type": "light",
                              "typography": Object {
                                "fontBoldFamily": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontBoldItalicFamily": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontHeadlineFamily": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontItalicFamily": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontLightFamily": "\\"Scotia Light\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontRegularFamily": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                              },
                            }
                          }
                          type="1"
                        >
                          <h1
                            className="TextSubtitlestyle__Text-canvas-core__sc-1l85m0x-0 boDPwx TextSubtitle__text errors__heading"
                            color="black"
                            type="1"
                          >
                            <FormattedMessage
                              id="errors.401.heading"
                              values={Object {}}
                            >
                              <span>
                                Oops, you don’t have access to view this page.
                              </span>
                            </FormattedMessage>
                          </h1>
                        </TextSubtitlestyle__Text>
                      </f>
                      <f
                        bold={false}
                        className="errors__description"
                        color="gray"
                        component="p"
                        italic={false}
                        numeric={false}
                        theme="light"
                        type="2"
                      >
                        <TextBodystyle__Text
                          as="p"
                          bold={false}
                          className="TextBody__text errors__description"
                          color="gray"
                          italic={false}
                          numeric={false}
                          theme={
                            Object {
                              "breakPoints": Object {
                                "values": Object {
                                  "lg": 1025,
                                  "md": 768,
                                  "sm": 481,
                                  "xs": 0,
                                },
                              },
                              "palette": Object {
                                "actionMenuList": Object {
                                  "item": Object {
                                    "background": Object {
                                      "hover": "#F6F7FC",
                                    },
                                    "text": "#333",
                                  },
                                  "menuButton": Object {
                                    "background": Object {
                                      "hover": "#F6F7FC",
                                    },
                                    "border": Object {
                                      "focus": "#007EAB",
                                    },
                                    "text": "#333",
                                  },
                                },
                                "alertBanner": Object {
                                  "alert": Object {
                                    "background": "#FFF3EF",
                                    "border": "#FDB197",
                                  },
                                  "error": Object {
                                    "background": "#FEECED",
                                    "border": "#F5888C",
                                  },
                                  "info": Object {
                                    "background": "#EBF7FC",
                                    "border": "#7FCEEA",
                                  },
                                  "success": Object {
                                    "background": "#ECF5F3",
                                    "border": "#89C1B3",
                                  },
                                },
                                "backToTop": Object {
                                  "background": "#FFFFFF",
                                  "border": "#E2E8EE",
                                  "hover": "#949494",
                                },
                                "background": Object {
                                  "card": Object {
                                    "hover": "#949494",
                                    "keyline": "#E2E8EE",
                                    "primary": "#FFFFFF",
                                    "secondary": "#FFFFFF",
                                  },
                                  "modal": Object {
                                    "keyline": "#E2E8EE",
                                    "overlay": "rgba(0, 0, 0, 0.5)",
                                    "primary": "#FFFFFF",
                                  },
                                  "primary": "#FFFFFF",
                                },
                                "brandColors": Object {
                                  "dark": Object {
                                    "black": "#757575",
                                    "blue": "#91ddf8",
                                    "green": "#84d9c6",
                                    "orange": "#ffba8e",
                                    "pink": "#fda8de",
                                    "purple": "#aea9f4",
                                    "red": "#ff969c",
                                    "white": "#333333",
                                    "yellow": "#ffeaa5",
                                  },
                                  "light": Object {
                                    "black": "#333333",
                                    "blue": "#009dd6",
                                    "darkBlue": "#007EAB",
                                    "darkRed": "#BE061B",
                                    "green": "#138468",
                                    "orange": "#fb6330",
                                    "pink": "#f2609e",
                                    "purple": "#7849b8",
                                    "red": "#ec111a",
                                    "white": "#FFFFFF",
                                    "yellow": "#ffd42f",
                                  },
                                },
                                "button": Object {
                                  "close": Object {
                                    "color": "#333333",
                                    "hover": "#949494",
                                  },
                                  "disabled": Object {
                                    "background": "#F6F6F6",
                                    "border": "#D6D6D6",
                                    "color": "#757575",
                                  },
                                  "navigationButtons": Object {
                                    "backButtonColor": "#333333",
                                    "continueButtonColor": "#ec111a",
                                  },
                                  "pillButton": Object {
                                    "background": "transparent",
                                    "border": "#333333",
                                    "caution": Object {
                                      "color": "#ec111a",
                                    },
                                    "color": "#333333",
                                  },
                                  "primary": Object {
                                    "background": "#ec111a",
                                    "border": "#ec111a",
                                    "color": "#FFFFFF",
                                  },
                                  "secondary": Object {
                                    "background": "transparent",
                                    "border": "#ec111a",
                                    "color": "#ec111a",
                                  },
                                },
                                "canvasGrey": Object {
                                  "100": "#FAFBFD",
                                  "200": "#F6F7FC",
                                  "300": "#F6F6F6",
                                  "400": "#E2E8EE",
                                  "500": "#D6D6D6",
                                  "600": "#757575",
                                  "700": "#949494",
                                },
                                "canvasShadow": "rgba(0, 34, 91, 0.11)",
                                "charts": Object {
                                  "lineChart": Object {
                                    "axisLineColor": "#757575",
                                    "gridLineColor": "#D6D6D6",
                                    "tooltip": Object {
                                      "borderColor": "#949494",
                                    },
                                  },
                                  "stackedColumn": Object {
                                    "axisLineColor": "#757575",
                                    "dataSetColors": Object {
                                      "limitedRetail": Array [
                                        "#333333",
                                        "#009DD6",
                                        "#7849B8",
                                        "#138468",
                                        "#FB6330",
                                      ],
                                      "retail": Array [
                                        "#009DD6",
                                        "#7849B8",
                                        "#138468",
                                        "#F2609E",
                                        "#F2609E",
                                      ],
                                    },
                                    "gridLineColor": "#D6D6D6",
                                    "tooltip": Object {
                                      "borderColor": "#383838",
                                    },
                                  },
                                },
                                "checkbox": Object {
                                  "border": "#757575",
                                  "checked": "#007EAB",
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                  "indeterminate": "#949494",
                                },
                                "common": Object {
                                  "black": "#333333",
                                  "white": "#FFFFFF",
                                },
                                "error": "#be061b",
                                "focusOutline": "#007EAB",
                                "footer": Object {
                                  "base": Object {
                                    "background": "#F6F7FC",
                                  },
                                  "border": "#E2E8EE",
                                  "link": Object {
                                    "hover": "#333333",
                                  },
                                  "logo": Object {
                                    "fill": "#ec111a",
                                  },
                                },
                                "hamburger": Object {
                                  "menu": Object {
                                    "background": "#FFFFFF",
                                  },
                                },
                                "icons": Object {
                                  "category": Object {
                                    "circle": "#E2E8EE",
                                    "path": "#757575",
                                  },
                                  "functional": Object {
                                    "black": "#333333",
                                    "blue": "#007EAB",
                                    "darkBlue": "#007EAB",
                                    "darkRed": "#BE061B",
                                    "green": "#138468",
                                    "orange": "#fb6330",
                                    "pink": "#f2609e",
                                    "purple": "#7849b8",
                                    "red": "#ec111a",
                                    "white": "#FFFFFF",
                                    "yellow": "#ffd42f",
                                  },
                                },
                                "link": Object {
                                  "emphasis": Object {
                                    "color": "#007EAB",
                                    "hover": "#005E80",
                                  },
                                },
                                "loadingIndicator": Object {
                                  "background": "#949494",
                                },
                                "meter": Object {
                                  "borderColor": "#949494",
                                  "complete": "#138468",
                                  "incomplete": "#F6F6F6",
                                },
                                "pagination": Object {
                                  "disabled": "#949494",
                                  "selected": "#ec111a",
                                },
                                "pinTextField": Object {
                                  "pinItem": Object {
                                    "backgroundGray": "#F6F7FC",
                                  },
                                },
                                "primary": "#009dd6",
                                "quickActionCard": Object {
                                  "chevron": Object {
                                    "black": "#333333",
                                  },
                                },
                                "radio": Object {
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                },
                                "search": Object {
                                  "border": "#757575",
                                },
                                "snackBar": Object {
                                  "background": "#2c2c2e",
                                  "button": Object {
                                    "color": "#009DD6",
                                    "hover": "#05BCFF",
                                  },
                                  "color": "#FFFFFF",
                                },
                                "stepTracker": Object {
                                  "border": "#949494",
                                  "incompleteBackground": "#D6D6D6",
                                },
                                "tabs": Object {
                                  "border": "#383838",
                                  "hover": "#757575",
                                },
                                "text": Object {
                                  "disabled": "#949494",
                                  "highEmphasis": "#333333",
                                  "mediumEmphasis": "#757575",
                                  "placeholderText": "#949494",
                                },
                                "textArea": Object {
                                  "border": "#757575",
                                },
                                "toggleSwitch": Object {
                                  "iconOff": "#949494",
                                  "iconOn": "#007EAB",
                                },
                                "tooltip": Object {
                                  "color": Object {
                                    "blue": "#009dd6",
                                    "default": "#333333",
                                  },
                                },
                              },
                              "tokens": Object {
                                "action": Object {
                                  "focus": Object {
                                    "default": "#007EAB",
                                    "outline": "#009DD6",
                                  },
                                  "hover": Object {
                                    "background": Object {
                                      "actionMenuList": Object {
                                        "default": "rgba(226,232,238,0.5)",
                                      },
                                      "pillButton": Object {
                                        "caution": "#BE061B",
                                        "default": "#333333",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#BE061B",
                                        "inverted": "#333333",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#BE061B",
                                        "textOnly": "#333333",
                                      },
                                    },
                                    "border": Object {
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#BE061B",
                                        "textOnly": "transparent",
                                      },
                                    },
                                    "default": "#005E80",
                                    "text": Object {
                                      "pillButton": Object {
                                        "caution": "#FFFFFF",
                                        "default": "#FFFFFF",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#FFFFFF",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#FFFFFF",
                                        "red": "#FFFFFF",
                                        "textOnly": "#FFFFFF",
                                      },
                                      "snackbar": Object {
                                        "button": "#05BCFF",
                                      },
                                    },
                                  },
                                },
                                "background": Object {
                                  "alertBanner": Object {
                                    "alert": "rgba(251,99,48,0.08)",
                                    "error": "rgba(236,17,26,0.08)",
                                    "info": "rgba(0,157,214,0.08)",
                                    "new": "rgba(120,73,184,0.08)",
                                    "success": "rgba(19,132,104,0.08)",
                                  },
                                  "base": "#FFFFFF",
                                  "comboBox": Object {
                                    "default": "#FFFFFF",
                                    "listBoxItem": Object {
                                      "hover": "#F6F6F6",
                                    },
                                  },
                                  "footer": Object {
                                    "base": Object {
                                      "grey": "#F6F7FC",
                                      "white": "#FFFFFF",
                                    },
                                    "logo": Object {
                                      "default": "#EC111A",
                                    },
                                    "text": Object {
                                      "hover": "#333333",
                                    },
                                  },
                                  "header": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "white": "#FFFFFF",
                                  },
                                  "meter": Object {
                                    "default": "#138468",
                                    "incomplete": "#F6F6F6",
                                  },
                                  "modal": "#FFFFFF",
                                  "navigation": Object {
                                    "default": "#FFFFFF",
                                  },
                                  "overlay": "rgba(0, 0, 0, 0.5)",
                                  "pillButtonPrimary": Object {
                                    "default": "#EC111A",
                                    "inverted": "#FFFFFF",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "transparent",
                                    "red": "transparent",
                                    "textOnly": "transparent",
                                  },
                                  "primary": "#FFFFFF",
                                  "progressIndicator": Object {
                                    "bar": Object {
                                      "default": "#138468",
                                    },
                                    "container": Object {
                                      "default": "#F6F6F6",
                                    },
                                  },
                                  "secondary": "#FFFFFF",
                                  "skeleton": "#949494",
                                  "snackbar": "#2C2C2C",
                                  "statusBadge": Object {
                                    "default": "transparent",
                                    "emphasis": "transparent",
                                    "error": "transparent",
                                    "new": "#7849B8",
                                    "success": "#138468",
                                    "success-emphasis": "transparent",
                                  },
                                  "table": Object {
                                    "row": Object {
                                      "alternate": "#FAFBFD",
                                      "hover": "rgba(226, 232, 238, 0.4)",
                                      "selected": "#F7F7F8",
                                    },
                                  },
                                },
                                "border": Object {
                                  "alertBanner": Object {
                                    "alert": "rgba(251,99,48,0.5)",
                                    "error": "rgba(236,17,26,0.5)",
                                    "info": "rgba(0,157,214,0.5)",
                                    "new": "rgba(120,73,184,0.5)",
                                    "success": "rgba(19,132,104,0.5)",
                                  },
                                  "card": "#E2E8EE",
                                  "default": "#707070",
                                  "header": Object {
                                    "white": "#E2E8EE",
                                  },
                                  "meter": Object {
                                    "default": "#949494",
                                  },
                                  "pillButton": Object {
                                    "caution": "#EC111A",
                                    "default": "#333333",
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": "#EC111A",
                                    "inverted": "#FFFFFF",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "textOnly": "transparent",
                                  },
                                  "progressIndicator": Object {
                                    "container": Object {
                                      "default": "#949494",
                                    },
                                  },
                                  "skeleton": "#949494",
                                  "statusBadge": Object {
                                    "default": "#707070",
                                    "emphasis": "#007EAB",
                                    "error": "#BE061B",
                                    "new": "#7849B8",
                                    "success": "#138468",
                                    "success-emphasis": "#138468",
                                  },
                                },
                                "checkbox": Object {
                                  "border": "#757575",
                                  "card": Object {
                                    "border": Object {
                                      "focus": "#007EAB",
                                      "hover": "#949494",
                                    },
                                  },
                                  "checked": "#007EAB",
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                  "indeterminate": "#949494",
                                },
                                "closeButton": Object {
                                  "border": "#949494",
                                  "color": "#333333",
                                },
                                "fileUpload": Object {
                                  "background": "#007EAB",
                                  "border": "#007EAB",
                                  "color": "#007EAB",
                                },
                                "grid": Object {
                                  "breakPoints": Object {
                                    "lg": Object {
                                      "gutter": 3,
                                      "margin": "3.6rem",
                                      "maxCols": 12,
                                      "maxWidth": "4000px",
                                      "minWidth": "1025px",
                                      "rowMargin": "-1.8rem",
                                      "size": "lg",
                                    },
                                    "md": Object {
                                      "gutter": 3,
                                      "margin": Array [
                                        "3.6rem",
                                        "4.2rem",
                                        "4.8rem",
                                        "5.4rem",
                                      ],
                                      "maxCols": 12,
                                      "maxWidth": "1024px",
                                      "minWidth": "768px",
                                      "rowMargin": "-1.5rem",
                                      "size": "md",
                                    },
                                    "sm": Object {
                                      "gutter": 1.8,
                                      "margin": Array [
                                        "2.4rem",
                                        "3.0rem",
                                        "3.6rem",
                                        "4.2rem",
                                      ],
                                      "maxCols": 8,
                                      "maxWidth": "767px",
                                      "minWidth": "481px",
                                      "rowMargin": "-1.2rem",
                                      "size": "sm",
                                    },
                                    "xs": Object {
                                      "gutter": 1.8,
                                      "margin": Array [
                                        "1.8rem",
                                        "2.4rem",
                                        "3.0rem",
                                        "3.6rem",
                                      ],
                                      "maxCols": 4,
                                      "maxWidth": "480px",
                                      "minWidth": "0px",
                                      "rowMargin": "-0.6rem",
                                      "size": "xs",
                                    },
                                  },
                                  "containerMaxWidth": 1200,
                                  "containerMaxWidthMargins": 1272,
                                  "containerMinWidth": 320,
                                  "gridColumns": 12,
                                },
                                "icons": Object {
                                  "functional": Object {
                                    "black": "#333333",
                                    "blue": "#007EAB",
                                    "darkBlue": "#007EAB",
                                    "darkGray": "#707070",
                                    "darkRed": "#BE061B",
                                    "gray": "#949494",
                                    "green": "#138468",
                                    "orange": "#FB6330",
                                    "pink": "#F2609E",
                                    "purple": "#7849B8",
                                    "red": "#EC111A",
                                    "white": "#FFFFFF",
                                    "yellow": "#FFD42F",
                                  },
                                },
                                "loadingIndicator": Object {
                                  "darkGray": "#949494",
                                  "red": "#EC111A",
                                },
                                "mode": "light",
                                "palette": Object {
                                  "color": Object {
                                    "base": "#FFFFFF",
                                    "black": "#333333",
                                    "blue": "#009DD6",
                                    "brandRed": "#EC111A",
                                    "darkBlue": "#007EAB",
                                    "darkBlue100": "#005E80",
                                    "darkGreen": "#117E63",
                                    "darkRed": "#BE061B",
                                    "darkRed100": "#BE061B",
                                    "green": "#138468",
                                    "green100": "rgb(132,217,198,0.15)",
                                    "orange": "#FB6330",
                                    "pink": "#F2609E",
                                    "primaryRed": "#EC111A",
                                    "purple": "#7849B8",
                                    "yellow": "#FFD42F",
                                  },
                                  "gray": Object {
                                    "0": "#FFFFFF",
                                    "100": "#FAFBFD",
                                    "200": "#F6F7FC",
                                    "300": "#F6F6F6",
                                    "400": "#E2E8EE",
                                    "500": "#D6D6D6",
                                    "550": "#949494",
                                    "600": "#707070",
                                    "700": "#333333",
                                    "800": "#2C2C2C",
                                  },
                                },
                                "pillButtonPrimary": Object {
                                  "default": Object {
                                    "backgroundColor": "#EC111A",
                                    "borderColor": "#EC111A",
                                    "textColor": "#FFFFFF",
                                  },
                                  "hover": Object {
                                    "default": Object {
                                      "backgroundColor": "#BE061B",
                                      "borderColor": "#BE061B",
                                      "textColor": "#FFFFFF",
                                    },
                                    "inverted": Object {
                                      "backgroundColor": "#333333",
                                      "borderColor": "#333333",
                                      "textColor": "#FFFFFF",
                                    },
                                  },
                                  "inverted": Object {
                                    "backgroundColor": "#FFFFFF",
                                    "borderColor": "#FFFFFF",
                                    "textColor": "#333333",
                                  },
                                },
                                "primary": Object {
                                  "dark": "#BE061B",
                                  "main": "#EC111A",
                                },
                                "secondary": Object {
                                  "dark": "#005E80",
                                  "main": "#007EAB",
                                },
                                "secondaryButton": Object {
                                  "default": Object {
                                    "backgroundColor": "#FFFFFF",
                                    "borderColor": "#EC111A",
                                    "textColor": "#EC111A",
                                  },
                                  "hover": Object {
                                    "default": Object {
                                      "backgroundColor": "#BE061B",
                                      "borderColor": "#BE061B",
                                      "textColor": "#FFFFFF",
                                    },
                                  },
                                },
                                "sidesheet": Object {
                                  "overlay": Object {
                                    "background": "#FFFFFF",
                                  },
                                  "persistent": Object {
                                    "background": "#FFFFFF",
                                  },
                                },
                                "size": Object {
                                  "borderRadius": Object {
                                    "0": "10rem",
                                    "100": "0.4rem",
                                    "200": "0.8rem",
                                    "300": "1.2rem",
                                    "50": "0.2rem",
                                  },
                                  "borderWidth": Object {
                                    "1": "0.1rem",
                                    "2": "0.2rem",
                                    "3": "0.3rem",
                                    "5": "0.5rem",
                                  },
                                  "breakPoints": Object {
                                    "lg": "1024px",
                                    "md": "768px",
                                    "sm": "375px",
                                    "xs": "0px",
                                  },
                                  "sizing": Object {
                                    "24": "2.4rem",
                                    "32": "3.2rem",
                                    "36": "3.6rem",
                                    "42": "4.2rem",
                                    "44": "4.4rem",
                                    "48": "4.8rem",
                                    "54": "5.4rem",
                                    "60": "6.0rem",
                                    "72": "7.2rem",
                                  },
                                  "spacing": Object {
                                    "1": "0.6rem",
                                    "10": "6.0rem",
                                    "11": "6.6rem",
                                    "12": "7.2rem",
                                    "13": "7.8rem",
                                    "14": "8.4rem",
                                    "15": "9.0rem",
                                    "16": "9.6rem",
                                    "17": "10.2rem",
                                    "18": "10.8rem",
                                    "19": "11.4rem",
                                    "2": "1.2rem",
                                    "20": "12.0rem",
                                    "3": "1.8rem",
                                    "4": "2.4rem",
                                    "5": "3.0rem",
                                    "6": "3.6rem",
                                    "7": "4.2rem",
                                    "8": "4.8rem",
                                    "9": "5.4rem",
                                  },
                                },
                                "state": Object {
                                  "disabled": Object {
                                    "background": "#F6F6F6",
                                    "border": "#D6D6D6",
                                    "text": "#949494",
                                  },
                                  "error": Object {
                                    "default": "#BE061B",
                                  },
                                },
                                "text": Object {
                                  "highEmphasis": "#333333",
                                  "medEmphasis": "#707070",
                                  "pillButton": Object {
                                    "caution": "#EC111A",
                                    "default": "#333333",
                                    "inverted": "#333333",
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": "#FFFFFF",
                                    "inverted": "#333333",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "textOnly": "#333333",
                                  },
                                  "placeholder": "#707070",
                                  "secondaryButton": "#EC111A",
                                  "snackbar": Object {
                                    "button": "#009DD6",
                                    "content": "#E0E0E0",
                                  },
                                  "statusBadge": Object {
                                    "default": "#707070",
                                    "emphasis": "#007EAB",
                                    "error": "#BE061B",
                                    "new": "#FFFFFF",
                                    "success": "#FFFFFF",
                                    "success-emphasis": "#138468",
                                  },
                                },
                                "textArea": Object {
                                  "border": "#757575",
                                },
                                "textButton": Object {
                                  "color": Object {
                                    "focus": "#007EAB",
                                    "hover": "#005E80",
                                  },
                                },
                                "theme": "default",
                                "tooltip": Object {
                                  "color": Object {
                                    "blue": "#009DD6",
                                    "default": "#333333",
                                  },
                                },
                                "transform": Object {
                                  "elevation": Object {
                                    "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
                                  },
                                  "motion": Object {
                                    "100": "200ms",
                                    "150": "350ms",
                                    "200": "500ms",
                                    "300": "0.1s",
                                    "400": "0.2s",
                                    "500": "0.3s",
                                    "600": "0.4s",
                                    "700": "0.5s",
                                    "800": "0.6s",
                                  },
                                  "opacity": Object {
                                    "0": 0,
                                    "100": 1,
                                    "11": 0.11,
                                    "15": 0.15,
                                    "16": 0.16,
                                    "2": 0.02,
                                    "3": 0.03,
                                    "38": 0.38,
                                    "5": 0.05,
                                    "50": 0.5,
                                    "60": 0.6,
                                    "87": 0.87,
                                  },
                                },
                                "type": "light",
                                "typography": Object {
                                  "lineHeight": Object {
                                    "100": "1.8rem",
                                    "1000": "5.4rem",
                                    "200": "2.0rem",
                                    "300": "2.1rem",
                                    "400": "2.2rem",
                                    "500": "2.4rem",
                                    "600": "2.6rem",
                                    "700": "2.7rem",
                                    "800": "3.0rem",
                                    "850": "3.5rem",
                                    "900": "4.0rem",
                                    "910": "4.1rem",
                                  },
                                  "size": Object {
                                    "100": "1.2rem",
                                    "1000": "3.6rem",
                                    "1100": "4.8rem",
                                    "200": "1.4rem",
                                    "300": "1.6rem",
                                    "400": "1.8rem",
                                    "500": "2.0rem",
                                    "600": "2.1rem",
                                    "700": "2.4rem",
                                    "800": "2.8rem",
                                    "900": "3.2rem",
                                  },
                                  "weight": Object {
                                    "bold": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "boldItalics": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "headline": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "italic": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "legal": "\\"Scotia Legal\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "legalItalic": "\\"Scotia Legal Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "regular": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  },
                                },
                              },
                              "transitions": Object {
                                "duaration": Object {
                                  "sideSheetMainWrapper": "600ms",
                                },
                                "effect": Object {
                                  "easing": Object {
                                    "easeInOut": "ease-in-out",
                                  },
                                },
                              },
                              "type": "light",
                              "typography": Object {
                                "fontBoldFamily": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontBoldItalicFamily": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontHeadlineFamily": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontItalicFamily": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontLightFamily": "\\"Scotia Light\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontRegularFamily": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                              },
                            }
                          }
                          type="2"
                        >
                          <p
                            className="TextBodystyle__Text-canvas-core__sc-xx5i8s-0 dJzUEf TextBody__text errors__description"
                            color="gray"
                            type="2"
                          >
                            <FormattedMessage
                              id="errors.401.description"
                              values={Object {}}
                            >
                              <span>
                                We couldn’t verify your credentials or your session has expired. Please try signing in again with a valid username and password.
                              </span>
                            </FormattedMessage>
                          </p>
                        </TextBodystyle__Text>
                      </f>
                      <d
                        className="errors__button"
                        disabled={false}
                        labelPadding={36}
                        onClick={[Function]}
                        size="regular"
                      >
                        <SecondaryButtonstyle__StyleSecondaryButtonCore
                          className="Button__button--secondary errors__button"
                          disabled={false}
                          labelPadding={36}
                          onClick={[Function]}
                          size="regular"
                          theme={
                            Object {
                              "breakPoints": Object {
                                "values": Object {
                                  "lg": 1025,
                                  "md": 768,
                                  "sm": 481,
                                  "xs": 0,
                                },
                              },
                              "palette": Object {
                                "actionMenuList": Object {
                                  "item": Object {
                                    "background": Object {
                                      "hover": "#F6F7FC",
                                    },
                                    "text": "#333",
                                  },
                                  "menuButton": Object {
                                    "background": Object {
                                      "hover": "#F6F7FC",
                                    },
                                    "border": Object {
                                      "focus": "#007EAB",
                                    },
                                    "text": "#333",
                                  },
                                },
                                "alertBanner": Object {
                                  "alert": Object {
                                    "background": "#FFF3EF",
                                    "border": "#FDB197",
                                  },
                                  "error": Object {
                                    "background": "#FEECED",
                                    "border": "#F5888C",
                                  },
                                  "info": Object {
                                    "background": "#EBF7FC",
                                    "border": "#7FCEEA",
                                  },
                                  "success": Object {
                                    "background": "#ECF5F3",
                                    "border": "#89C1B3",
                                  },
                                },
                                "backToTop": Object {
                                  "background": "#FFFFFF",
                                  "border": "#E2E8EE",
                                  "hover": "#949494",
                                },
                                "background": Object {
                                  "card": Object {
                                    "hover": "#949494",
                                    "keyline": "#E2E8EE",
                                    "primary": "#FFFFFF",
                                    "secondary": "#FFFFFF",
                                  },
                                  "modal": Object {
                                    "keyline": "#E2E8EE",
                                    "overlay": "rgba(0, 0, 0, 0.5)",
                                    "primary": "#FFFFFF",
                                  },
                                  "primary": "#FFFFFF",
                                },
                                "brandColors": Object {
                                  "dark": Object {
                                    "black": "#757575",
                                    "blue": "#91ddf8",
                                    "green": "#84d9c6",
                                    "orange": "#ffba8e",
                                    "pink": "#fda8de",
                                    "purple": "#aea9f4",
                                    "red": "#ff969c",
                                    "white": "#333333",
                                    "yellow": "#ffeaa5",
                                  },
                                  "light": Object {
                                    "black": "#333333",
                                    "blue": "#009dd6",
                                    "darkBlue": "#007EAB",
                                    "darkRed": "#BE061B",
                                    "green": "#138468",
                                    "orange": "#fb6330",
                                    "pink": "#f2609e",
                                    "purple": "#7849b8",
                                    "red": "#ec111a",
                                    "white": "#FFFFFF",
                                    "yellow": "#ffd42f",
                                  },
                                },
                                "button": Object {
                                  "close": Object {
                                    "color": "#333333",
                                    "hover": "#949494",
                                  },
                                  "disabled": Object {
                                    "background": "#F6F6F6",
                                    "border": "#D6D6D6",
                                    "color": "#757575",
                                  },
                                  "navigationButtons": Object {
                                    "backButtonColor": "#333333",
                                    "continueButtonColor": "#ec111a",
                                  },
                                  "pillButton": Object {
                                    "background": "transparent",
                                    "border": "#333333",
                                    "caution": Object {
                                      "color": "#ec111a",
                                    },
                                    "color": "#333333",
                                  },
                                  "primary": Object {
                                    "background": "#ec111a",
                                    "border": "#ec111a",
                                    "color": "#FFFFFF",
                                  },
                                  "secondary": Object {
                                    "background": "transparent",
                                    "border": "#ec111a",
                                    "color": "#ec111a",
                                  },
                                },
                                "canvasGrey": Object {
                                  "100": "#FAFBFD",
                                  "200": "#F6F7FC",
                                  "300": "#F6F6F6",
                                  "400": "#E2E8EE",
                                  "500": "#D6D6D6",
                                  "600": "#757575",
                                  "700": "#949494",
                                },
                                "canvasShadow": "rgba(0, 34, 91, 0.11)",
                                "charts": Object {
                                  "lineChart": Object {
                                    "axisLineColor": "#757575",
                                    "gridLineColor": "#D6D6D6",
                                    "tooltip": Object {
                                      "borderColor": "#949494",
                                    },
                                  },
                                  "stackedColumn": Object {
                                    "axisLineColor": "#757575",
                                    "dataSetColors": Object {
                                      "limitedRetail": Array [
                                        "#333333",
                                        "#009DD6",
                                        "#7849B8",
                                        "#138468",
                                        "#FB6330",
                                      ],
                                      "retail": Array [
                                        "#009DD6",
                                        "#7849B8",
                                        "#138468",
                                        "#F2609E",
                                        "#F2609E",
                                      ],
                                    },
                                    "gridLineColor": "#D6D6D6",
                                    "tooltip": Object {
                                      "borderColor": "#383838",
                                    },
                                  },
                                },
                                "checkbox": Object {
                                  "border": "#757575",
                                  "checked": "#007EAB",
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                  "indeterminate": "#949494",
                                },
                                "common": Object {
                                  "black": "#333333",
                                  "white": "#FFFFFF",
                                },
                                "error": "#be061b",
                                "focusOutline": "#007EAB",
                                "footer": Object {
                                  "base": Object {
                                    "background": "#F6F7FC",
                                  },
                                  "border": "#E2E8EE",
                                  "link": Object {
                                    "hover": "#333333",
                                  },
                                  "logo": Object {
                                    "fill": "#ec111a",
                                  },
                                },
                                "hamburger": Object {
                                  "menu": Object {
                                    "background": "#FFFFFF",
                                  },
                                },
                                "icons": Object {
                                  "category": Object {
                                    "circle": "#E2E8EE",
                                    "path": "#757575",
                                  },
                                  "functional": Object {
                                    "black": "#333333",
                                    "blue": "#007EAB",
                                    "darkBlue": "#007EAB",
                                    "darkRed": "#BE061B",
                                    "green": "#138468",
                                    "orange": "#fb6330",
                                    "pink": "#f2609e",
                                    "purple": "#7849b8",
                                    "red": "#ec111a",
                                    "white": "#FFFFFF",
                                    "yellow": "#ffd42f",
                                  },
                                },
                                "link": Object {
                                  "emphasis": Object {
                                    "color": "#007EAB",
                                    "hover": "#005E80",
                                  },
                                },
                                "loadingIndicator": Object {
                                  "background": "#949494",
                                },
                                "meter": Object {
                                  "borderColor": "#949494",
                                  "complete": "#138468",
                                  "incomplete": "#F6F6F6",
                                },
                                "pagination": Object {
                                  "disabled": "#949494",
                                  "selected": "#ec111a",
                                },
                                "pinTextField": Object {
                                  "pinItem": Object {
                                    "backgroundGray": "#F6F7FC",
                                  },
                                },
                                "primary": "#009dd6",
                                "quickActionCard": Object {
                                  "chevron": Object {
                                    "black": "#333333",
                                  },
                                },
                                "radio": Object {
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                },
                                "search": Object {
                                  "border": "#757575",
                                },
                                "snackBar": Object {
                                  "background": "#2c2c2e",
                                  "button": Object {
                                    "color": "#009DD6",
                                    "hover": "#05BCFF",
                                  },
                                  "color": "#FFFFFF",
                                },
                                "stepTracker": Object {
                                  "border": "#949494",
                                  "incompleteBackground": "#D6D6D6",
                                },
                                "tabs": Object {
                                  "border": "#383838",
                                  "hover": "#757575",
                                },
                                "text": Object {
                                  "disabled": "#949494",
                                  "highEmphasis": "#333333",
                                  "mediumEmphasis": "#757575",
                                  "placeholderText": "#949494",
                                },
                                "textArea": Object {
                                  "border": "#757575",
                                },
                                "toggleSwitch": Object {
                                  "iconOff": "#949494",
                                  "iconOn": "#007EAB",
                                },
                                "tooltip": Object {
                                  "color": Object {
                                    "blue": "#009dd6",
                                    "default": "#333333",
                                  },
                                },
                              },
                              "tokens": Object {
                                "action": Object {
                                  "focus": Object {
                                    "default": "#007EAB",
                                    "outline": "#009DD6",
                                  },
                                  "hover": Object {
                                    "background": Object {
                                      "actionMenuList": Object {
                                        "default": "rgba(226,232,238,0.5)",
                                      },
                                      "pillButton": Object {
                                        "caution": "#BE061B",
                                        "default": "#333333",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#BE061B",
                                        "inverted": "#333333",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#BE061B",
                                        "textOnly": "#333333",
                                      },
                                    },
                                    "border": Object {
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#BE061B",
                                        "textOnly": "transparent",
                                      },
                                    },
                                    "default": "#005E80",
                                    "text": Object {
                                      "pillButton": Object {
                                        "caution": "#FFFFFF",
                                        "default": "#FFFFFF",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#FFFFFF",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#FFFFFF",
                                        "red": "#FFFFFF",
                                        "textOnly": "#FFFFFF",
                                      },
                                      "snackbar": Object {
                                        "button": "#05BCFF",
                                      },
                                    },
                                  },
                                },
                                "background": Object {
                                  "alertBanner": Object {
                                    "alert": "rgba(251,99,48,0.08)",
                                    "error": "rgba(236,17,26,0.08)",
                                    "info": "rgba(0,157,214,0.08)",
                                    "new": "rgba(120,73,184,0.08)",
                                    "success": "rgba(19,132,104,0.08)",
                                  },
                                  "base": "#FFFFFF",
                                  "comboBox": Object {
                                    "default": "#FFFFFF",
                                    "listBoxItem": Object {
                                      "hover": "#F6F6F6",
                                    },
                                  },
                                  "footer": Object {
                                    "base": Object {
                                      "grey": "#F6F7FC",
                                      "white": "#FFFFFF",
                                    },
                                    "logo": Object {
                                      "default": "#EC111A",
                                    },
                                    "text": Object {
                                      "hover": "#333333",
                                    },
                                  },
                                  "header": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "white": "#FFFFFF",
                                  },
                                  "meter": Object {
                                    "default": "#138468",
                                    "incomplete": "#F6F6F6",
                                  },
                                  "modal": "#FFFFFF",
                                  "navigation": Object {
                                    "default": "#FFFFFF",
                                  },
                                  "overlay": "rgba(0, 0, 0, 0.5)",
                                  "pillButtonPrimary": Object {
                                    "default": "#EC111A",
                                    "inverted": "#FFFFFF",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "transparent",
                                    "red": "transparent",
                                    "textOnly": "transparent",
                                  },
                                  "primary": "#FFFFFF",
                                  "progressIndicator": Object {
                                    "bar": Object {
                                      "default": "#138468",
                                    },
                                    "container": Object {
                                      "default": "#F6F6F6",
                                    },
                                  },
                                  "secondary": "#FFFFFF",
                                  "skeleton": "#949494",
                                  "snackbar": "#2C2C2C",
                                  "statusBadge": Object {
                                    "default": "transparent",
                                    "emphasis": "transparent",
                                    "error": "transparent",
                                    "new": "#7849B8",
                                    "success": "#138468",
                                    "success-emphasis": "transparent",
                                  },
                                  "table": Object {
                                    "row": Object {
                                      "alternate": "#FAFBFD",
                                      "hover": "rgba(226, 232, 238, 0.4)",
                                      "selected": "#F7F7F8",
                                    },
                                  },
                                },
                                "border": Object {
                                  "alertBanner": Object {
                                    "alert": "rgba(251,99,48,0.5)",
                                    "error": "rgba(236,17,26,0.5)",
                                    "info": "rgba(0,157,214,0.5)",
                                    "new": "rgba(120,73,184,0.5)",
                                    "success": "rgba(19,132,104,0.5)",
                                  },
                                  "card": "#E2E8EE",
                                  "default": "#707070",
                                  "header": Object {
                                    "white": "#E2E8EE",
                                  },
                                  "meter": Object {
                                    "default": "#949494",
                                  },
                                  "pillButton": Object {
                                    "caution": "#EC111A",
                                    "default": "#333333",
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": "#EC111A",
                                    "inverted": "#FFFFFF",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "textOnly": "transparent",
                                  },
                                  "progressIndicator": Object {
                                    "container": Object {
                                      "default": "#949494",
                                    },
                                  },
                                  "skeleton": "#949494",
                                  "statusBadge": Object {
                                    "default": "#707070",
                                    "emphasis": "#007EAB",
                                    "error": "#BE061B",
                                    "new": "#7849B8",
                                    "success": "#138468",
                                    "success-emphasis": "#138468",
                                  },
                                },
                                "checkbox": Object {
                                  "border": "#757575",
                                  "card": Object {
                                    "border": Object {
                                      "focus": "#007EAB",
                                      "hover": "#949494",
                                    },
                                  },
                                  "checked": "#007EAB",
                                  "focus": Object {
                                    "boxShadowColor": Object {
                                      "a": "#FFFFFF",
                                      "b": "#007EAB",
                                    },
                                  },
                                  "indeterminate": "#949494",
                                },
                                "closeButton": Object {
                                  "border": "#949494",
                                  "color": "#333333",
                                },
                                "fileUpload": Object {
                                  "background": "#007EAB",
                                  "border": "#007EAB",
                                  "color": "#007EAB",
                                },
                                "grid": Object {
                                  "breakPoints": Object {
                                    "lg": Object {
                                      "gutter": 3,
                                      "margin": "3.6rem",
                                      "maxCols": 12,
                                      "maxWidth": "4000px",
                                      "minWidth": "1025px",
                                      "rowMargin": "-1.8rem",
                                      "size": "lg",
                                    },
                                    "md": Object {
                                      "gutter": 3,
                                      "margin": Array [
                                        "3.6rem",
                                        "4.2rem",
                                        "4.8rem",
                                        "5.4rem",
                                      ],
                                      "maxCols": 12,
                                      "maxWidth": "1024px",
                                      "minWidth": "768px",
                                      "rowMargin": "-1.5rem",
                                      "size": "md",
                                    },
                                    "sm": Object {
                                      "gutter": 1.8,
                                      "margin": Array [
                                        "2.4rem",
                                        "3.0rem",
                                        "3.6rem",
                                        "4.2rem",
                                      ],
                                      "maxCols": 8,
                                      "maxWidth": "767px",
                                      "minWidth": "481px",
                                      "rowMargin": "-1.2rem",
                                      "size": "sm",
                                    },
                                    "xs": Object {
                                      "gutter": 1.8,
                                      "margin": Array [
                                        "1.8rem",
                                        "2.4rem",
                                        "3.0rem",
                                        "3.6rem",
                                      ],
                                      "maxCols": 4,
                                      "maxWidth": "480px",
                                      "minWidth": "0px",
                                      "rowMargin": "-0.6rem",
                                      "size": "xs",
                                    },
                                  },
                                  "containerMaxWidth": 1200,
                                  "containerMaxWidthMargins": 1272,
                                  "containerMinWidth": 320,
                                  "gridColumns": 12,
                                },
                                "icons": Object {
                                  "functional": Object {
                                    "black": "#333333",
                                    "blue": "#007EAB",
                                    "darkBlue": "#007EAB",
                                    "darkGray": "#707070",
                                    "darkRed": "#BE061B",
                                    "gray": "#949494",
                                    "green": "#138468",
                                    "orange": "#FB6330",
                                    "pink": "#F2609E",
                                    "purple": "#7849B8",
                                    "red": "#EC111A",
                                    "white": "#FFFFFF",
                                    "yellow": "#FFD42F",
                                  },
                                },
                                "loadingIndicator": Object {
                                  "darkGray": "#949494",
                                  "red": "#EC111A",
                                },
                                "mode": "light",
                                "palette": Object {
                                  "color": Object {
                                    "base": "#FFFFFF",
                                    "black": "#333333",
                                    "blue": "#009DD6",
                                    "brandRed": "#EC111A",
                                    "darkBlue": "#007EAB",
                                    "darkBlue100": "#005E80",
                                    "darkGreen": "#117E63",
                                    "darkRed": "#BE061B",
                                    "darkRed100": "#BE061B",
                                    "green": "#138468",
                                    "green100": "rgb(132,217,198,0.15)",
                                    "orange": "#FB6330",
                                    "pink": "#F2609E",
                                    "primaryRed": "#EC111A",
                                    "purple": "#7849B8",
                                    "yellow": "#FFD42F",
                                  },
                                  "gray": Object {
                                    "0": "#FFFFFF",
                                    "100": "#FAFBFD",
                                    "200": "#F6F7FC",
                                    "300": "#F6F6F6",
                                    "400": "#E2E8EE",
                                    "500": "#D6D6D6",
                                    "550": "#949494",
                                    "600": "#707070",
                                    "700": "#333333",
                                    "800": "#2C2C2C",
                                  },
                                },
                                "pillButtonPrimary": Object {
                                  "default": Object {
                                    "backgroundColor": "#EC111A",
                                    "borderColor": "#EC111A",
                                    "textColor": "#FFFFFF",
                                  },
                                  "hover": Object {
                                    "default": Object {
                                      "backgroundColor": "#BE061B",
                                      "borderColor": "#BE061B",
                                      "textColor": "#FFFFFF",
                                    },
                                    "inverted": Object {
                                      "backgroundColor": "#333333",
                                      "borderColor": "#333333",
                                      "textColor": "#FFFFFF",
                                    },
                                  },
                                  "inverted": Object {
                                    "backgroundColor": "#FFFFFF",
                                    "borderColor": "#FFFFFF",
                                    "textColor": "#333333",
                                  },
                                },
                                "primary": Object {
                                  "dark": "#BE061B",
                                  "main": "#EC111A",
                                },
                                "secondary": Object {
                                  "dark": "#005E80",
                                  "main": "#007EAB",
                                },
                                "secondaryButton": Object {
                                  "default": Object {
                                    "backgroundColor": "#FFFFFF",
                                    "borderColor": "#EC111A",
                                    "textColor": "#EC111A",
                                  },
                                  "hover": Object {
                                    "default": Object {
                                      "backgroundColor": "#BE061B",
                                      "borderColor": "#BE061B",
                                      "textColor": "#FFFFFF",
                                    },
                                  },
                                },
                                "sidesheet": Object {
                                  "overlay": Object {
                                    "background": "#FFFFFF",
                                  },
                                  "persistent": Object {
                                    "background": "#FFFFFF",
                                  },
                                },
                                "size": Object {
                                  "borderRadius": Object {
                                    "0": "10rem",
                                    "100": "0.4rem",
                                    "200": "0.8rem",
                                    "300": "1.2rem",
                                    "50": "0.2rem",
                                  },
                                  "borderWidth": Object {
                                    "1": "0.1rem",
                                    "2": "0.2rem",
                                    "3": "0.3rem",
                                    "5": "0.5rem",
                                  },
                                  "breakPoints": Object {
                                    "lg": "1024px",
                                    "md": "768px",
                                    "sm": "375px",
                                    "xs": "0px",
                                  },
                                  "sizing": Object {
                                    "24": "2.4rem",
                                    "32": "3.2rem",
                                    "36": "3.6rem",
                                    "42": "4.2rem",
                                    "44": "4.4rem",
                                    "48": "4.8rem",
                                    "54": "5.4rem",
                                    "60": "6.0rem",
                                    "72": "7.2rem",
                                  },
                                  "spacing": Object {
                                    "1": "0.6rem",
                                    "10": "6.0rem",
                                    "11": "6.6rem",
                                    "12": "7.2rem",
                                    "13": "7.8rem",
                                    "14": "8.4rem",
                                    "15": "9.0rem",
                                    "16": "9.6rem",
                                    "17": "10.2rem",
                                    "18": "10.8rem",
                                    "19": "11.4rem",
                                    "2": "1.2rem",
                                    "20": "12.0rem",
                                    "3": "1.8rem",
                                    "4": "2.4rem",
                                    "5": "3.0rem",
                                    "6": "3.6rem",
                                    "7": "4.2rem",
                                    "8": "4.8rem",
                                    "9": "5.4rem",
                                  },
                                },
                                "state": Object {
                                  "disabled": Object {
                                    "background": "#F6F6F6",
                                    "border": "#D6D6D6",
                                    "text": "#949494",
                                  },
                                  "error": Object {
                                    "default": "#BE061B",
                                  },
                                },
                                "text": Object {
                                  "highEmphasis": "#333333",
                                  "medEmphasis": "#707070",
                                  "pillButton": Object {
                                    "caution": "#EC111A",
                                    "default": "#333333",
                                    "inverted": "#333333",
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": "#FFFFFF",
                                    "inverted": "#333333",
                                  },
                                  "pillButtonSecondary": Object {
                                    "black": "#333333",
                                    "red": "#EC111A",
                                    "textOnly": "#333333",
                                  },
                                  "placeholder": "#707070",
                                  "secondaryButton": "#EC111A",
                                  "snackbar": Object {
                                    "button": "#009DD6",
                                    "content": "#E0E0E0",
                                  },
                                  "statusBadge": Object {
                                    "default": "#707070",
                                    "emphasis": "#007EAB",
                                    "error": "#BE061B",
                                    "new": "#FFFFFF",
                                    "success": "#FFFFFF",
                                    "success-emphasis": "#138468",
                                  },
                                },
                                "textArea": Object {
                                  "border": "#757575",
                                },
                                "textButton": Object {
                                  "color": Object {
                                    "focus": "#007EAB",
                                    "hover": "#005E80",
                                  },
                                },
                                "theme": "default",
                                "tooltip": Object {
                                  "color": Object {
                                    "blue": "#009DD6",
                                    "default": "#333333",
                                  },
                                },
                                "transform": Object {
                                  "elevation": Object {
                                    "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
                                  },
                                  "motion": Object {
                                    "100": "200ms",
                                    "150": "350ms",
                                    "200": "500ms",
                                    "300": "0.1s",
                                    "400": "0.2s",
                                    "500": "0.3s",
                                    "600": "0.4s",
                                    "700": "0.5s",
                                    "800": "0.6s",
                                  },
                                  "opacity": Object {
                                    "0": 0,
                                    "100": 1,
                                    "11": 0.11,
                                    "15": 0.15,
                                    "16": 0.16,
                                    "2": 0.02,
                                    "3": 0.03,
                                    "38": 0.38,
                                    "5": 0.05,
                                    "50": 0.5,
                                    "60": 0.6,
                                    "87": 0.87,
                                  },
                                },
                                "type": "light",
                                "typography": Object {
                                  "lineHeight": Object {
                                    "100": "1.8rem",
                                    "1000": "5.4rem",
                                    "200": "2.0rem",
                                    "300": "2.1rem",
                                    "400": "2.2rem",
                                    "500": "2.4rem",
                                    "600": "2.6rem",
                                    "700": "2.7rem",
                                    "800": "3.0rem",
                                    "850": "3.5rem",
                                    "900": "4.0rem",
                                    "910": "4.1rem",
                                  },
                                  "size": Object {
                                    "100": "1.2rem",
                                    "1000": "3.6rem",
                                    "1100": "4.8rem",
                                    "200": "1.4rem",
                                    "300": "1.6rem",
                                    "400": "1.8rem",
                                    "500": "2.0rem",
                                    "600": "2.1rem",
                                    "700": "2.4rem",
                                    "800": "2.8rem",
                                    "900": "3.2rem",
                                  },
                                  "weight": Object {
                                    "bold": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "boldItalics": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "headline": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "italic": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "legal": "\\"Scotia Legal\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "legalItalic": "\\"Scotia Legal Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "regular": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  },
                                },
                              },
                              "transitions": Object {
                                "duaration": Object {
                                  "sideSheetMainWrapper": "600ms",
                                },
                                "effect": Object {
                                  "easing": Object {
                                    "easeInOut": "ease-in-out",
                                  },
                                },
                              },
                              "type": "light",
                              "typography": Object {
                                "fontBoldFamily": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontBoldItalicFamily": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontHeadlineFamily": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontItalicFamily": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontLightFamily": "\\"Scotia Light\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                "fontRegularFamily": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                              },
                            }
                          }
                        >
                          <f
                            Icon={null}
                            className="SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary errors__button"
                            disabled={false}
                            iconPosition="left"
                            labelPadding={36}
                            loading={false}
                            loadingAriaLabel="Loading"
                            onClick={[Function]}
                            size="regular"
                            theme={
                              Object {
                                "breakPoints": Object {
                                  "values": Object {
                                    "lg": 1025,
                                    "md": 768,
                                    "sm": 481,
                                    "xs": 0,
                                  },
                                },
                                "palette": Object {
                                  "actionMenuList": Object {
                                    "item": Object {
                                      "background": Object {
                                        "hover": "#F6F7FC",
                                      },
                                      "text": "#333",
                                    },
                                    "menuButton": Object {
                                      "background": Object {
                                        "hover": "#F6F7FC",
                                      },
                                      "border": Object {
                                        "focus": "#007EAB",
                                      },
                                      "text": "#333",
                                    },
                                  },
                                  "alertBanner": Object {
                                    "alert": Object {
                                      "background": "#FFF3EF",
                                      "border": "#FDB197",
                                    },
                                    "error": Object {
                                      "background": "#FEECED",
                                      "border": "#F5888C",
                                    },
                                    "info": Object {
                                      "background": "#EBF7FC",
                                      "border": "#7FCEEA",
                                    },
                                    "success": Object {
                                      "background": "#ECF5F3",
                                      "border": "#89C1B3",
                                    },
                                  },
                                  "backToTop": Object {
                                    "background": "#FFFFFF",
                                    "border": "#E2E8EE",
                                    "hover": "#949494",
                                  },
                                  "background": Object {
                                    "card": Object {
                                      "hover": "#949494",
                                      "keyline": "#E2E8EE",
                                      "primary": "#FFFFFF",
                                      "secondary": "#FFFFFF",
                                    },
                                    "modal": Object {
                                      "keyline": "#E2E8EE",
                                      "overlay": "rgba(0, 0, 0, 0.5)",
                                      "primary": "#FFFFFF",
                                    },
                                    "primary": "#FFFFFF",
                                  },
                                  "brandColors": Object {
                                    "dark": Object {
                                      "black": "#757575",
                                      "blue": "#91ddf8",
                                      "green": "#84d9c6",
                                      "orange": "#ffba8e",
                                      "pink": "#fda8de",
                                      "purple": "#aea9f4",
                                      "red": "#ff969c",
                                      "white": "#333333",
                                      "yellow": "#ffeaa5",
                                    },
                                    "light": Object {
                                      "black": "#333333",
                                      "blue": "#009dd6",
                                      "darkBlue": "#007EAB",
                                      "darkRed": "#BE061B",
                                      "green": "#138468",
                                      "orange": "#fb6330",
                                      "pink": "#f2609e",
                                      "purple": "#7849b8",
                                      "red": "#ec111a",
                                      "white": "#FFFFFF",
                                      "yellow": "#ffd42f",
                                    },
                                  },
                                  "button": Object {
                                    "close": Object {
                                      "color": "#333333",
                                      "hover": "#949494",
                                    },
                                    "disabled": Object {
                                      "background": "#F6F6F6",
                                      "border": "#D6D6D6",
                                      "color": "#757575",
                                    },
                                    "navigationButtons": Object {
                                      "backButtonColor": "#333333",
                                      "continueButtonColor": "#ec111a",
                                    },
                                    "pillButton": Object {
                                      "background": "transparent",
                                      "border": "#333333",
                                      "caution": Object {
                                        "color": "#ec111a",
                                      },
                                      "color": "#333333",
                                    },
                                    "primary": Object {
                                      "background": "#ec111a",
                                      "border": "#ec111a",
                                      "color": "#FFFFFF",
                                    },
                                    "secondary": Object {
                                      "background": "transparent",
                                      "border": "#ec111a",
                                      "color": "#ec111a",
                                    },
                                  },
                                  "canvasGrey": Object {
                                    "100": "#FAFBFD",
                                    "200": "#F6F7FC",
                                    "300": "#F6F6F6",
                                    "400": "#E2E8EE",
                                    "500": "#D6D6D6",
                                    "600": "#757575",
                                    "700": "#949494",
                                  },
                                  "canvasShadow": "rgba(0, 34, 91, 0.11)",
                                  "charts": Object {
                                    "lineChart": Object {
                                      "axisLineColor": "#757575",
                                      "gridLineColor": "#D6D6D6",
                                      "tooltip": Object {
                                        "borderColor": "#949494",
                                      },
                                    },
                                    "stackedColumn": Object {
                                      "axisLineColor": "#757575",
                                      "dataSetColors": Object {
                                        "limitedRetail": Array [
                                          "#333333",
                                          "#009DD6",
                                          "#7849B8",
                                          "#138468",
                                          "#FB6330",
                                        ],
                                        "retail": Array [
                                          "#009DD6",
                                          "#7849B8",
                                          "#138468",
                                          "#F2609E",
                                          "#F2609E",
                                        ],
                                      },
                                      "gridLineColor": "#D6D6D6",
                                      "tooltip": Object {
                                        "borderColor": "#383838",
                                      },
                                    },
                                  },
                                  "checkbox": Object {
                                    "border": "#757575",
                                    "checked": "#007EAB",
                                    "focus": Object {
                                      "boxShadowColor": Object {
                                        "a": "#FFFFFF",
                                        "b": "#007EAB",
                                      },
                                    },
                                    "indeterminate": "#949494",
                                  },
                                  "common": Object {
                                    "black": "#333333",
                                    "white": "#FFFFFF",
                                  },
                                  "error": "#be061b",
                                  "focusOutline": "#007EAB",
                                  "footer": Object {
                                    "base": Object {
                                      "background": "#F6F7FC",
                                    },
                                    "border": "#E2E8EE",
                                    "link": Object {
                                      "hover": "#333333",
                                    },
                                    "logo": Object {
                                      "fill": "#ec111a",
                                    },
                                  },
                                  "hamburger": Object {
                                    "menu": Object {
                                      "background": "#FFFFFF",
                                    },
                                  },
                                  "icons": Object {
                                    "category": Object {
                                      "circle": "#E2E8EE",
                                      "path": "#757575",
                                    },
                                    "functional": Object {
                                      "black": "#333333",
                                      "blue": "#007EAB",
                                      "darkBlue": "#007EAB",
                                      "darkRed": "#BE061B",
                                      "green": "#138468",
                                      "orange": "#fb6330",
                                      "pink": "#f2609e",
                                      "purple": "#7849b8",
                                      "red": "#ec111a",
                                      "white": "#FFFFFF",
                                      "yellow": "#ffd42f",
                                    },
                                  },
                                  "link": Object {
                                    "emphasis": Object {
                                      "color": "#007EAB",
                                      "hover": "#005E80",
                                    },
                                  },
                                  "loadingIndicator": Object {
                                    "background": "#949494",
                                  },
                                  "meter": Object {
                                    "borderColor": "#949494",
                                    "complete": "#138468",
                                    "incomplete": "#F6F6F6",
                                  },
                                  "pagination": Object {
                                    "disabled": "#949494",
                                    "selected": "#ec111a",
                                  },
                                  "pinTextField": Object {
                                    "pinItem": Object {
                                      "backgroundGray": "#F6F7FC",
                                    },
                                  },
                                  "primary": "#009dd6",
                                  "quickActionCard": Object {
                                    "chevron": Object {
                                      "black": "#333333",
                                    },
                                  },
                                  "radio": Object {
                                    "focus": Object {
                                      "boxShadowColor": Object {
                                        "a": "#FFFFFF",
                                        "b": "#007EAB",
                                      },
                                    },
                                  },
                                  "search": Object {
                                    "border": "#757575",
                                  },
                                  "snackBar": Object {
                                    "background": "#2c2c2e",
                                    "button": Object {
                                      "color": "#009DD6",
                                      "hover": "#05BCFF",
                                    },
                                    "color": "#FFFFFF",
                                  },
                                  "stepTracker": Object {
                                    "border": "#949494",
                                    "incompleteBackground": "#D6D6D6",
                                  },
                                  "tabs": Object {
                                    "border": "#383838",
                                    "hover": "#757575",
                                  },
                                  "text": Object {
                                    "disabled": "#949494",
                                    "highEmphasis": "#333333",
                                    "mediumEmphasis": "#757575",
                                    "placeholderText": "#949494",
                                  },
                                  "textArea": Object {
                                    "border": "#757575",
                                  },
                                  "toggleSwitch": Object {
                                    "iconOff": "#949494",
                                    "iconOn": "#007EAB",
                                  },
                                  "tooltip": Object {
                                    "color": Object {
                                      "blue": "#009dd6",
                                      "default": "#333333",
                                    },
                                  },
                                },
                                "tokens": Object {
                                  "action": Object {
                                    "focus": Object {
                                      "default": "#007EAB",
                                      "outline": "#009DD6",
                                    },
                                    "hover": Object {
                                      "background": Object {
                                        "actionMenuList": Object {
                                          "default": "rgba(226,232,238,0.5)",
                                        },
                                        "pillButton": Object {
                                          "caution": "#BE061B",
                                          "default": "#333333",
                                          "inverted": "#FFFFFF",
                                        },
                                        "pillButtonPrimary": Object {
                                          "default": "#BE061B",
                                          "inverted": "#333333",
                                        },
                                        "pillButtonSecondary": Object {
                                          "black": "#333333",
                                          "red": "#BE061B",
                                          "textOnly": "#333333",
                                        },
                                      },
                                      "border": Object {
                                        "pillButtonSecondary": Object {
                                          "black": "#333333",
                                          "red": "#BE061B",
                                          "textOnly": "transparent",
                                        },
                                      },
                                      "default": "#005E80",
                                      "text": Object {
                                        "pillButton": Object {
                                          "caution": "#FFFFFF",
                                          "default": "#FFFFFF",
                                        },
                                        "pillButtonPrimary": Object {
                                          "default": "#FFFFFF",
                                          "inverted": "#FFFFFF",
                                        },
                                        "pillButtonSecondary": Object {
                                          "black": "#FFFFFF",
                                          "red": "#FFFFFF",
                                          "textOnly": "#FFFFFF",
                                        },
                                        "snackbar": Object {
                                          "button": "#05BCFF",
                                        },
                                      },
                                    },
                                  },
                                  "background": Object {
                                    "alertBanner": Object {
                                      "alert": "rgba(251,99,48,0.08)",
                                      "error": "rgba(236,17,26,0.08)",
                                      "info": "rgba(0,157,214,0.08)",
                                      "new": "rgba(120,73,184,0.08)",
                                      "success": "rgba(19,132,104,0.08)",
                                    },
                                    "base": "#FFFFFF",
                                    "comboBox": Object {
                                      "default": "#FFFFFF",
                                      "listBoxItem": Object {
                                        "hover": "#F6F6F6",
                                      },
                                    },
                                    "footer": Object {
                                      "base": Object {
                                        "grey": "#F6F7FC",
                                        "white": "#FFFFFF",
                                      },
                                      "logo": Object {
                                        "default": "#EC111A",
                                      },
                                      "text": Object {
                                        "hover": "#333333",
                                      },
                                    },
                                    "header": Object {
                                      "black": "#333333",
                                      "red": "#EC111A",
                                      "white": "#FFFFFF",
                                    },
                                    "meter": Object {
                                      "default": "#138468",
                                      "incomplete": "#F6F6F6",
                                    },
                                    "modal": "#FFFFFF",
                                    "navigation": Object {
                                      "default": "#FFFFFF",
                                    },
                                    "overlay": "rgba(0, 0, 0, 0.5)",
                                    "pillButtonPrimary": Object {
                                      "default": "#EC111A",
                                      "inverted": "#FFFFFF",
                                    },
                                    "pillButtonSecondary": Object {
                                      "black": "transparent",
                                      "red": "transparent",
                                      "textOnly": "transparent",
                                    },
                                    "primary": "#FFFFFF",
                                    "progressIndicator": Object {
                                      "bar": Object {
                                        "default": "#138468",
                                      },
                                      "container": Object {
                                        "default": "#F6F6F6",
                                      },
                                    },
                                    "secondary": "#FFFFFF",
                                    "skeleton": "#949494",
                                    "snackbar": "#2C2C2C",
                                    "statusBadge": Object {
                                      "default": "transparent",
                                      "emphasis": "transparent",
                                      "error": "transparent",
                                      "new": "#7849B8",
                                      "success": "#138468",
                                      "success-emphasis": "transparent",
                                    },
                                    "table": Object {
                                      "row": Object {
                                        "alternate": "#FAFBFD",
                                        "hover": "rgba(226, 232, 238, 0.4)",
                                        "selected": "#F7F7F8",
                                      },
                                    },
                                  },
                                  "border": Object {
                                    "alertBanner": Object {
                                      "alert": "rgba(251,99,48,0.5)",
                                      "error": "rgba(236,17,26,0.5)",
                                      "info": "rgba(0,157,214,0.5)",
                                      "new": "rgba(120,73,184,0.5)",
                                      "success": "rgba(19,132,104,0.5)",
                                    },
                                    "card": "#E2E8EE",
                                    "default": "#707070",
                                    "header": Object {
                                      "white": "#E2E8EE",
                                    },
                                    "meter": Object {
                                      "default": "#949494",
                                    },
                                    "pillButton": Object {
                                      "caution": "#EC111A",
                                      "default": "#333333",
                                    },
                                    "pillButtonPrimary": Object {
                                      "default": "#EC111A",
                                      "inverted": "#FFFFFF",
                                    },
                                    "pillButtonSecondary": Object {
                                      "black": "#333333",
                                      "red": "#EC111A",
                                      "textOnly": "transparent",
                                    },
                                    "progressIndicator": Object {
                                      "container": Object {
                                        "default": "#949494",
                                      },
                                    },
                                    "skeleton": "#949494",
                                    "statusBadge": Object {
                                      "default": "#707070",
                                      "emphasis": "#007EAB",
                                      "error": "#BE061B",
                                      "new": "#7849B8",
                                      "success": "#138468",
                                      "success-emphasis": "#138468",
                                    },
                                  },
                                  "checkbox": Object {
                                    "border": "#757575",
                                    "card": Object {
                                      "border": Object {
                                        "focus": "#007EAB",
                                        "hover": "#949494",
                                      },
                                    },
                                    "checked": "#007EAB",
                                    "focus": Object {
                                      "boxShadowColor": Object {
                                        "a": "#FFFFFF",
                                        "b": "#007EAB",
                                      },
                                    },
                                    "indeterminate": "#949494",
                                  },
                                  "closeButton": Object {
                                    "border": "#949494",
                                    "color": "#333333",
                                  },
                                  "fileUpload": Object {
                                    "background": "#007EAB",
                                    "border": "#007EAB",
                                    "color": "#007EAB",
                                  },
                                  "grid": Object {
                                    "breakPoints": Object {
                                      "lg": Object {
                                        "gutter": 3,
                                        "margin": "3.6rem",
                                        "maxCols": 12,
                                        "maxWidth": "4000px",
                                        "minWidth": "1025px",
                                        "rowMargin": "-1.8rem",
                                        "size": "lg",
                                      },
                                      "md": Object {
                                        "gutter": 3,
                                        "margin": Array [
                                          "3.6rem",
                                          "4.2rem",
                                          "4.8rem",
                                          "5.4rem",
                                        ],
                                        "maxCols": 12,
                                        "maxWidth": "1024px",
                                        "minWidth": "768px",
                                        "rowMargin": "-1.5rem",
                                        "size": "md",
                                      },
                                      "sm": Object {
                                        "gutter": 1.8,
                                        "margin": Array [
                                          "2.4rem",
                                          "3.0rem",
                                          "3.6rem",
                                          "4.2rem",
                                        ],
                                        "maxCols": 8,
                                        "maxWidth": "767px",
                                        "minWidth": "481px",
                                        "rowMargin": "-1.2rem",
                                        "size": "sm",
                                      },
                                      "xs": Object {
                                        "gutter": 1.8,
                                        "margin": Array [
                                          "1.8rem",
                                          "2.4rem",
                                          "3.0rem",
                                          "3.6rem",
                                        ],
                                        "maxCols": 4,
                                        "maxWidth": "480px",
                                        "minWidth": "0px",
                                        "rowMargin": "-0.6rem",
                                        "size": "xs",
                                      },
                                    },
                                    "containerMaxWidth": 1200,
                                    "containerMaxWidthMargins": 1272,
                                    "containerMinWidth": 320,
                                    "gridColumns": 12,
                                  },
                                  "icons": Object {
                                    "functional": Object {
                                      "black": "#333333",
                                      "blue": "#007EAB",
                                      "darkBlue": "#007EAB",
                                      "darkGray": "#707070",
                                      "darkRed": "#BE061B",
                                      "gray": "#949494",
                                      "green": "#138468",
                                      "orange": "#FB6330",
                                      "pink": "#F2609E",
                                      "purple": "#7849B8",
                                      "red": "#EC111A",
                                      "white": "#FFFFFF",
                                      "yellow": "#FFD42F",
                                    },
                                  },
                                  "loadingIndicator": Object {
                                    "darkGray": "#949494",
                                    "red": "#EC111A",
                                  },
                                  "mode": "light",
                                  "palette": Object {
                                    "color": Object {
                                      "base": "#FFFFFF",
                                      "black": "#333333",
                                      "blue": "#009DD6",
                                      "brandRed": "#EC111A",
                                      "darkBlue": "#007EAB",
                                      "darkBlue100": "#005E80",
                                      "darkGreen": "#117E63",
                                      "darkRed": "#BE061B",
                                      "darkRed100": "#BE061B",
                                      "green": "#138468",
                                      "green100": "rgb(132,217,198,0.15)",
                                      "orange": "#FB6330",
                                      "pink": "#F2609E",
                                      "primaryRed": "#EC111A",
                                      "purple": "#7849B8",
                                      "yellow": "#FFD42F",
                                    },
                                    "gray": Object {
                                      "0": "#FFFFFF",
                                      "100": "#FAFBFD",
                                      "200": "#F6F7FC",
                                      "300": "#F6F6F6",
                                      "400": "#E2E8EE",
                                      "500": "#D6D6D6",
                                      "550": "#949494",
                                      "600": "#707070",
                                      "700": "#333333",
                                      "800": "#2C2C2C",
                                    },
                                  },
                                  "pillButtonPrimary": Object {
                                    "default": Object {
                                      "backgroundColor": "#EC111A",
                                      "borderColor": "#EC111A",
                                      "textColor": "#FFFFFF",
                                    },
                                    "hover": Object {
                                      "default": Object {
                                        "backgroundColor": "#BE061B",
                                        "borderColor": "#BE061B",
                                        "textColor": "#FFFFFF",
                                      },
                                      "inverted": Object {
                                        "backgroundColor": "#333333",
                                        "borderColor": "#333333",
                                        "textColor": "#FFFFFF",
                                      },
                                    },
                                    "inverted": Object {
                                      "backgroundColor": "#FFFFFF",
                                      "borderColor": "#FFFFFF",
                                      "textColor": "#333333",
                                    },
                                  },
                                  "primary": Object {
                                    "dark": "#BE061B",
                                    "main": "#EC111A",
                                  },
                                  "secondary": Object {
                                    "dark": "#005E80",
                                    "main": "#007EAB",
                                  },
                                  "secondaryButton": Object {
                                    "default": Object {
                                      "backgroundColor": "#FFFFFF",
                                      "borderColor": "#EC111A",
                                      "textColor": "#EC111A",
                                    },
                                    "hover": Object {
                                      "default": Object {
                                        "backgroundColor": "#BE061B",
                                        "borderColor": "#BE061B",
                                        "textColor": "#FFFFFF",
                                      },
                                    },
                                  },
                                  "sidesheet": Object {
                                    "overlay": Object {
                                      "background": "#FFFFFF",
                                    },
                                    "persistent": Object {
                                      "background": "#FFFFFF",
                                    },
                                  },
                                  "size": Object {
                                    "borderRadius": Object {
                                      "0": "10rem",
                                      "100": "0.4rem",
                                      "200": "0.8rem",
                                      "300": "1.2rem",
                                      "50": "0.2rem",
                                    },
                                    "borderWidth": Object {
                                      "1": "0.1rem",
                                      "2": "0.2rem",
                                      "3": "0.3rem",
                                      "5": "0.5rem",
                                    },
                                    "breakPoints": Object {
                                      "lg": "1024px",
                                      "md": "768px",
                                      "sm": "375px",
                                      "xs": "0px",
                                    },
                                    "sizing": Object {
                                      "24": "2.4rem",
                                      "32": "3.2rem",
                                      "36": "3.6rem",
                                      "42": "4.2rem",
                                      "44": "4.4rem",
                                      "48": "4.8rem",
                                      "54": "5.4rem",
                                      "60": "6.0rem",
                                      "72": "7.2rem",
                                    },
                                    "spacing": Object {
                                      "1": "0.6rem",
                                      "10": "6.0rem",
                                      "11": "6.6rem",
                                      "12": "7.2rem",
                                      "13": "7.8rem",
                                      "14": "8.4rem",
                                      "15": "9.0rem",
                                      "16": "9.6rem",
                                      "17": "10.2rem",
                                      "18": "10.8rem",
                                      "19": "11.4rem",
                                      "2": "1.2rem",
                                      "20": "12.0rem",
                                      "3": "1.8rem",
                                      "4": "2.4rem",
                                      "5": "3.0rem",
                                      "6": "3.6rem",
                                      "7": "4.2rem",
                                      "8": "4.8rem",
                                      "9": "5.4rem",
                                    },
                                  },
                                  "state": Object {
                                    "disabled": Object {
                                      "background": "#F6F6F6",
                                      "border": "#D6D6D6",
                                      "text": "#949494",
                                    },
                                    "error": Object {
                                      "default": "#BE061B",
                                    },
                                  },
                                  "text": Object {
                                    "highEmphasis": "#333333",
                                    "medEmphasis": "#707070",
                                    "pillButton": Object {
                                      "caution": "#EC111A",
                                      "default": "#333333",
                                      "inverted": "#333333",
                                    },
                                    "pillButtonPrimary": Object {
                                      "default": "#FFFFFF",
                                      "inverted": "#333333",
                                    },
                                    "pillButtonSecondary": Object {
                                      "black": "#333333",
                                      "red": "#EC111A",
                                      "textOnly": "#333333",
                                    },
                                    "placeholder": "#707070",
                                    "secondaryButton": "#EC111A",
                                    "snackbar": Object {
                                      "button": "#009DD6",
                                      "content": "#E0E0E0",
                                    },
                                    "statusBadge": Object {
                                      "default": "#707070",
                                      "emphasis": "#007EAB",
                                      "error": "#BE061B",
                                      "new": "#FFFFFF",
                                      "success": "#FFFFFF",
                                      "success-emphasis": "#138468",
                                    },
                                  },
                                  "textArea": Object {
                                    "border": "#757575",
                                  },
                                  "textButton": Object {
                                    "color": Object {
                                      "focus": "#007EAB",
                                      "hover": "#005E80",
                                    },
                                  },
                                  "theme": "default",
                                  "tooltip": Object {
                                    "color": Object {
                                      "blue": "#009DD6",
                                      "default": "#333333",
                                    },
                                  },
                                  "transform": Object {
                                    "elevation": Object {
                                      "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
                                    },
                                    "motion": Object {
                                      "100": "200ms",
                                      "150": "350ms",
                                      "200": "500ms",
                                      "300": "0.1s",
                                      "400": "0.2s",
                                      "500": "0.3s",
                                      "600": "0.4s",
                                      "700": "0.5s",
                                      "800": "0.6s",
                                    },
                                    "opacity": Object {
                                      "0": 0,
                                      "100": 1,
                                      "11": 0.11,
                                      "15": 0.15,
                                      "16": 0.16,
                                      "2": 0.02,
                                      "3": 0.03,
                                      "38": 0.38,
                                      "5": 0.05,
                                      "50": 0.5,
                                      "60": 0.6,
                                      "87": 0.87,
                                    },
                                  },
                                  "type": "light",
                                  "typography": Object {
                                    "lineHeight": Object {
                                      "100": "1.8rem",
                                      "1000": "5.4rem",
                                      "200": "2.0rem",
                                      "300": "2.1rem",
                                      "400": "2.2rem",
                                      "500": "2.4rem",
                                      "600": "2.6rem",
                                      "700": "2.7rem",
                                      "800": "3.0rem",
                                      "850": "3.5rem",
                                      "900": "4.0rem",
                                      "910": "4.1rem",
                                    },
                                    "size": Object {
                                      "100": "1.2rem",
                                      "1000": "3.6rem",
                                      "1100": "4.8rem",
                                      "200": "1.4rem",
                                      "300": "1.6rem",
                                      "400": "1.8rem",
                                      "500": "2.0rem",
                                      "600": "2.1rem",
                                      "700": "2.4rem",
                                      "800": "2.8rem",
                                      "900": "3.2rem",
                                    },
                                    "weight": Object {
                                      "bold": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      "boldItalics": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      "headline": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      "italic": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      "legal": "\\"Scotia Legal\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      "legalItalic": "\\"Scotia Legal Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      "regular": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    },
                                  },
                                },
                                "transitions": Object {
                                  "duaration": Object {
                                    "sideSheetMainWrapper": "600ms",
                                  },
                                  "effect": Object {
                                    "easing": Object {
                                      "easeInOut": "ease-in-out",
                                    },
                                  },
                                },
                                "type": "light",
                                "typography": Object {
                                  "fontBoldFamily": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  "fontBoldItalicFamily": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  "fontHeadlineFamily": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  "fontItalicFamily": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  "fontLightFamily": "\\"Scotia Light\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  "fontRegularFamily": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                },
                              }
                            }
                          >
                            <ButtonCorestyle__StyledButton
                              className="ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary errors__button"
                              disabled={false}
                              labelPadding={36}
                              onClick={[Function]}
                              size="regular"
                              theme={
                                Object {
                                  "breakPoints": Object {
                                    "values": Object {
                                      "lg": 1025,
                                      "md": 768,
                                      "sm": 481,
                                      "xs": 0,
                                    },
                                  },
                                  "palette": Object {
                                    "actionMenuList": Object {
                                      "item": Object {
                                        "background": Object {
                                          "hover": "#F6F7FC",
                                        },
                                        "text": "#333",
                                      },
                                      "menuButton": Object {
                                        "background": Object {
                                          "hover": "#F6F7FC",
                                        },
                                        "border": Object {
                                          "focus": "#007EAB",
                                        },
                                        "text": "#333",
                                      },
                                    },
                                    "alertBanner": Object {
                                      "alert": Object {
                                        "background": "#FFF3EF",
                                        "border": "#FDB197",
                                      },
                                      "error": Object {
                                        "background": "#FEECED",
                                        "border": "#F5888C",
                                      },
                                      "info": Object {
                                        "background": "#EBF7FC",
                                        "border": "#7FCEEA",
                                      },
                                      "success": Object {
                                        "background": "#ECF5F3",
                                        "border": "#89C1B3",
                                      },
                                    },
                                    "backToTop": Object {
                                      "background": "#FFFFFF",
                                      "border": "#E2E8EE",
                                      "hover": "#949494",
                                    },
                                    "background": Object {
                                      "card": Object {
                                        "hover": "#949494",
                                        "keyline": "#E2E8EE",
                                        "primary": "#FFFFFF",
                                        "secondary": "#FFFFFF",
                                      },
                                      "modal": Object {
                                        "keyline": "#E2E8EE",
                                        "overlay": "rgba(0, 0, 0, 0.5)",
                                        "primary": "#FFFFFF",
                                      },
                                      "primary": "#FFFFFF",
                                    },
                                    "brandColors": Object {
                                      "dark": Object {
                                        "black": "#757575",
                                        "blue": "#91ddf8",
                                        "green": "#84d9c6",
                                        "orange": "#ffba8e",
                                        "pink": "#fda8de",
                                        "purple": "#aea9f4",
                                        "red": "#ff969c",
                                        "white": "#333333",
                                        "yellow": "#ffeaa5",
                                      },
                                      "light": Object {
                                        "black": "#333333",
                                        "blue": "#009dd6",
                                        "darkBlue": "#007EAB",
                                        "darkRed": "#BE061B",
                                        "green": "#138468",
                                        "orange": "#fb6330",
                                        "pink": "#f2609e",
                                        "purple": "#7849b8",
                                        "red": "#ec111a",
                                        "white": "#FFFFFF",
                                        "yellow": "#ffd42f",
                                      },
                                    },
                                    "button": Object {
                                      "close": Object {
                                        "color": "#333333",
                                        "hover": "#949494",
                                      },
                                      "disabled": Object {
                                        "background": "#F6F6F6",
                                        "border": "#D6D6D6",
                                        "color": "#757575",
                                      },
                                      "navigationButtons": Object {
                                        "backButtonColor": "#333333",
                                        "continueButtonColor": "#ec111a",
                                      },
                                      "pillButton": Object {
                                        "background": "transparent",
                                        "border": "#333333",
                                        "caution": Object {
                                          "color": "#ec111a",
                                        },
                                        "color": "#333333",
                                      },
                                      "primary": Object {
                                        "background": "#ec111a",
                                        "border": "#ec111a",
                                        "color": "#FFFFFF",
                                      },
                                      "secondary": Object {
                                        "background": "transparent",
                                        "border": "#ec111a",
                                        "color": "#ec111a",
                                      },
                                    },
                                    "canvasGrey": Object {
                                      "100": "#FAFBFD",
                                      "200": "#F6F7FC",
                                      "300": "#F6F6F6",
                                      "400": "#E2E8EE",
                                      "500": "#D6D6D6",
                                      "600": "#757575",
                                      "700": "#949494",
                                    },
                                    "canvasShadow": "rgba(0, 34, 91, 0.11)",
                                    "charts": Object {
                                      "lineChart": Object {
                                        "axisLineColor": "#757575",
                                        "gridLineColor": "#D6D6D6",
                                        "tooltip": Object {
                                          "borderColor": "#949494",
                                        },
                                      },
                                      "stackedColumn": Object {
                                        "axisLineColor": "#757575",
                                        "dataSetColors": Object {
                                          "limitedRetail": Array [
                                            "#333333",
                                            "#009DD6",
                                            "#7849B8",
                                            "#138468",
                                            "#FB6330",
                                          ],
                                          "retail": Array [
                                            "#009DD6",
                                            "#7849B8",
                                            "#138468",
                                            "#F2609E",
                                            "#F2609E",
                                          ],
                                        },
                                        "gridLineColor": "#D6D6D6",
                                        "tooltip": Object {
                                          "borderColor": "#383838",
                                        },
                                      },
                                    },
                                    "checkbox": Object {
                                      "border": "#757575",
                                      "checked": "#007EAB",
                                      "focus": Object {
                                        "boxShadowColor": Object {
                                          "a": "#FFFFFF",
                                          "b": "#007EAB",
                                        },
                                      },
                                      "indeterminate": "#949494",
                                    },
                                    "common": Object {
                                      "black": "#333333",
                                      "white": "#FFFFFF",
                                    },
                                    "error": "#be061b",
                                    "focusOutline": "#007EAB",
                                    "footer": Object {
                                      "base": Object {
                                        "background": "#F6F7FC",
                                      },
                                      "border": "#E2E8EE",
                                      "link": Object {
                                        "hover": "#333333",
                                      },
                                      "logo": Object {
                                        "fill": "#ec111a",
                                      },
                                    },
                                    "hamburger": Object {
                                      "menu": Object {
                                        "background": "#FFFFFF",
                                      },
                                    },
                                    "icons": Object {
                                      "category": Object {
                                        "circle": "#E2E8EE",
                                        "path": "#757575",
                                      },
                                      "functional": Object {
                                        "black": "#333333",
                                        "blue": "#007EAB",
                                        "darkBlue": "#007EAB",
                                        "darkRed": "#BE061B",
                                        "green": "#138468",
                                        "orange": "#fb6330",
                                        "pink": "#f2609e",
                                        "purple": "#7849b8",
                                        "red": "#ec111a",
                                        "white": "#FFFFFF",
                                        "yellow": "#ffd42f",
                                      },
                                    },
                                    "link": Object {
                                      "emphasis": Object {
                                        "color": "#007EAB",
                                        "hover": "#005E80",
                                      },
                                    },
                                    "loadingIndicator": Object {
                                      "background": "#949494",
                                    },
                                    "meter": Object {
                                      "borderColor": "#949494",
                                      "complete": "#138468",
                                      "incomplete": "#F6F6F6",
                                    },
                                    "pagination": Object {
                                      "disabled": "#949494",
                                      "selected": "#ec111a",
                                    },
                                    "pinTextField": Object {
                                      "pinItem": Object {
                                        "backgroundGray": "#F6F7FC",
                                      },
                                    },
                                    "primary": "#009dd6",
                                    "quickActionCard": Object {
                                      "chevron": Object {
                                        "black": "#333333",
                                      },
                                    },
                                    "radio": Object {
                                      "focus": Object {
                                        "boxShadowColor": Object {
                                          "a": "#FFFFFF",
                                          "b": "#007EAB",
                                        },
                                      },
                                    },
                                    "search": Object {
                                      "border": "#757575",
                                    },
                                    "snackBar": Object {
                                      "background": "#2c2c2e",
                                      "button": Object {
                                        "color": "#009DD6",
                                        "hover": "#05BCFF",
                                      },
                                      "color": "#FFFFFF",
                                    },
                                    "stepTracker": Object {
                                      "border": "#949494",
                                      "incompleteBackground": "#D6D6D6",
                                    },
                                    "tabs": Object {
                                      "border": "#383838",
                                      "hover": "#757575",
                                    },
                                    "text": Object {
                                      "disabled": "#949494",
                                      "highEmphasis": "#333333",
                                      "mediumEmphasis": "#757575",
                                      "placeholderText": "#949494",
                                    },
                                    "textArea": Object {
                                      "border": "#757575",
                                    },
                                    "toggleSwitch": Object {
                                      "iconOff": "#949494",
                                      "iconOn": "#007EAB",
                                    },
                                    "tooltip": Object {
                                      "color": Object {
                                        "blue": "#009dd6",
                                        "default": "#333333",
                                      },
                                    },
                                  },
                                  "tokens": Object {
                                    "action": Object {
                                      "focus": Object {
                                        "default": "#007EAB",
                                        "outline": "#009DD6",
                                      },
                                      "hover": Object {
                                        "background": Object {
                                          "actionMenuList": Object {
                                            "default": "rgba(226,232,238,0.5)",
                                          },
                                          "pillButton": Object {
                                            "caution": "#BE061B",
                                            "default": "#333333",
                                            "inverted": "#FFFFFF",
                                          },
                                          "pillButtonPrimary": Object {
                                            "default": "#BE061B",
                                            "inverted": "#333333",
                                          },
                                          "pillButtonSecondary": Object {
                                            "black": "#333333",
                                            "red": "#BE061B",
                                            "textOnly": "#333333",
                                          },
                                        },
                                        "border": Object {
                                          "pillButtonSecondary": Object {
                                            "black": "#333333",
                                            "red": "#BE061B",
                                            "textOnly": "transparent",
                                          },
                                        },
                                        "default": "#005E80",
                                        "text": Object {
                                          "pillButton": Object {
                                            "caution": "#FFFFFF",
                                            "default": "#FFFFFF",
                                          },
                                          "pillButtonPrimary": Object {
                                            "default": "#FFFFFF",
                                            "inverted": "#FFFFFF",
                                          },
                                          "pillButtonSecondary": Object {
                                            "black": "#FFFFFF",
                                            "red": "#FFFFFF",
                                            "textOnly": "#FFFFFF",
                                          },
                                          "snackbar": Object {
                                            "button": "#05BCFF",
                                          },
                                        },
                                      },
                                    },
                                    "background": Object {
                                      "alertBanner": Object {
                                        "alert": "rgba(251,99,48,0.08)",
                                        "error": "rgba(236,17,26,0.08)",
                                        "info": "rgba(0,157,214,0.08)",
                                        "new": "rgba(120,73,184,0.08)",
                                        "success": "rgba(19,132,104,0.08)",
                                      },
                                      "base": "#FFFFFF",
                                      "comboBox": Object {
                                        "default": "#FFFFFF",
                                        "listBoxItem": Object {
                                          "hover": "#F6F6F6",
                                        },
                                      },
                                      "footer": Object {
                                        "base": Object {
                                          "grey": "#F6F7FC",
                                          "white": "#FFFFFF",
                                        },
                                        "logo": Object {
                                          "default": "#EC111A",
                                        },
                                        "text": Object {
                                          "hover": "#333333",
                                        },
                                      },
                                      "header": Object {
                                        "black": "#333333",
                                        "red": "#EC111A",
                                        "white": "#FFFFFF",
                                      },
                                      "meter": Object {
                                        "default": "#138468",
                                        "incomplete": "#F6F6F6",
                                      },
                                      "modal": "#FFFFFF",
                                      "navigation": Object {
                                        "default": "#FFFFFF",
                                      },
                                      "overlay": "rgba(0, 0, 0, 0.5)",
                                      "pillButtonPrimary": Object {
                                        "default": "#EC111A",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "transparent",
                                        "red": "transparent",
                                        "textOnly": "transparent",
                                      },
                                      "primary": "#FFFFFF",
                                      "progressIndicator": Object {
                                        "bar": Object {
                                          "default": "#138468",
                                        },
                                        "container": Object {
                                          "default": "#F6F6F6",
                                        },
                                      },
                                      "secondary": "#FFFFFF",
                                      "skeleton": "#949494",
                                      "snackbar": "#2C2C2C",
                                      "statusBadge": Object {
                                        "default": "transparent",
                                        "emphasis": "transparent",
                                        "error": "transparent",
                                        "new": "#7849B8",
                                        "success": "#138468",
                                        "success-emphasis": "transparent",
                                      },
                                      "table": Object {
                                        "row": Object {
                                          "alternate": "#FAFBFD",
                                          "hover": "rgba(226, 232, 238, 0.4)",
                                          "selected": "#F7F7F8",
                                        },
                                      },
                                    },
                                    "border": Object {
                                      "alertBanner": Object {
                                        "alert": "rgba(251,99,48,0.5)",
                                        "error": "rgba(236,17,26,0.5)",
                                        "info": "rgba(0,157,214,0.5)",
                                        "new": "rgba(120,73,184,0.5)",
                                        "success": "rgba(19,132,104,0.5)",
                                      },
                                      "card": "#E2E8EE",
                                      "default": "#707070",
                                      "header": Object {
                                        "white": "#E2E8EE",
                                      },
                                      "meter": Object {
                                        "default": "#949494",
                                      },
                                      "pillButton": Object {
                                        "caution": "#EC111A",
                                        "default": "#333333",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#EC111A",
                                        "inverted": "#FFFFFF",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#EC111A",
                                        "textOnly": "transparent",
                                      },
                                      "progressIndicator": Object {
                                        "container": Object {
                                          "default": "#949494",
                                        },
                                      },
                                      "skeleton": "#949494",
                                      "statusBadge": Object {
                                        "default": "#707070",
                                        "emphasis": "#007EAB",
                                        "error": "#BE061B",
                                        "new": "#7849B8",
                                        "success": "#138468",
                                        "success-emphasis": "#138468",
                                      },
                                    },
                                    "checkbox": Object {
                                      "border": "#757575",
                                      "card": Object {
                                        "border": Object {
                                          "focus": "#007EAB",
                                          "hover": "#949494",
                                        },
                                      },
                                      "checked": "#007EAB",
                                      "focus": Object {
                                        "boxShadowColor": Object {
                                          "a": "#FFFFFF",
                                          "b": "#007EAB",
                                        },
                                      },
                                      "indeterminate": "#949494",
                                    },
                                    "closeButton": Object {
                                      "border": "#949494",
                                      "color": "#333333",
                                    },
                                    "fileUpload": Object {
                                      "background": "#007EAB",
                                      "border": "#007EAB",
                                      "color": "#007EAB",
                                    },
                                    "grid": Object {
                                      "breakPoints": Object {
                                        "lg": Object {
                                          "gutter": 3,
                                          "margin": "3.6rem",
                                          "maxCols": 12,
                                          "maxWidth": "4000px",
                                          "minWidth": "1025px",
                                          "rowMargin": "-1.8rem",
                                          "size": "lg",
                                        },
                                        "md": Object {
                                          "gutter": 3,
                                          "margin": Array [
                                            "3.6rem",
                                            "4.2rem",
                                            "4.8rem",
                                            "5.4rem",
                                          ],
                                          "maxCols": 12,
                                          "maxWidth": "1024px",
                                          "minWidth": "768px",
                                          "rowMargin": "-1.5rem",
                                          "size": "md",
                                        },
                                        "sm": Object {
                                          "gutter": 1.8,
                                          "margin": Array [
                                            "2.4rem",
                                            "3.0rem",
                                            "3.6rem",
                                            "4.2rem",
                                          ],
                                          "maxCols": 8,
                                          "maxWidth": "767px",
                                          "minWidth": "481px",
                                          "rowMargin": "-1.2rem",
                                          "size": "sm",
                                        },
                                        "xs": Object {
                                          "gutter": 1.8,
                                          "margin": Array [
                                            "1.8rem",
                                            "2.4rem",
                                            "3.0rem",
                                            "3.6rem",
                                          ],
                                          "maxCols": 4,
                                          "maxWidth": "480px",
                                          "minWidth": "0px",
                                          "rowMargin": "-0.6rem",
                                          "size": "xs",
                                        },
                                      },
                                      "containerMaxWidth": 1200,
                                      "containerMaxWidthMargins": 1272,
                                      "containerMinWidth": 320,
                                      "gridColumns": 12,
                                    },
                                    "icons": Object {
                                      "functional": Object {
                                        "black": "#333333",
                                        "blue": "#007EAB",
                                        "darkBlue": "#007EAB",
                                        "darkGray": "#707070",
                                        "darkRed": "#BE061B",
                                        "gray": "#949494",
                                        "green": "#138468",
                                        "orange": "#FB6330",
                                        "pink": "#F2609E",
                                        "purple": "#7849B8",
                                        "red": "#EC111A",
                                        "white": "#FFFFFF",
                                        "yellow": "#FFD42F",
                                      },
                                    },
                                    "loadingIndicator": Object {
                                      "darkGray": "#949494",
                                      "red": "#EC111A",
                                    },
                                    "mode": "light",
                                    "palette": Object {
                                      "color": Object {
                                        "base": "#FFFFFF",
                                        "black": "#333333",
                                        "blue": "#009DD6",
                                        "brandRed": "#EC111A",
                                        "darkBlue": "#007EAB",
                                        "darkBlue100": "#005E80",
                                        "darkGreen": "#117E63",
                                        "darkRed": "#BE061B",
                                        "darkRed100": "#BE061B",
                                        "green": "#138468",
                                        "green100": "rgb(132,217,198,0.15)",
                                        "orange": "#FB6330",
                                        "pink": "#F2609E",
                                        "primaryRed": "#EC111A",
                                        "purple": "#7849B8",
                                        "yellow": "#FFD42F",
                                      },
                                      "gray": Object {
                                        "0": "#FFFFFF",
                                        "100": "#FAFBFD",
                                        "200": "#F6F7FC",
                                        "300": "#F6F6F6",
                                        "400": "#E2E8EE",
                                        "500": "#D6D6D6",
                                        "550": "#949494",
                                        "600": "#707070",
                                        "700": "#333333",
                                        "800": "#2C2C2C",
                                      },
                                    },
                                    "pillButtonPrimary": Object {
                                      "default": Object {
                                        "backgroundColor": "#EC111A",
                                        "borderColor": "#EC111A",
                                        "textColor": "#FFFFFF",
                                      },
                                      "hover": Object {
                                        "default": Object {
                                          "backgroundColor": "#BE061B",
                                          "borderColor": "#BE061B",
                                          "textColor": "#FFFFFF",
                                        },
                                        "inverted": Object {
                                          "backgroundColor": "#333333",
                                          "borderColor": "#333333",
                                          "textColor": "#FFFFFF",
                                        },
                                      },
                                      "inverted": Object {
                                        "backgroundColor": "#FFFFFF",
                                        "borderColor": "#FFFFFF",
                                        "textColor": "#333333",
                                      },
                                    },
                                    "primary": Object {
                                      "dark": "#BE061B",
                                      "main": "#EC111A",
                                    },
                                    "secondary": Object {
                                      "dark": "#005E80",
                                      "main": "#007EAB",
                                    },
                                    "secondaryButton": Object {
                                      "default": Object {
                                        "backgroundColor": "#FFFFFF",
                                        "borderColor": "#EC111A",
                                        "textColor": "#EC111A",
                                      },
                                      "hover": Object {
                                        "default": Object {
                                          "backgroundColor": "#BE061B",
                                          "borderColor": "#BE061B",
                                          "textColor": "#FFFFFF",
                                        },
                                      },
                                    },
                                    "sidesheet": Object {
                                      "overlay": Object {
                                        "background": "#FFFFFF",
                                      },
                                      "persistent": Object {
                                        "background": "#FFFFFF",
                                      },
                                    },
                                    "size": Object {
                                      "borderRadius": Object {
                                        "0": "10rem",
                                        "100": "0.4rem",
                                        "200": "0.8rem",
                                        "300": "1.2rem",
                                        "50": "0.2rem",
                                      },
                                      "borderWidth": Object {
                                        "1": "0.1rem",
                                        "2": "0.2rem",
                                        "3": "0.3rem",
                                        "5": "0.5rem",
                                      },
                                      "breakPoints": Object {
                                        "lg": "1024px",
                                        "md": "768px",
                                        "sm": "375px",
                                        "xs": "0px",
                                      },
                                      "sizing": Object {
                                        "24": "2.4rem",
                                        "32": "3.2rem",
                                        "36": "3.6rem",
                                        "42": "4.2rem",
                                        "44": "4.4rem",
                                        "48": "4.8rem",
                                        "54": "5.4rem",
                                        "60": "6.0rem",
                                        "72": "7.2rem",
                                      },
                                      "spacing": Object {
                                        "1": "0.6rem",
                                        "10": "6.0rem",
                                        "11": "6.6rem",
                                        "12": "7.2rem",
                                        "13": "7.8rem",
                                        "14": "8.4rem",
                                        "15": "9.0rem",
                                        "16": "9.6rem",
                                        "17": "10.2rem",
                                        "18": "10.8rem",
                                        "19": "11.4rem",
                                        "2": "1.2rem",
                                        "20": "12.0rem",
                                        "3": "1.8rem",
                                        "4": "2.4rem",
                                        "5": "3.0rem",
                                        "6": "3.6rem",
                                        "7": "4.2rem",
                                        "8": "4.8rem",
                                        "9": "5.4rem",
                                      },
                                    },
                                    "state": Object {
                                      "disabled": Object {
                                        "background": "#F6F6F6",
                                        "border": "#D6D6D6",
                                        "text": "#949494",
                                      },
                                      "error": Object {
                                        "default": "#BE061B",
                                      },
                                    },
                                    "text": Object {
                                      "highEmphasis": "#333333",
                                      "medEmphasis": "#707070",
                                      "pillButton": Object {
                                        "caution": "#EC111A",
                                        "default": "#333333",
                                        "inverted": "#333333",
                                      },
                                      "pillButtonPrimary": Object {
                                        "default": "#FFFFFF",
                                        "inverted": "#333333",
                                      },
                                      "pillButtonSecondary": Object {
                                        "black": "#333333",
                                        "red": "#EC111A",
                                        "textOnly": "#333333",
                                      },
                                      "placeholder": "#707070",
                                      "secondaryButton": "#EC111A",
                                      "snackbar": Object {
                                        "button": "#009DD6",
                                        "content": "#E0E0E0",
                                      },
                                      "statusBadge": Object {
                                        "default": "#707070",
                                        "emphasis": "#007EAB",
                                        "error": "#BE061B",
                                        "new": "#FFFFFF",
                                        "success": "#FFFFFF",
                                        "success-emphasis": "#138468",
                                      },
                                    },
                                    "textArea": Object {
                                      "border": "#757575",
                                    },
                                    "textButton": Object {
                                      "color": Object {
                                        "focus": "#007EAB",
                                        "hover": "#005E80",
                                      },
                                    },
                                    "theme": "default",
                                    "tooltip": Object {
                                      "color": Object {
                                        "blue": "#009DD6",
                                        "default": "#333333",
                                      },
                                    },
                                    "transform": Object {
                                      "elevation": Object {
                                        "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
                                      },
                                      "motion": Object {
                                        "100": "200ms",
                                        "150": "350ms",
                                        "200": "500ms",
                                        "300": "0.1s",
                                        "400": "0.2s",
                                        "500": "0.3s",
                                        "600": "0.4s",
                                        "700": "0.5s",
                                        "800": "0.6s",
                                      },
                                      "opacity": Object {
                                        "0": 0,
                                        "100": 1,
                                        "11": 0.11,
                                        "15": 0.15,
                                        "16": 0.16,
                                        "2": 0.02,
                                        "3": 0.03,
                                        "38": 0.38,
                                        "5": 0.05,
                                        "50": 0.5,
                                        "60": 0.6,
                                        "87": 0.87,
                                      },
                                    },
                                    "type": "light",
                                    "typography": Object {
                                      "lineHeight": Object {
                                        "100": "1.8rem",
                                        "1000": "5.4rem",
                                        "200": "2.0rem",
                                        "300": "2.1rem",
                                        "400": "2.2rem",
                                        "500": "2.4rem",
                                        "600": "2.6rem",
                                        "700": "2.7rem",
                                        "800": "3.0rem",
                                        "850": "3.5rem",
                                        "900": "4.0rem",
                                        "910": "4.1rem",
                                      },
                                      "size": Object {
                                        "100": "1.2rem",
                                        "1000": "3.6rem",
                                        "1100": "4.8rem",
                                        "200": "1.4rem",
                                        "300": "1.6rem",
                                        "400": "1.8rem",
                                        "500": "2.0rem",
                                        "600": "2.1rem",
                                        "700": "2.4rem",
                                        "800": "2.8rem",
                                        "900": "3.2rem",
                                      },
                                      "weight": Object {
                                        "bold": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                        "boldItalics": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                        "headline": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                        "italic": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                        "legal": "\\"Scotia Legal\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                        "legalItalic": "\\"Scotia Legal Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                        "regular": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                      },
                                    },
                                  },
                                  "transitions": Object {
                                    "duaration": Object {
                                      "sideSheetMainWrapper": "600ms",
                                    },
                                    "effect": Object {
                                      "easing": Object {
                                        "easeInOut": "ease-in-out",
                                      },
                                    },
                                  },
                                  "type": "light",
                                  "typography": Object {
                                    "fontBoldFamily": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "fontBoldItalicFamily": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "fontHeadlineFamily": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "fontItalicFamily": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "fontLightFamily": "\\"Scotia Light\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                    "fontRegularFamily": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                                  },
                                }
                              }
                            >
                              <button
                                className="ButtonCorestyle__StyledButton-canvas-core__sc-v39ho0-0 kRyMJn ButtonCore__button SecondaryButtonstyle__StyleSecondaryButtonCore-canvas-core__sc-1fquqhk-0 fnyzYu Button__button--secondary errors__button"
                                disabled={false}
                                onClick={[Function]}
                                size="regular"
                              >
                                <ButtonCorestyle__StyledButtonCoreBlock
                                  className="ButtonCore__block"
                                  tabIndex={-1}
                                >
                                  <span
                                    className="ButtonCorestyle__StyledButtonCoreBlock-canvas-core__sc-v39ho0-1 jqQZpC ButtonCore__block"
                                    tabIndex={-1}
                                  >
                                    <span
                                      className="ButtonCore__text"
                                    >
                                      <FormattedMessage
                                        id="errors.cta.tryAgain"
                                        values={Object {}}
                                      >
                                        <span>
                                          Try again
                                        </span>
                                      </FormattedMessage>
                                    </span>
                                  </span>
                                </ButtonCorestyle__StyledButtonCoreBlock>
                              </button>
                            </ButtonCorestyle__StyledButton>
                          </f>
                        </SecondaryButtonstyle__StyleSecondaryButtonCore>
                      </d>
                      <UnauthorizedIcon
                        className="errors__image"
                      >
                        <svg
                          className="errors__image"
                          height="122"
                          viewBox="0 0 176 122"
                          width="176"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <g
                            fill="none"
                            fillRule="evenodd"
                            id="401-Unauthorized-site"
                          >
                            <g
                              id="401Unauthorized"
                              transform="translate(-163 -485)"
                            >
                              <g
                                id="Unauthorized"
                                transform="translate(163 484)"
                              >
                                <g
                                  id="Group-6"
                                  stroke="#EB1D26"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="1.25"
                                  transform="translate(0 117.548)"
                                >
                                  <path
                                    d="M116.55756,1.15030102 L174.905929,1.15030102"
                                    id="Stroke-2"
                                  />
                                  <path
                                    d="M25.6338383,1.15030102 L0.624363121,1.15030102"
                                    id="Stroke-4"
                                  />
                                </g>
                                <path
                                  d="M74.8742695,24.095951 L84.8975319,24.095951"
                                  id="Stroke-7"
                                  stroke="#E2E8EE"
                                  strokeLinecap="round"
                                  strokeWidth="1.25"
                                />
                                <g
                                  id="Group-61"
                                  transform="translate(17.475 .823)"
                                >
                                  <path
                                    d="M64.5988652,19.8554633 L54.5756028,19.8554633 C54.5756028,19.8554633 52.4985532,19.6471163 52.4985532,18.2238306 C52.4985532,16.8005449 53.4035177,16.5934531 53.4035177,16.5934531"
                                    id="Stroke-8"
                                    stroke="#E2E8EE"
                                    strokeLinecap="round"
                                    strokeWidth="1.25"
                                  />
                                  <path
                                    d="M5.1727773,45.9502898 L15.1960397,45.9502898"
                                    id="Stroke-10"
                                    stroke="#E2E8EE"
                                    strokeLinecap="round"
                                    strokeWidth="1.25"
                                  />
                                  <path
                                    d="M86.8920738,118.297635 C86.8920738,120.187818 71.229322,121.720298 51.908017,121.720298 C32.5879603,121.720298 16.9252085,120.187818 16.9252085,118.297635 C16.9252085,116.406196 32.5879603,114.873716 51.908017,114.873716 C71.229322,114.873716 86.8920738,116.406196 86.8920738,118.297635"
                                    fill="#D0D7DA"
                                    id="Fill-12"
                                  />
                                  <path
                                    d="M12.3720511,42.5318939 L2.34878865,42.5318939 C2.34878865,42.5318939 0.271739007,42.324802 0.271739007,40.9015163 C0.271739007,39.4782306 1.17795177,39.2711388 1.17795177,39.2711388"
                                    id="Stroke-14"
                                    stroke="#E2E8EE"
                                    strokeLinecap="round"
                                    strokeWidth="1.25"
                                  />
                                  <polygon
                                    fill="#FFF"
                                    id="Fill-16"
                                    points="39.7398014 117.91646 35.8628085 117.91646 25.1342979 58.9768684 29.4356879 58.9768684"
                                  />
                                  <polygon
                                    id="Stroke-18"
                                    points="39.7398014 117.91646 35.8628085 117.91646 25.1342979 58.9768684 29.4356879 58.9768684"
                                    stroke="#D6D6D6"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.25"
                                  />
                                  <g
                                    id="Group-23"
                                    transform="translate(22.468 84.092)"
                                  >
                                    <polyline
                                      fill="#FFF"
                                      id="Fill-20"
                                      points="48.7351489 0.444306122 50.3828085 0.444306122 50.3828085 16.7367857 0.13293617 16.7367857 0.13293617 0.444306122 1.70695035 0.444306122"
                                    />
                                    <polyline
                                      id="Stroke-22"
                                      points="48.7351489 0.444306122 50.3828085 0.444306122 50.3828085 16.7367857 0.13293617 16.7367857 0.13293617 0.444306122 1.70695035 0.444306122"
                                      stroke="#A0A8AA"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="1.25"
                                    />
                                  </g>
                                  <g
                                    id="Group-27"
                                    transform="translate(42.44 84.092)"
                                  >
                                    <path
                                      d="M1.02654184,0.444306122 L28.7633929,0.444306122"
                                      fill="#FFF"
                                      id="Fill-24"
                                    />
                                    <path
                                      d="M1.02654184,0.444306122 L28.7633929,0.444306122"
                                      id="Stroke-26"
                                      stroke="#DDE5E8"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="1.25"
                                    />
                                  </g>
                                  <g
                                    id="Group-31"
                                    transform="translate(23.716 84.092)"
                                  >
                                    <path
                                      d="M0.458473759,0.444306122 L14.5209986,0.444306122"
                                      fill="#FFF"
                                      id="Fill-28"
                                    />
                                    <path
                                      d="M0.458473759,0.444306122 L14.5209986,0.444306122"
                                      id="Stroke-30"
                                      stroke="#A0A8AA"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="1.25"
                                    />
                                  </g>
                                  <polygon
                                    fill="#DDE5E8"
                                    id="Fill-32"
                                    points="71.2100993 97.7297755 80.8264397 97.7297755 80.8264397 84.5361429 71.2100993 84.5361429"
                                  />
                                  <polygon
                                    fill="#FFF"
                                    id="Fill-34"
                                    points="25.1218156 78.6898776 77.3725957 78.6898776 77.3725957 62.397398 25.1218156 62.397398"
                                  />
                                  <polygon
                                    id="Stroke-35"
                                    points="25.1218156 78.6898776 77.3725957 78.6898776 77.3725957 62.397398 25.1218156 62.397398"
                                    stroke="#A0A8AA"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.25"
                                  />
                                  <polygon
                                    fill="#ED0722"
                                    id="Fill-36"
                                    points="67.6012255 61.8278327 77.784261 79.3201898 70.6543887 79.3201898 60.6947858 61.8278327"
                                  />
                                  <polygon
                                    fill="#ED0722"
                                    id="Fill-37"
                                    points="49.7780426 61.8278327 59.9598298 79.3201898 52.8312057 79.3201898 42.8716028 61.8278327"
                                  />
                                  <polygon
                                    fill="#ED0722"
                                    id="Fill-38"
                                    points="31.9542355 61.8278327 42.1372709 79.3201898 35.0073986 79.3201898 25.0477957 61.8278327"
                                  />
                                  <polygon
                                    fill="#FFF"
                                    id="Fill-39"
                                    points="88.3542468 117.91646 84.4785021 117.91646 73.7487433 58.9768684 78.0501333 58.9768684"
                                  />
                                  <polygon
                                    id="Stroke-40"
                                    points="88.3542468 117.91646 84.4785021 117.91646 73.7487433 58.9768684 78.0501333 58.9768684"
                                    stroke="#D6D6D6"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.25"
                                  />
                                  <path
                                    d="M84.4193362,80.687498 C84.4193362,80.687498 85.4566128,92.6574061 74.5695773,87.5767531"
                                    id="Stroke-41"
                                    stroke="#391572"
                                    strokeLinecap="round"
                                    strokeWidth="1.25"
                                  />
                                  <g
                                    id="Group-44"
                                    transform="translate(17.475 57.735)"
                                  >
                                    <polyline
                                      fill="#FFF"
                                      id="Fill-42"
                                      points="6.88734184 7.31912755 7.65125674 1.24192347 11.9513986 1.24192347 4.12127092 60.1815153 0.244278014 60.1815153 5.99111489 14.4531276"
                                    />
                                    <polyline
                                      id="Stroke-43"
                                      points="6.88734184 7.31912755 7.65125674 1.24192347 11.9513986 1.24192347 4.12127092 60.1815153 0.244278014 60.1815153 5.99111489 14.4531276"
                                      stroke="#A0A8AA"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="1.25"
                                    />
                                  </g>
                                  <polygon
                                    fill="#FFF"
                                    id="Fill-45"
                                    points="72.8513929 117.91646 68.9744 117.91646 73.4829957 58.9768684 77.7843858 58.9768684"
                                  />
                                  <polygon
                                    id="Stroke-46"
                                    points="72.8513929 117.91646 68.9744 117.91646 73.4829957 58.9768684 77.7843858 58.9768684"
                                    stroke="#A0A8AA"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.25"
                                  />
                                  <polygon
                                    fill="#A0A8AA"
                                    id="Fill-47"
                                    points="73.3791433 64.3833459 73.3791433 67.9039071 76.3436823 68.7561214 76.5833418 66.7190908"
                                  />
                                  <polygon
                                    fill="#F3F4F6"
                                    id="Fill-48"
                                    points="26.6364142 85.2408827 24.6954213 100.073679 29.4262014 100.073679 38.2399319 85.2408827"
                                  />
                                  <polygon
                                    fill="#391572"
                                    id="Fill-49"
                                    points="88.5796766 78.3120918 87.7271376 77.1548878 80.6933787 71.3688673 80.2589957 73.8238469 83.8439035 76.5524388 83.8364142 79.3525714 87.0281305 79.9851429"
                                  />
                                  <g
                                    id="Group-52"
                                    transform="translate(63.66 50.204)"
                                  >
                                    <path
                                      d="M9.08759149,3.93625102 L9.02892482,3.97892449 L6.52498156,7.21206735 L3.24214468,5.80886327 C3.24214468,5.80886327 1.68935035,5.08592449 0.879251064,6.04984286 C0.0704,7.01250612 1.14886809,8.45461837 1.14886809,8.45461837 L10.250939,14.2946082 L24.0700596,25.4637612 L26.2856624,28.0141286 L28.6560454,26.8280571 L28.1517617,24.7684347 C28.3914213,24.132098 28.8582582,23.5020367 29.3513078,22.8393429 C29.8156482,22.214302 30.2949674,21.5666694 30.6544567,20.8311796 C33.8886128,14.2230673 30.8479319,6.04231224 23.8790809,2.59454694 C20.9307688,1.13737347 17.4070241,0.846189796 14.1241872,1.6482"
                                      fill="#FFD400"
                                      id="Fill-50"
                                    />
                                    <path
                                      d="M9.08759149,3.93625102 L9.02892482,3.97892449 L6.52498156,7.21206735 L3.24214468,5.80886327 C3.24214468,5.80886327 1.68935035,5.08592449 0.879251064,6.04984286 C0.0704,7.01250612 1.14886809,8.45461837 1.14886809,8.45461837 L10.250939,14.2946082 L24.0700596,25.4637612 L26.2856624,28.0141286 L28.6560454,26.8280571 L28.1517617,24.7684347 C28.3914213,24.132098 28.8582582,23.5020367 29.3513078,22.8393429 C29.8156482,22.214302 30.2949674,21.5666694 30.6544567,20.8311796 C33.8886128,14.2230673 30.8479319,6.04231224 23.8790809,2.59454694 C20.9307688,1.13737347 17.4070241,0.846189796 14.1241872,1.6482"
                                      id="Stroke-51"
                                      stroke="#8230DF"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="1.25"
                                    />
                                  </g>
                                  <path
                                    d="M73.9867801,56.7681398 C73.9867801,56.7681398 77.3557447,53.277701 80.2591206,53.277701 C83.1637447,53.277701 85.6739291,55.3084561 85.6739291,55.3084561 L73.9867801,56.7681398 Z"
                                    fill="#FFF"
                                    id="Fill-53"
                                  />
                                  <path
                                    d="M93.2355631,66.5234204 L84.4193362,69.5959102 C84.4193362,69.5959102 89.1613504,71.6944408 90.0101447,73.7603388 C92.5565277,71.3681143 93.2355631,66.5234204 93.2355631,66.5234204"
                                    fill="#EDAA14"
                                    id="Fill-54"
                                  />
                                  <g
                                    id="Group-57"
                                    transform="translate(96.113)"
                                  >
                                    <polyline
                                      fill="#FFF"
                                      id="Fill-55"
                                      points="3.74230922 8.79977143 0.23853617 12.324098 0.23853617 28.2538531 11.4401248 39.5183939 27.2838695 39.5183939 38.4867064 28.2538531 38.4867064 12.324098 27.2838695 1.05830204 11.4401248 1.05830204 8.62412482 3.89106735"
                                    />
                                    <polyline
                                      id="Stroke-56"
                                      points="3.74230922 8.79977143 0.23853617 12.324098 0.23853617 28.2538531 11.4401248 39.5183939 27.2838695 39.5183939 38.4867064 28.2538531 38.4867064 12.324098 27.2838695 1.05830204 11.4401248 1.05830204 8.62412482 3.89106735"
                                      stroke="#DDE5E8"
                                      strokeLinecap="round"
                                      strokeWidth="1.25"
                                    />
                                  </g>
                                  <path
                                    d="M115.475722,39.6465398 L115.475722,116.619438"
                                    id="Stroke-58"
                                    stroke="#DDE5E8"
                                    strokeWidth="1.875"
                                  />
                                  <polygon
                                    fill="#F3F4F6"
                                    id="Fill-59"
                                    points="121.946031 4.58212653 109.005662 4.58212653 99.8561589 13.7820245 99.8561589 26.7936673 109.005662 35.9935653 121.946031 35.9935653 131.096783 26.7936673 131.096783 13.7820245"
                                  />
                                  <path
                                    d="M108.437345,21.1105653 L123.349912,21.1105653"
                                    id="Stroke-60"
                                    stroke="#FFF"
                                    strokeWidth="6.25"
                                  />
                                </g>
                              </g>
                            </g>
                          </g>
                        </svg>
                      </UnauthorizedIcon>
                    </div>
                  </_Error>
                </ErrorContainer>
              </Connect(ErrorContainer)>
            </Route>
          </ProtectedRoute>
        </Connect(ProtectedRoute)>
      </Router>
    </BrowserRouter>
  </Provider>
</IntlProvider>
`;
