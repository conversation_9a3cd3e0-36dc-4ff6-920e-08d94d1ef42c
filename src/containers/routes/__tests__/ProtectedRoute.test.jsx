import React from 'react';
import { createMockStore } from 'redux-test-utils';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { mount } from 'enzyme';

import { ProtectedRoute } from 'containers/routes';
import en from 'messages/en';

describe('ProtectedRoute', () => {
	const component = () => <div>Hello</div>;

	const mockStoreWithError = createMockStore({
		app: {
			isLoading: false,
		},
		errors: {
			'401': 'sample error',
		},
	});

	const mockStore = createMockStore({
		app: {
			isLoading: false,
		},
		errors: {},
	});

	it('matches the snapshot', () => {
		const wrapper = mount(
			<IntlProvider locale="en" messages={en}>
				<Provider store={mockStore}>
					<BrowserRouter>
						<ProtectedRoute component={component} />
					</BrowserRouter>
				</Provider>
			</IntlProvider>
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches the snapshot with an error', () => {
		const wrapper = mount(
			<IntlProvider locale="en" messages={en}>
				<Provider store={mockStoreWithError}>
					<BrowserRouter>
						<ProtectedRoute component={component} />
					</BrowserRouter>
				</Provider>
			</IntlProvider>
		);

		expect(wrapper).toMatchSnapshot();
	});
})
