import React from 'react';

import { Switch, Route } from 'react-router-dom';
import { ProtectedRoute } from 'containers/routes';
import OffersContainer from '../offers/OffersContainer';
import ContentContainer from 'containers/content/ContentContainer';
import { Error } from 'containers/errors/ErrorContainer';

const Router = () => (
	<Switch>
		<ProtectedRoute path="/ccau/campaigns" exact component={OffersContainer} />
		<ProtectedRoute path="/ccau/campaigns/:id" exact component={ContentContainer} />
		<ProtectedRoute path="/campaigns" exact component={OffersContainer} />
		<ProtectedRoute path="/campaigns/:id" exact component={ContentContainer} />
		<Route render={() => <Error errorCode={'404'} />} />
	</Switch>
);

export default Router;
