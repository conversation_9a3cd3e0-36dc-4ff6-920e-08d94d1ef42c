import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Route } from 'react-router-dom';

import LoadingContainer from 'containers/loading/LoadingContainer';
import ErrorsContainer, { ErrorShape } from 'containers/errors/ErrorContainer';
import { errorSelector } from 'store/errors/errorsSelectors';

const mapStateToProps = (state) => ({
	error: errorSelector(state),
	isLoading: state.app.isLoading,
});
@connect(mapStateToProps, null)
class ProtectedRoute extends React.Component {
	static propTypes = {
		error: ErrorShape,
		component: PropTypes.elementType,
		isLoading: PropTypes.bool.isRequired,
	};

	render() {
		const {
			component: Component,
			error,
			isLoading,
			...rest
		} = this.props;

		return (
			<Route
				render={(props) => !error ? (
				<>
					<LoadingContainer />
					<Component {...props} />
				</>) : <ErrorsContainer />}
				{...rest}
			/>
		);
	}
}

export default ProtectedRoute;
