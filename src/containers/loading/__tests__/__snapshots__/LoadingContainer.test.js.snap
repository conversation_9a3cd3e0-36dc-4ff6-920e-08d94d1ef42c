// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LoadingContainer.jsx should match snapshot when the app is loading 1`] = `
<Provider
  store={
    Object {
      "dispatch": [Function],
      "getAction": [Function],
      "getActions": [Function],
      "getState": [Function],
      "isActionDispatched": [Function],
      "isActionTypeDispatched": [Function],
      "subscribe": [Function],
    }
  }
>
  <Connect(LoadingContainer)>
    <LoadingContainer
      dispatch={[Function]}
      isLoading={true}
    >
      <div
        className="pw-loading"
      >
        <s
          color="gray"
          size={48}
        >
          <Component
            color="gray"
            disabled={false}
            iconType="spinner"
            preserveAspectRatio="xMidYMid"
            size={48}
            viewBox="0 0 100 100"
          >
            <SvgIconstyle__Wrapper
              aria-hidden="true"
              as="svg"
              className="SvgIcon__icon"
              color="gray"
              disabled={false}
              focusable="false"
              iconType="spinner"
              preserveAspectRatio="xMidYMid"
              role="presentation"
              size={48}
              theme={
                Object {
                  "breakPoints": Object {
                    "values": Object {
                      "lg": 1025,
                      "md": 768,
                      "sm": 481,
                      "xs": 0,
                    },
                  },
                  "palette": Object {
                    "actionMenuList": Object {
                      "item": Object {
                        "background": Object {
                          "hover": "#F6F7FC",
                        },
                        "text": "#333",
                      },
                      "menuButton": Object {
                        "background": Object {
                          "hover": "#F6F7FC",
                        },
                        "border": Object {
                          "focus": "#007EAB",
                        },
                        "text": "#333",
                      },
                    },
                    "alertBanner": Object {
                      "alert": Object {
                        "background": "#FFF3EF",
                        "border": "#FDB197",
                      },
                      "error": Object {
                        "background": "#FEECED",
                        "border": "#F5888C",
                      },
                      "info": Object {
                        "background": "#EBF7FC",
                        "border": "#7FCEEA",
                      },
                      "success": Object {
                        "background": "#ECF5F3",
                        "border": "#89C1B3",
                      },
                    },
                    "backToTop": Object {
                      "background": "#FFFFFF",
                      "border": "#E2E8EE",
                      "hover": "#949494",
                    },
                    "background": Object {
                      "card": Object {
                        "hover": "#949494",
                        "keyline": "#E2E8EE",
                        "primary": "#FFFFFF",
                        "secondary": "#FFFFFF",
                      },
                      "modal": Object {
                        "keyline": "#E2E8EE",
                        "overlay": "rgba(0, 0, 0, 0.5)",
                        "primary": "#FFFFFF",
                      },
                      "primary": "#FFFFFF",
                    },
                    "brandColors": Object {
                      "dark": Object {
                        "black": "#757575",
                        "blue": "#91ddf8",
                        "green": "#84d9c6",
                        "orange": "#ffba8e",
                        "pink": "#fda8de",
                        "purple": "#aea9f4",
                        "red": "#ff969c",
                        "white": "#333333",
                        "yellow": "#ffeaa5",
                      },
                      "light": Object {
                        "black": "#333333",
                        "blue": "#009dd6",
                        "darkBlue": "#007EAB",
                        "darkRed": "#BE061B",
                        "green": "#138468",
                        "orange": "#fb6330",
                        "pink": "#f2609e",
                        "purple": "#7849b8",
                        "red": "#ec111a",
                        "white": "#FFFFFF",
                        "yellow": "#ffd42f",
                      },
                    },
                    "button": Object {
                      "close": Object {
                        "color": "#333333",
                        "hover": "#949494",
                      },
                      "disabled": Object {
                        "background": "#F6F6F6",
                        "border": "#D6D6D6",
                        "color": "#757575",
                      },
                      "navigationButtons": Object {
                        "backButtonColor": "#333333",
                        "continueButtonColor": "#ec111a",
                      },
                      "pillButton": Object {
                        "background": "transparent",
                        "border": "#333333",
                        "caution": Object {
                          "color": "#ec111a",
                        },
                        "color": "#333333",
                      },
                      "primary": Object {
                        "background": "#ec111a",
                        "border": "#ec111a",
                        "color": "#FFFFFF",
                      },
                      "secondary": Object {
                        "background": "transparent",
                        "border": "#ec111a",
                        "color": "#ec111a",
                      },
                    },
                    "canvasGrey": Object {
                      "100": "#FAFBFD",
                      "200": "#F6F7FC",
                      "300": "#F6F6F6",
                      "400": "#E2E8EE",
                      "500": "#D6D6D6",
                      "600": "#757575",
                      "700": "#949494",
                    },
                    "canvasShadow": "rgba(0, 34, 91, 0.11)",
                    "charts": Object {
                      "lineChart": Object {
                        "axisLineColor": "#757575",
                        "gridLineColor": "#D6D6D6",
                        "tooltip": Object {
                          "borderColor": "#949494",
                        },
                      },
                      "stackedColumn": Object {
                        "axisLineColor": "#757575",
                        "dataSetColors": Object {
                          "limitedRetail": Array [
                            "#333333",
                            "#009DD6",
                            "#7849B8",
                            "#138468",
                            "#FB6330",
                          ],
                          "retail": Array [
                            "#009DD6",
                            "#7849B8",
                            "#138468",
                            "#F2609E",
                            "#F2609E",
                          ],
                        },
                        "gridLineColor": "#D6D6D6",
                        "tooltip": Object {
                          "borderColor": "#383838",
                        },
                      },
                    },
                    "checkbox": Object {
                      "border": "#757575",
                      "checked": "#007EAB",
                      "focus": Object {
                        "boxShadowColor": Object {
                          "a": "#FFFFFF",
                          "b": "#007EAB",
                        },
                      },
                      "indeterminate": "#949494",
                    },
                    "common": Object {
                      "black": "#333333",
                      "white": "#FFFFFF",
                    },
                    "error": "#be061b",
                    "focusOutline": "#007EAB",
                    "footer": Object {
                      "base": Object {
                        "background": "#F6F7FC",
                      },
                      "border": "#E2E8EE",
                      "link": Object {
                        "hover": "#333333",
                      },
                      "logo": Object {
                        "fill": "#ec111a",
                      },
                    },
                    "hamburger": Object {
                      "menu": Object {
                        "background": "#FFFFFF",
                      },
                    },
                    "icons": Object {
                      "category": Object {
                        "circle": "#E2E8EE",
                        "path": "#757575",
                      },
                      "functional": Object {
                        "black": "#333333",
                        "blue": "#007EAB",
                        "darkBlue": "#007EAB",
                        "darkRed": "#BE061B",
                        "green": "#138468",
                        "orange": "#fb6330",
                        "pink": "#f2609e",
                        "purple": "#7849b8",
                        "red": "#ec111a",
                        "white": "#FFFFFF",
                        "yellow": "#ffd42f",
                      },
                    },
                    "link": Object {
                      "emphasis": Object {
                        "color": "#007EAB",
                        "hover": "#005E80",
                      },
                    },
                    "loadingIndicator": Object {
                      "background": "#949494",
                    },
                    "meter": Object {
                      "borderColor": "#949494",
                      "complete": "#138468",
                      "incomplete": "#F6F6F6",
                    },
                    "pagination": Object {
                      "disabled": "#949494",
                      "selected": "#ec111a",
                    },
                    "pinTextField": Object {
                      "pinItem": Object {
                        "backgroundGray": "#F6F7FC",
                      },
                    },
                    "primary": "#009dd6",
                    "quickActionCard": Object {
                      "chevron": Object {
                        "black": "#333333",
                      },
                    },
                    "radio": Object {
                      "focus": Object {
                        "boxShadowColor": Object {
                          "a": "#FFFFFF",
                          "b": "#007EAB",
                        },
                      },
                    },
                    "search": Object {
                      "border": "#757575",
                    },
                    "snackBar": Object {
                      "background": "#2c2c2e",
                      "button": Object {
                        "color": "#009DD6",
                        "hover": "#05BCFF",
                      },
                      "color": "#FFFFFF",
                    },
                    "stepTracker": Object {
                      "border": "#949494",
                      "incompleteBackground": "#D6D6D6",
                    },
                    "tabs": Object {
                      "border": "#383838",
                      "hover": "#757575",
                    },
                    "text": Object {
                      "disabled": "#949494",
                      "highEmphasis": "#333333",
                      "mediumEmphasis": "#757575",
                      "placeholderText": "#949494",
                    },
                    "textArea": Object {
                      "border": "#757575",
                    },
                    "toggleSwitch": Object {
                      "iconOff": "#949494",
                      "iconOn": "#007EAB",
                    },
                    "tooltip": Object {
                      "color": Object {
                        "blue": "#009dd6",
                        "default": "#333333",
                      },
                    },
                  },
                  "tokens": Object {
                    "action": Object {
                      "focus": Object {
                        "default": "#007EAB",
                        "outline": "#009DD6",
                      },
                      "hover": Object {
                        "background": Object {
                          "actionMenuList": Object {
                            "default": "rgba(226,232,238,0.5)",
                          },
                          "pillButton": Object {
                            "caution": "#BE061B",
                            "default": "#333333",
                            "inverted": "#FFFFFF",
                          },
                          "pillButtonPrimary": Object {
                            "default": "#BE061B",
                            "inverted": "#333333",
                          },
                          "pillButtonSecondary": Object {
                            "black": "#333333",
                            "red": "#BE061B",
                            "textOnly": "#333333",
                          },
                        },
                        "border": Object {
                          "pillButtonSecondary": Object {
                            "black": "#333333",
                            "red": "#BE061B",
                            "textOnly": "transparent",
                          },
                        },
                        "default": "#005E80",
                        "text": Object {
                          "pillButton": Object {
                            "caution": "#FFFFFF",
                            "default": "#FFFFFF",
                          },
                          "pillButtonPrimary": Object {
                            "default": "#FFFFFF",
                            "inverted": "#FFFFFF",
                          },
                          "pillButtonSecondary": Object {
                            "black": "#FFFFFF",
                            "red": "#FFFFFF",
                            "textOnly": "#FFFFFF",
                          },
                          "snackbar": Object {
                            "button": "#05BCFF",
                          },
                        },
                      },
                    },
                    "background": Object {
                      "alertBanner": Object {
                        "alert": "rgba(251,99,48,0.08)",
                        "error": "rgba(236,17,26,0.08)",
                        "info": "rgba(0,157,214,0.08)",
                        "new": "rgba(120,73,184,0.08)",
                        "success": "rgba(19,132,104,0.08)",
                      },
                      "base": "#FFFFFF",
                      "comboBox": Object {
                        "default": "#FFFFFF",
                        "listBoxItem": Object {
                          "hover": "#F6F6F6",
                        },
                      },
                      "footer": Object {
                        "base": Object {
                          "grey": "#F6F7FC",
                          "white": "#FFFFFF",
                        },
                        "logo": Object {
                          "default": "#EC111A",
                        },
                        "text": Object {
                          "hover": "#333333",
                        },
                      },
                      "header": Object {
                        "black": "#333333",
                        "red": "#EC111A",
                        "white": "#FFFFFF",
                      },
                      "meter": Object {
                        "default": "#138468",
                        "incomplete": "#F6F6F6",
                      },
                      "modal": "#FFFFFF",
                      "navigation": Object {
                        "default": "#FFFFFF",
                      },
                      "overlay": "rgba(0, 0, 0, 0.5)",
                      "pillButtonPrimary": Object {
                        "default": "#EC111A",
                        "inverted": "#FFFFFF",
                      },
                      "pillButtonSecondary": Object {
                        "black": "transparent",
                        "red": "transparent",
                        "textOnly": "transparent",
                      },
                      "primary": "#FFFFFF",
                      "progressIndicator": Object {
                        "bar": Object {
                          "default": "#138468",
                        },
                        "container": Object {
                          "default": "#F6F6F6",
                        },
                      },
                      "secondary": "#FFFFFF",
                      "skeleton": "#949494",
                      "snackbar": "#2C2C2C",
                      "statusBadge": Object {
                        "default": "transparent",
                        "emphasis": "transparent",
                        "error": "transparent",
                        "new": "#7849B8",
                        "success": "#138468",
                        "success-emphasis": "transparent",
                      },
                      "table": Object {
                        "row": Object {
                          "alternate": "#FAFBFD",
                          "hover": "rgba(226, 232, 238, 0.4)",
                          "selected": "#F7F7F8",
                        },
                      },
                    },
                    "border": Object {
                      "alertBanner": Object {
                        "alert": "rgba(251,99,48,0.5)",
                        "error": "rgba(236,17,26,0.5)",
                        "info": "rgba(0,157,214,0.5)",
                        "new": "rgba(120,73,184,0.5)",
                        "success": "rgba(19,132,104,0.5)",
                      },
                      "card": "#E2E8EE",
                      "default": "#707070",
                      "header": Object {
                        "white": "#E2E8EE",
                      },
                      "meter": Object {
                        "default": "#949494",
                      },
                      "pillButton": Object {
                        "caution": "#EC111A",
                        "default": "#333333",
                      },
                      "pillButtonPrimary": Object {
                        "default": "#EC111A",
                        "inverted": "#FFFFFF",
                      },
                      "pillButtonSecondary": Object {
                        "black": "#333333",
                        "red": "#EC111A",
                        "textOnly": "transparent",
                      },
                      "progressIndicator": Object {
                        "container": Object {
                          "default": "#949494",
                        },
                      },
                      "skeleton": "#949494",
                      "statusBadge": Object {
                        "default": "#707070",
                        "emphasis": "#007EAB",
                        "error": "#BE061B",
                        "new": "#7849B8",
                        "success": "#138468",
                        "success-emphasis": "#138468",
                      },
                    },
                    "checkbox": Object {
                      "border": "#757575",
                      "card": Object {
                        "border": Object {
                          "focus": "#007EAB",
                          "hover": "#949494",
                        },
                      },
                      "checked": "#007EAB",
                      "focus": Object {
                        "boxShadowColor": Object {
                          "a": "#FFFFFF",
                          "b": "#007EAB",
                        },
                      },
                      "indeterminate": "#949494",
                    },
                    "closeButton": Object {
                      "border": "#949494",
                      "color": "#333333",
                    },
                    "fileUpload": Object {
                      "background": "#007EAB",
                      "border": "#007EAB",
                      "color": "#007EAB",
                    },
                    "grid": Object {
                      "breakPoints": Object {
                        "lg": Object {
                          "gutter": 3,
                          "margin": "3.6rem",
                          "maxCols": 12,
                          "maxWidth": "4000px",
                          "minWidth": "1025px",
                          "rowMargin": "-1.8rem",
                          "size": "lg",
                        },
                        "md": Object {
                          "gutter": 3,
                          "margin": Array [
                            "3.6rem",
                            "4.2rem",
                            "4.8rem",
                            "5.4rem",
                          ],
                          "maxCols": 12,
                          "maxWidth": "1024px",
                          "minWidth": "768px",
                          "rowMargin": "-1.5rem",
                          "size": "md",
                        },
                        "sm": Object {
                          "gutter": 1.8,
                          "margin": Array [
                            "2.4rem",
                            "3.0rem",
                            "3.6rem",
                            "4.2rem",
                          ],
                          "maxCols": 8,
                          "maxWidth": "767px",
                          "minWidth": "481px",
                          "rowMargin": "-1.2rem",
                          "size": "sm",
                        },
                        "xs": Object {
                          "gutter": 1.8,
                          "margin": Array [
                            "1.8rem",
                            "2.4rem",
                            "3.0rem",
                            "3.6rem",
                          ],
                          "maxCols": 4,
                          "maxWidth": "480px",
                          "minWidth": "0px",
                          "rowMargin": "-0.6rem",
                          "size": "xs",
                        },
                      },
                      "containerMaxWidth": 1200,
                      "containerMaxWidthMargins": 1272,
                      "containerMinWidth": 320,
                      "gridColumns": 12,
                    },
                    "icons": Object {
                      "functional": Object {
                        "black": "#333333",
                        "blue": "#007EAB",
                        "darkBlue": "#007EAB",
                        "darkGray": "#707070",
                        "darkRed": "#BE061B",
                        "gray": "#949494",
                        "green": "#138468",
                        "orange": "#FB6330",
                        "pink": "#F2609E",
                        "purple": "#7849B8",
                        "red": "#EC111A",
                        "white": "#FFFFFF",
                        "yellow": "#FFD42F",
                      },
                    },
                    "loadingIndicator": Object {
                      "darkGray": "#949494",
                      "red": "#EC111A",
                    },
                    "mode": "light",
                    "palette": Object {
                      "color": Object {
                        "base": "#FFFFFF",
                        "black": "#333333",
                        "blue": "#009DD6",
                        "brandRed": "#EC111A",
                        "darkBlue": "#007EAB",
                        "darkBlue100": "#005E80",
                        "darkGreen": "#117E63",
                        "darkRed": "#BE061B",
                        "darkRed100": "#BE061B",
                        "green": "#138468",
                        "green100": "rgb(132,217,198,0.15)",
                        "orange": "#FB6330",
                        "pink": "#F2609E",
                        "primaryRed": "#EC111A",
                        "purple": "#7849B8",
                        "yellow": "#FFD42F",
                      },
                      "gray": Object {
                        "0": "#FFFFFF",
                        "100": "#FAFBFD",
                        "200": "#F6F7FC",
                        "300": "#F6F6F6",
                        "400": "#E2E8EE",
                        "500": "#D6D6D6",
                        "550": "#949494",
                        "600": "#707070",
                        "700": "#333333",
                        "800": "#2C2C2C",
                      },
                    },
                    "pillButtonPrimary": Object {
                      "default": Object {
                        "backgroundColor": "#EC111A",
                        "borderColor": "#EC111A",
                        "textColor": "#FFFFFF",
                      },
                      "hover": Object {
                        "default": Object {
                          "backgroundColor": "#BE061B",
                          "borderColor": "#BE061B",
                          "textColor": "#FFFFFF",
                        },
                        "inverted": Object {
                          "backgroundColor": "#333333",
                          "borderColor": "#333333",
                          "textColor": "#FFFFFF",
                        },
                      },
                      "inverted": Object {
                        "backgroundColor": "#FFFFFF",
                        "borderColor": "#FFFFFF",
                        "textColor": "#333333",
                      },
                    },
                    "primary": Object {
                      "dark": "#BE061B",
                      "main": "#EC111A",
                    },
                    "secondary": Object {
                      "dark": "#005E80",
                      "main": "#007EAB",
                    },
                    "secondaryButton": Object {
                      "default": Object {
                        "backgroundColor": "#FFFFFF",
                        "borderColor": "#EC111A",
                        "textColor": "#EC111A",
                      },
                      "hover": Object {
                        "default": Object {
                          "backgroundColor": "#BE061B",
                          "borderColor": "#BE061B",
                          "textColor": "#FFFFFF",
                        },
                      },
                    },
                    "sidesheet": Object {
                      "overlay": Object {
                        "background": "#FFFFFF",
                      },
                      "persistent": Object {
                        "background": "#FFFFFF",
                      },
                    },
                    "size": Object {
                      "borderRadius": Object {
                        "0": "10rem",
                        "100": "0.4rem",
                        "200": "0.8rem",
                        "300": "1.2rem",
                        "50": "0.2rem",
                      },
                      "borderWidth": Object {
                        "1": "0.1rem",
                        "2": "0.2rem",
                        "3": "0.3rem",
                        "5": "0.5rem",
                      },
                      "breakPoints": Object {
                        "lg": "1024px",
                        "md": "768px",
                        "sm": "375px",
                        "xs": "0px",
                      },
                      "sizing": Object {
                        "24": "2.4rem",
                        "32": "3.2rem",
                        "36": "3.6rem",
                        "42": "4.2rem",
                        "44": "4.4rem",
                        "48": "4.8rem",
                        "54": "5.4rem",
                        "60": "6.0rem",
                        "72": "7.2rem",
                      },
                      "spacing": Object {
                        "1": "0.6rem",
                        "10": "6.0rem",
                        "11": "6.6rem",
                        "12": "7.2rem",
                        "13": "7.8rem",
                        "14": "8.4rem",
                        "15": "9.0rem",
                        "16": "9.6rem",
                        "17": "10.2rem",
                        "18": "10.8rem",
                        "19": "11.4rem",
                        "2": "1.2rem",
                        "20": "12.0rem",
                        "3": "1.8rem",
                        "4": "2.4rem",
                        "5": "3.0rem",
                        "6": "3.6rem",
                        "7": "4.2rem",
                        "8": "4.8rem",
                        "9": "5.4rem",
                      },
                    },
                    "state": Object {
                      "disabled": Object {
                        "background": "#F6F6F6",
                        "border": "#D6D6D6",
                        "text": "#949494",
                      },
                      "error": Object {
                        "default": "#BE061B",
                      },
                    },
                    "text": Object {
                      "highEmphasis": "#333333",
                      "medEmphasis": "#707070",
                      "pillButton": Object {
                        "caution": "#EC111A",
                        "default": "#333333",
                        "inverted": "#333333",
                      },
                      "pillButtonPrimary": Object {
                        "default": "#FFFFFF",
                        "inverted": "#333333",
                      },
                      "pillButtonSecondary": Object {
                        "black": "#333333",
                        "red": "#EC111A",
                        "textOnly": "#333333",
                      },
                      "placeholder": "#707070",
                      "secondaryButton": "#EC111A",
                      "snackbar": Object {
                        "button": "#009DD6",
                        "content": "#E0E0E0",
                      },
                      "statusBadge": Object {
                        "default": "#707070",
                        "emphasis": "#007EAB",
                        "error": "#BE061B",
                        "new": "#FFFFFF",
                        "success": "#FFFFFF",
                        "success-emphasis": "#138468",
                      },
                    },
                    "textArea": Object {
                      "border": "#757575",
                    },
                    "textButton": Object {
                      "color": Object {
                        "focus": "#007EAB",
                        "hover": "#005E80",
                      },
                    },
                    "theme": "default",
                    "tooltip": Object {
                      "color": Object {
                        "blue": "#009DD6",
                        "default": "#333333",
                      },
                    },
                    "transform": Object {
                      "elevation": Object {
                        "100": "0px 2px 10px rgba(0, 34, 91, 0.11)",
                      },
                      "motion": Object {
                        "100": "200ms",
                        "150": "350ms",
                        "200": "500ms",
                        "300": "0.1s",
                        "400": "0.2s",
                        "500": "0.3s",
                        "600": "0.4s",
                        "700": "0.5s",
                        "800": "0.6s",
                      },
                      "opacity": Object {
                        "0": 0,
                        "100": 1,
                        "11": 0.11,
                        "15": 0.15,
                        "16": 0.16,
                        "2": 0.02,
                        "3": 0.03,
                        "38": 0.38,
                        "5": 0.05,
                        "50": 0.5,
                        "60": 0.6,
                        "87": 0.87,
                      },
                    },
                    "type": "light",
                    "typography": Object {
                      "lineHeight": Object {
                        "100": "1.8rem",
                        "1000": "5.4rem",
                        "200": "2.0rem",
                        "300": "2.1rem",
                        "400": "2.2rem",
                        "500": "2.4rem",
                        "600": "2.6rem",
                        "700": "2.7rem",
                        "800": "3.0rem",
                        "850": "3.5rem",
                        "900": "4.0rem",
                        "910": "4.1rem",
                      },
                      "size": Object {
                        "100": "1.2rem",
                        "1000": "3.6rem",
                        "1100": "4.8rem",
                        "200": "1.4rem",
                        "300": "1.6rem",
                        "400": "1.8rem",
                        "500": "2.0rem",
                        "600": "2.1rem",
                        "700": "2.4rem",
                        "800": "2.8rem",
                        "900": "3.2rem",
                      },
                      "weight": Object {
                        "bold": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                        "boldItalics": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                        "headline": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                        "italic": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                        "legal": "\\"Scotia Legal\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                        "legalItalic": "\\"Scotia Legal Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                        "regular": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                      },
                    },
                  },
                  "transitions": Object {
                    "duaration": Object {
                      "sideSheetMainWrapper": "600ms",
                    },
                    "effect": Object {
                      "easing": Object {
                        "easeInOut": "ease-in-out",
                      },
                    },
                  },
                  "type": "light",
                  "typography": Object {
                    "fontBoldFamily": "\\"Scotia Bold\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                    "fontBoldItalicFamily": "\\"Scotia Bold Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                    "fontHeadlineFamily": "\\"Scotia Headline\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                    "fontItalicFamily": "\\"Scotia Italic\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                    "fontLightFamily": "\\"Scotia Light\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                    "fontRegularFamily": "\\"Scotia Regular\\", \\"Arial\\", \\"Helvetica\\", \\"sans-serif\\"",
                  },
                }
              }
              viewBox="0 0 100 100"
            >
              <svg
                aria-hidden="true"
                className="SvgIconstyle__Wrapper-canvas-core__sc-15g7y6h-0 iBFupd SvgIcon__icon"
                color="gray"
                disabled={false}
                focusable="false"
                preserveAspectRatio="xMidYMid"
                role="presentation"
                size={48}
                viewBox="0 0 100 100"
              >
                <circle
                  cx="50"
                  cy="50"
                  fill="none"
                  r="45"
                  strokeDasharray="164.93361431346415 56.97787143782138"
                  strokeWidth="7"
                  transform="rotate(243.787 50 50)"
                >
                  <animateTransform
                    attributeName="transform"
                    begin="0s"
                    calcMode="linear"
                    dur="6s"
                    keyTimes="0;1"
                    repeatCount="indefinite"
                    type="rotate"
                    values="0 50 50;360 50 50"
                  />
                </circle>
              </svg>
            </SvgIconstyle__Wrapper>
          </Component>
        </s>
      </div>
    </LoadingContainer>
  </Connect(LoadingContainer)>
</Provider>
`;

exports[`LoadingContainer.jsx should match snapshot when the app is not loading 1`] = `
<Provider
  store={
    Object {
      "dispatch": [Function],
      "getAction": [Function],
      "getActions": [Function],
      "getState": [Function],
      "isActionDispatched": [Function],
      "isActionTypeDispatched": [Function],
      "subscribe": [Function],
    }
  }
>
  <Connect(LoadingContainer)>
    <LoadingContainer
      dispatch={[Function]}
      isLoading={false}
    />
  </Connect(LoadingContainer)>
</Provider>
`;
