import React from 'react';
import { createMockStore } from 'redux-test-utils';
import { mount } from 'enzyme';
import { Provider } from 'react-redux';

import LoadingContainer from 'containers/loading/LoadingContainer';

describe('LoadingContainer.jsx', () => {
	const loadingState = {
		app: {
			isLoading: false,
		},
	};

	it('should match snapshot when the app is not loading', () => {
		const store = createMockStore(loadingState);
		const wrapper = mount(
			<Provider store={store}>
				<LoadingContainer />
			</Provider>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('should match snapshot when the app is loading', () => {
		loadingState.app.isLoading = true;
		const store = createMockStore(loadingState);
		const wrapper = mount(
			<Provider store={store}>
				<LoadingContainer />
			</Provider>,
		);
		expect(wrapper).toMatchSnapshot();
	});
});
