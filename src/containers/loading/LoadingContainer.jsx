import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import IconSpinner from 'canvas-core-react/lib/IconSpinner';

const mapStateToProps = (state) => ({
	isLoading: state.app.isLoading,
});

@connect(mapStateToProps, null)
class LoadingContainer extends React.Component {
	static propTypes = {
		isLoading: PropTypes.bool.isRequired,
	};

	render() {
		const { isLoading } = this.props;

		return isLoading && (
			<div className="pw-loading">
				<IconSpinner size={48} color="gray" />
			</div>
		);
	}
}

export default LoadingContainer;
