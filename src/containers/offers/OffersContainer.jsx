import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import queryString from 'query-string';

import OfferPreviews from '../../components/offers/OfferPreviews';
import { fetchOffers } from 'store/offers/offersActions';
import { setDisposition } from '../../store/dispositions/dispositionsActions';
import { listPageLoad, trackAction, trackDismiss } from '../../analytics';
import { getGUIDFromCookie } from 'utils/cookieUtils';

const mapStateToProps = (state) => ({
	offers: state.offers.offersList,
});

const mapDispatchToProps = (dispatch) => ({
	fetchOffers: (args) => dispatch(fetchOffers(args)),
	setDisposition: (args) => dispatch(setDisposition(args)).catch(e => console.error('offer container page failed to set disposition')),
});

@connect(mapStateToProps, mapDispatchToProps)
class OffersContainer extends React.Component {
	static propTypes = {
		offers: PropTypes.object,
		location: PropTypes.shape({
			search: PropTypes.string,
		}).isRequired,
		fetchOffers: PropTypes.func.isRequired,
		setDisposition: PropTypes.func.isRequired,
	};

	componentDidMount() {
		this.props.fetchOffers({ ...this.queryParams });
	}

	async componentDidUpdate(prevProps) {
		const { offers } = this.props;
		const guid = await getGUIDFromCookie();
		if (!prevProps.offers && offers) {
			listPageLoad(offers, guid, document.URL, window.analyticsEnv);
		}
	}

	handleDismiss = (id, messageId, ruleName) => {
		this.props.setDisposition({ id, messageId, disposition: 'D', ...this.queryParams });
		trackDismiss(ruleName);
	}

	onCtaClick = (ctaLink, messageId, name, id) => async (page, href, eventType, inlineLinkText) => {
		// If CTA does not call campaign details endpoint in pigeon, send viewed disposition
		if (ctaLink && ctaLink.linkAction && ctaLink.linkAction.url !== 'pigeon-web') {
			await this.props.setDisposition({ id, messageId, disposition: 'V', ...this.queryParams });
		}
		trackAction(name, id, page, href, eventType, inlineLinkText);
	}

	get queryParams() {
		return queryString.parse(this.props.location.search);
	}

	render() {
		const { offers } = this.props;

		const pageName = this.queryParams && this.queryParams.page;

		const application = this.queryParams && this.queryParams.application;

		if (!offers) {
			return null;
		}
		return (
			<OfferPreviews
				offers={offers}
				pageName={pageName}
				handleDismiss={this.handleDismiss}
				onCtaClick={this.onCtaClick}
				application={application}
			/>
		);
	}
}

export default OffersContainer;
