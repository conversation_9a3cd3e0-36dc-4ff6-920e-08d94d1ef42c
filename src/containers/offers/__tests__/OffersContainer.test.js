import React from 'react';
import { createMockStore } from 'redux-test-utils';
import { IntlProvider } from 'react-intl';
import { mount } from 'enzyme';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as analytics from '../../../analytics';
import { setDisposition } from '../../../store/dispositions/dispositionsActions';

import OffersContainer from 'containers/offers/OffersContainer';

jest.mock('../../../store/dispositions/dispositionsActions', () => ({
	setDisposition: jest.fn(() => {}),
}));

describe('OffersContainer.jsx', () => {
	const blankOffersState = {
		offers: {
			offersList: {},
		},
	};

	it('should match snapshot', () => {
		const store = createMockStore(blankOffersState);
		const wrapper = mount(
			<Provider store={store}>
				<IntlProvider
					locale="en"
					messages={{
						'offers.recommendedOffers': 'hey',
						'offers.activeOffersSubheading': 'subheading',
					}}
				>
					<OffersContainer location={{}} />
				</IntlProvider>
			</Provider>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('should match snapshot - no offers', () => {
		const store = createMockStore({
			offers: {
				offersList: null,
			},
		});
		const wrapper = mount(
			<Provider store={store}>
				<IntlProvider
					locale="en"
					messages={{
						'offers.recommendedOffers': 'hey',
						'offers.activeOffersSubheading': 'subheading',
					}}
				>
					<OffersContainer location={{}} />
				</IntlProvider>
			</Provider>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('should match snapshot - no offers', () => {
		const store = createMockStore({
			offers: {
				offersList: {
					'0N54bCD28hmM:0N54bCD28hmM': {
						id: '0N54bCD28hmM',
						name: 'pg_XYI01_ D2D-x-iTrade-Batch-P-WOiT_Nova_Priority_EN_BNS-23-003_0323_v2',
						messageId: '0N54bCD28hmM',
						title: 'Your Preferred Package just got better!',
						description: 'Get up to 20 free trades at Scotia iTRADE.',
						image: null,
						imageDark: null,
						imageAltText: null,
						type: 'targetedCampaignPreview',
						ctaLink: {
							name: 'Pigeon_Web_Offers_Preview_Learn_More',
							linkText: 'Learn more',
							linkAction: {
								name: 'Learn More',
								url: 'pigeon-web',
							},
							accessibilityText: 'Learn more',
						},
						application: 'nova',
						container: 'priority-box',
						page: 'accounts',
						dismissable: true,
						hasBeenDismissed: false,
						snoozed: false,
						viewed: true,
					},
				},
				offersSortOrder: [ '0N54bCD28hmM:0N54bCD28hmM' ],
			},
		});
		const wrapper = mount(
			<BrowserRouter>
				<Provider store={store}>
					<IntlProvider
						locale="en"
						messages={{
							'offers.recommendedOffers': 'hey',
							'offers.activeOffersSubheading': 'subheading',
						}}
					>
						<OffersContainer location={{}} />
					</IntlProvider>
				</Provider>
			</BrowserRouter>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('should handle onCtaClick', async () => {
		analytics.trackAction = jest.fn();
		const store = createMockStore({
			offers: {
				offersList: {
					'0N54bCD28hmM:0N54bCD28hmM': {
						id: '0N54bCD28hmM',
						name: 'pg_XYI01_ D2D-x-iTrade-Batch-P-WOiT_Nova_Priority_EN_BNS-23-003_0323_v2',
						messageId: '0N54bCD28hmM',
						title: 'Your Preferred Package just got better!',
						description: 'Get up to 20 free trades at Scotia iTRADE.',
						image: null,
						imageDark: null,
						imageAltText: null,
						type: 'targetedCampaignPreview',
						ctaLink: {
							name: 'Pigeon_Web_Offers_Preview_Learn_More',
							linkText: 'Learn more',
							linkAction: {
								name: 'Learn More',
								url: 'pigeon-web',
							},
							accessibilityText: 'Learn more',
						},
						application: 'nova',
						container: 'priority-box',
						page: 'accounts',
						dismissable: true,
						hasBeenDismissed: false,
						snoozed: false,
						viewed: true,
					},
				},
				offersSortOrder: [ '0N54bCD28hmM:0N54bCD28hmM' ],
			},
		});
		render(
			<BrowserRouter>
				<Provider store={store}>
					<IntlProvider
						locale="en"
						messages={{
							'offers.recommendedOffers': 'hey',
							'offers.activeOffersSubheading': 'subheading',
						}}
					>
						<OffersContainer location={{}} />
					</IntlProvider>
				</Provider>
			</BrowserRouter>,
		);

		userEvent.click(screen.getByRole('button', { name: /learn more/i }));
		await waitFor(() => expect(analytics.trackAction).toHaveBeenCalledTimes(1));
	});

	it('should handle setDisposition', async () => {
		analytics.trackAction = jest.fn();
		const store = createMockStore({
			offers: {
				offersList: {
					'0N54bCD28hmM:0N54bCD28hmM': {
						id: '0N54bCD28hmM',
						name: 'XXpg_XYI01_ D2D-x-iTrade-Batch-P-WOiT_Nova_Priority_EN_BNS-23-003_0323_v2',
						messageId: '0N54bCD28hmM',
						title: 'Your Preferred Package just got better!',
						description: 'Get up to 20 free trades at Scotia iTRADE.',
						image: null,
						imageDark: null,
						imageAltText: null,
						type: 'targetedCampaignPreview',
						ctaLink: {
							name: 'Pigeon_Web_Offers_Preview_Learn_More',
							linkText: 'Learn more',
							linkAction: {
								name: 'Learn More',
								url: 'pigeon://disposition/?disposition=V',
							},
							accessibilityText: 'Learn more',
						},
						application: 'nova',
						container: 'priority-box',
						page: 'accounts',
						dismissable: true,
						hasBeenDismissed: false,
						snoozed: false,
						viewed: true,
					},
				},
				offersSortOrder: [ '0N54bCD28hmM:0N54bCD28hmM' ],
			},
		});
		render(
			<BrowserRouter>
				<Provider store={store}>
					<IntlProvider
						locale="en"
						messages={{
							'offers.recommendedOffers': 'hey',
							'offers.activeOffersSubheading': 'subheading',
						}}
					>
						<OffersContainer location={{
							search: 'disposition',
						}} />
					</IntlProvider>
				</Provider>
			</BrowserRouter>,
		);

		userEvent.click(screen.getByRole('button', { name: /learn more/i }));
		await waitFor(() => expect(setDisposition).toHaveBeenCalledTimes(1));
	});
});
