import React from 'react';
import PropTypes from 'prop-types';
import queryString from 'query-string';
import { connect } from 'react-redux';
import { GenericTemplate, ITradeDetails, NovaSoftMsgDetails, CcauGenericTemplate } from 'pigeon-pigeon-web-renderer';
import { withLDConsumer } from 'launchdarkly-react-client-sdk';
import { get } from 'lodash';

import { setPlatform } from '../../store/app/appActions';
import { getCookieProperty } from '../../utils/cookieUtils';
import { activeContentSelector } from '../../store/content/contentSelectors';
import { fetchContent, setContentId, fulfillCampaign } from '../../store/content/contentActions';
import { setDisposition } from '../../store/dispositions/dispositionsActions';
import { getOpaqueToken } from '../../api/content';
import { trackAction, detailsPageLoad, trackInlineLink } from '../../analytics';
import { getSOLEndpointFromCookie, getEExperienceParamsFromCookie, getGUIDFromCookie } from 'utils/cookieUtils';
import { campaignPreviewTypes, selectContentsMappedByMode } from 'store/content/constants';
import { isAndroid, isIOS } from 'react-device-detect';

import Form from 'components/form/Form';

const mapStateToProps = (state) => ({
	activeContent: activeContentSelector(state),
	page: state.app.page,
	platform: state.app.platform,
});

const mapDispatchToProps = (dispatch) => ({
	fetchContent: (args) => dispatch(fetchContent(args)),
	setContentId: (id) => dispatch(setContentId(id)),
	setPlatform: (args) => dispatch(setPlatform(args)),
	fulfillCampaign: (id, messageId, type) => dispatch(fulfillCampaign({ id, messageId, campaignType: type })),
	setDisposition: (args) => dispatch(setDisposition(args)).catch(e => console.error('setDisposition failed', e.stack)),
});

@connect(mapStateToProps, mapDispatchToProps)
class ContentContainer extends React.Component {
	static propTypes = {
		activeContent: PropTypes.object,
		fetchContent: PropTypes.func.isRequired,
		setContentId: PropTypes.func.isRequired,
		setPlatform: PropTypes.func.isRequired,
		location: PropTypes.shape({
			search: PropTypes.string,
		}).isRequired,
		match: PropTypes.shape({
			params: PropTypes.shape({
				id: PropTypes.string,
			}),
		}),
		page: PropTypes.string,
		platform: PropTypes.string,
		campaignId: PropTypes.string,
		fulfillCampaign: PropTypes.func.isRequired,
		setDisposition: PropTypes.func.isRequired,
		flags: PropTypes.shape({
			pigeonWebFeaturesEexpOauthSso: PropTypes.bool,
		}),
	}

	inlineLinks = [];
	inlineLinksEventAttached = false;
	state = {
		opaqueToken: '',
	}

	async componentDidMount() {
		const { activeContent, fetchContent: fetchContentDetails, setContentId: setContentDetailsId, match, platform } = this.props;
		const values = this.queryParams;

		const id = match.params.id;
		const messageId = values.message_id;
		const mode = values.mode;
		const selectContents = selectContentsMappedByMode[mode];
		const channel = values.channel;
		fetchContentDetails({ id, messageId, selectContents, channel });

		setContentDetailsId(id);

		if (activeContent && get(activeContent, 'metadata.ruleId') === id) { // ensure activeContent data is not stale
			this.reportPageView();
			this.attachEventsToLinks();
		}

		if (!platform) {
			// platform is initialized on preview page, empty value here indicates content container was loaded directly
			// order of fallbacks:
			// 1. query params
			const platformQuery = this.queryParams.platform;
			// 2. page request headers that were reflected into cookies by bff middleware
			const xApplicationCookie = getCookieProperty('x-application');
			const xApplication = xApplicationCookie ? xApplicationCookie.toUpperCase() : undefined;
			// temp mapping until all clients start sending platform by query param
			const platformHeaderMap = { 'N1': 'ios', 'N2': 'android' };
			const platformHeader = platformHeaderMap[xApplication];
			if (platformQuery || platformHeader) {
				this.props.setPlatform(platformQuery || platformHeader);
			} else {
				if (isAndroid) {
					this.props.setPlatform('android');
				} else if (isIOS) {
					this.props.setPlatform('ios');
				} else {
					this.props.setPlatform('web');
				}
			}
		}
		// If legacy authentication need to fetch opaque token to authenticate
		if (!this.state.opaqueToken) {
			const opaqueToken = await getOpaqueToken(this.props.match.params.id);
			this.setState({ opaqueToken });
		}
	}

	trackInlineLink = (clickEvent) => {
		const {
			page,
			activeContent,
		} = this.props;
		const { metadata } = activeContent;
		return trackInlineLink(clickEvent)(metadata.ruleName, metadata.ruleId, page);
	}

	attachEventsToLinks = () => {
		if (this.inlineLinksEventAttached) {
			return false;
		}
		this.inlineLinks = document.getElementsByClassName('pw-link-trackable');
		Array.from(this.inlineLinks, link => link.addEventListener('click', this.trackInlineLink));
		this.inlineLinksEventAttached = true;
	}

	componentDidUpdate(prevProps) {
		const { activeContent, platform } = this.props;
		if (!prevProps.activeContent && activeContent) {
			this.reportPageView();
			this.attachEventsToLinks();
		}
		const activeContentUpdated = prevProps.activeContent !== activeContent;
		const platformUpdated = prevProps.platform !== platform;
		if (activeContentUpdated || platformUpdated) {
			// Set specific dark mode bg color for starburst's pw details page
			if (activeContent && activeContent.type === campaignPreviewTypes.iTradePriorityBoxDetails && platform === 'ios' && getCookieProperty('dark-mode') === 'true') {
				document.documentElement.setAttribute('dark-mode-ios', true);
			} else if (document.documentElement.getAttribute('dark-mode-ios')) {
				document.documentElement.removeAttribute('dark-mode-ios');
			}
		}
	}

	componentWillUnmount() {
		// clean up inline links tracking
		Array.from(this.inlineLinks, link => link.removeEventListener('click', this.trackInlineLink));
		this.inlineLinks = [];
		this.inlineLinksEventAttached = false;
	}

	handleCampaignFulfillment = (type) => () => {
		const { activeContent: { metadata: { messageId } } } = this.props;
		const { match: { params: { id } } } = this.props;
		this.props.fulfillCampaign(id, messageId, type);
	}

	get queryParams() {
		return queryString.parse(this.props.location.search);
	}

	async reportPageView() {
		const {
			activeContent: {
				name,
				metadata: {
					ruleId,
					messageId,
					ruleName,
				},
			},
		} = this.props;
		const guid = await getGUIDFromCookie();
		detailsPageLoad(messageId, ruleId, name, ruleName, guid, document?.URL, window.analyticsEnv);
	}

	render() {
		const currentUrl = new URL(window.location.href);
		const isCcau = currentUrl.pathname.split('/')[1] === 'ccau';

		const { activeContent, flags: { pigeonWebFeaturesEexpOauthSso: isEExpSsoEnabled } } = this.props;
		if (!activeContent) {
			return null;
		}
		const { match: { params: { id } }, platform } = this.props;
		const { metadata, icon, heading, description, alertBanner, primaryLink, snoozeLink, continueLink,
		} = activeContent;
		const { ruleId, ruleName, messageId, messageCategory } = metadata;

		if (isCcau) {
			return (

				<CcauGenericTemplate
					id={ruleId}
					type={activeContent.type}
					messageId={messageId}
					content={activeContent}
					trackAction={(page, href, eventType, inlineLinkText) =>
						trackAction(ruleName, ruleId, page, href, eventType, inlineLinkText)
					}
					openCtaLinkInNewTab={false}
					onClickHandlers={() => {}}
					onFulfillCampaign={this.handleCampaignFulfillment}
				/>
			);
		}

		if (activeContent.type === campaignPreviewTypes.iTradePriorityBoxDetails) {
			return (
				<ITradeDetails description={activeContent.description} platform={platform} />
			);
		}

		if (activeContent.type === campaignPreviewTypes.NovaSoftMsgDetails) {
			return (
				<NovaSoftMsgDetails
					id={ruleId}
					messageId={messageId}
					image={icon}
					heading={heading}
					description={description}
					alertBanner={alertBanner}
					primaryLink={primaryLink}
					snoozeLink={snoozeLink}
					continueLink={continueLink}
					onDispose={async (disposition) => this.props.setDisposition({
						id, messageId, messageCategory, platform, disposition, ...this.queryParams,
					})}
					trackAction={(page, href, eventType, inlineLinkText) =>
						trackAction(ruleName, ruleId, page, href, eventType, inlineLinkText)
					}
				/>
			);
		}

		return (
			<>
				<GenericTemplate
					content={activeContent}
					messageId={metadata.messageId}
					eExperiencePostParams={{ ...getEExperienceParamsFromCookie(isEExpSsoEnabled, this.state.opaqueToken), messageId: metadata.messageId }}
					solEndpoint={getSOLEndpointFromCookie()}
					trackAction={(page, href, eventType, inlineLinkText) =>
						trackAction(metadata.ruleName, metadata.ruleId, page, href, eventType, inlineLinkText)
					}
					onFulfillCampaign={this.handleCampaignFulfillment}
				/>
				<Form />
			</>
		);
	}
}

export default withLDConsumer()(ContentContainer);
