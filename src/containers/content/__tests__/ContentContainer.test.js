import React from 'react';
import { createMockStore } from 'redux-test-utils';

import ContentContainer from 'containers/content/ContentContainer';
import { mountWithStore, shallowWithStore } from '../../../utils/testUtils';
import * as reactDeviceDetect from 'react-device-detect';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import { setPlatform } from '../../../store/app/appActions';

Object.defineProperty(reactDeviceDetect, 'browserName', { get: () => 'Chrome' });

jest.mock('api/content', () => ({
	getOpaqueToken: jest.fn(() => {}),
}));

jest.mock('../../../store/app/appActions', () => ({
	setPlatform: jest.fn(),
}));

const originalLocation = window.location;
const originalHeaders = window.headers;

describe('ContentContainer.jsx', () => {
	const contentList = {
		'template-id': {
			metadata: {
				messageId: 'fake',
				campaignId: 'fake campaign id',
				ruleName: 'fake rule name',
			},
			type: 'standingCampaignTemplate1Details',
			badgeIcon: 'testBadgeIcon',
			card: {
				productImage: {
					image: {
						file: {
							url: 'icon.svg',
						},
					},
					altText: 'altText',
				},
			},
			details: 'testDetails',
			ctaLink: {
				linkAction: {
					url: 'testurl',
				},
				linkText: 'heyLink',
			},
			title: 'testTitle',
			subtitle: 'testSubtitle',
			footer: 'testFooter',
		},
		akyc: {
			type: 'nova__soft-messaging-details',
			application: 'nova',
			container: 'priority-box',
			metadata: {
				ruleName: 'tony test PIGEON-3237 kyc',
				ruleId: 'akyc',
				messageId: 'akyc',
				campaignId: 'MESSAGE',
			},
			name: 'AML-KYC low risk group',
			icon: {
				title: 'test akyc shield',
				file: {
					url: '//images.ctfassets.net/4szkx38resvm/c5eIXkJGjV5Lq5K36pSup/f4c8f71d6ee1d24b2ac6d528c2493426/aml-kyc.svg',
					details: {
						size: 5441,
						image: {
							width: 184,
							height: 144,
						},
					},
					fileName: 'aml-kyc.svg',
					contentType: 'image/svg+xml',
				},
			},
			heading: 'Verify your profile to continue digital banking',
			description: 'Reviewing your profile info won’t take long, and it helps ensure your account is safe, secure, and up to date.',
			alertBanner: 'After __SOLUI_EXPDATE_END(July 22, 2022)__ you won’t have access to digital banking.',
			primaryLink: {
				name: 'test akyc verify link',
				linkText: 'Verify my profile',
				linkAction: {
					name: 'test akyc verify url',
					url: 'https://kyc-refresh-ma-ist.apps.stg.azr-cc-pcf.cloud.bns/api/session?returnURI=scotiabank://nova/close&amlTrigger=005',
				},
			},
			snoozeLink: {
				name: 'test akyc snooze link',
				linkText: 'Remind me later',
				linkAction: {
					name: 'test akyc snooze url',
					url: 'pigeon://disposition?disposition=S&returnURI=scotiabank://nova/close',
				},
			},
		},
		itrade: {
			metadata: {
				ruleName: 'itrade test',
				ruleId: 'itrade',
				messageId: 'itrade',
				campaignId: 'MESSAGE',
			},
			'type': 'itrade__priority-details',
			'description': 'Please be advised of the holiday hours for Canadian US Markets as well as our Cusomer Service Centre for your reference:\n\n__Note:__ banking services including cheque request & EFT withdrawals will not be available on Canadian holidays described below.\n\n__Thursday, Dec 24th, 2020__\n<mark>Canadian and US equity market will close at 1:00 pm EST</mark>\n\n<mark>Customer Service Call Centre will remain open from 8:00 am to 6:00 pm EST</mark>\n\n<mark>Bond market will close at 1:00 pm EST</mark>\n<mark>GIC market will close at 12:00 pm EST</mark>\n\n__Friday, Dec 25th, 2020__\n<mark>All markets and Customer Service Call Centre closed.</mark>\n\n__Monday, Dec 28th, 2020__\n<mark>Canadian equity market closed.</mark>\n\n<mark>US equity market opens regular trading hours.</mark>\n\n<mark>Customer Service Call Centre opens from 8:00 am to 6:00 pm EST</mark>',

		},
		ccau_intercept_details: {
			'type': 'ccau_intercept_details',
			'container': 'wave-intercept',
			metadata: {
				ruleName: 'wave test',
				ruleId: 'wave',
				messageId: 'wave',
				campaignId: 'MESSAGE',
			},
			'content': {
				'name': 'WAVE - Intercept - Details ',
				'isScotiaLogoVisible': true,
				'contents': [
					{
						'name': 'WAVE - Intercept - Update ID',
						'heroImage': {
							'name': 'WAVE - Intercept - Image',
							'lightModeImage': {
								'title': 'update-id',
								'description': '',
								'file': {
									'url': '//images.ctfassets.net/gk5ecj6dpckc/1JC9CYXfDWK3NPQP47inqT/df376c910db7e1ddc7e24862f9bc841f/update-id.svg',
									'details': {
										'size': 9584,
										'image': {
											'width': 174,
											'height': 152,
										},
									},
									'fileName': 'update-id.svg',
									'contentType': 'image/svg+xml',
								},
							},
							'altText': 'ID Image',
						},
						'heading': 'We need to update your personal information',
						'description': 'It seems like your __SOLUI_CCAU_IDV_ID_TYPE_END__ has expired. To make your banking experience safer, please update it to a valid document.',
					},
				],
				'cancelCta': {
					'name': 'WAVE - Intercept - Skip',
					'accessibilityText': 'Cancel button',
					'linkText': 'Skip',
					'linkAction': {
						'name': 'WAVE - Skip CTA',
						'url': 'scotia://wave/close',
					},
				},
				'acceptCta': {
					'name': 'WAVE - Intercept - Continue ',
					'accessibilityText': 'Accept button',
					'linkText': 'Continue',
					'linkAction': {
						'name': 'deeplink to cob intro page',
						'url': 'http://apply.online.scointnet.net//profile/id-remediation/id-verification?lang=en&country=DO&campaignMessageId=SOLUI_MESSAGE_ID_END&requiredIdType=SOLUI_CCAU_IDV_ID_TYPE_END',
					},
				},
			},
		},
	};

	const populatedContentStoreState = {
		app: {
			token: 'mock-token',
			language: 'en',
		},
		content: {
			id: 'template-id',
			contentList,
			SAMLToken: {
				token: 'mock-token',
				campaignType: 'scotiahome',
			},
		},
	};

	const nullContentStoreState = {
		app: {
			token: 'mock-token',
			language: 'en',
		},
		content: {
			contentList: null,
		},
	};

	afterEach(() => {
		window.location = originalLocation;
		window.heders = originalHeaders;
	});

	it('should match render a list', () => {
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should match render an experience', () => {
		const populatedContentStoreState = {
			content: {
				id: 'experience-id',
				contentList,
			},
			app: {
				token: 'blabla',
			},
		};
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should render a template', () => {
		const populatedContentStoreState = {
			content: {
				id: 'template-id',
				contentList,
				SAMLToken: {
					token: 'mock-token',
					campaignType: 'scrl',
				},
			},
			app: {
				token: 'blabla',
			},
		};
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('can handle contentList being null', () => {
		const store = createMockStore(nullContentStoreState);
		const component = shallowWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should trigger various callbacks correctly', () => {
		const mockStore = { ...populatedContentStoreState };
		mockStore.content.id = 'akyc';
		const store = createMockStore(mockStore);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should set platform from query param', () => {
		const populatedContentStoreState = {
			content: {
				id: 'experience-id',
				contentList,
			},
			app: {
				token: 'blabla',
			},
		};
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{ search: 'platform=ios' }} match={{ params: { } }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should set platform from reactDeviceDetect (android)', () => {
		Object.defineProperty(reactDeviceDetect, 'isAndroid', { get: () => true });

		const populatedContentStoreState = {
			content: {
				id: 'experience-id',
				contentList,
			},
			app: {
				token: 'blabla',
			},
		};
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should set platform from reactDeviceDetect (ios)', () => {
		Object.defineProperty(reactDeviceDetect, 'isAndroid', { get: () => false });
		Object.defineProperty(reactDeviceDetect, 'isIOS', { get: () => true });

		const populatedContentStoreState = {
			content: {
				id: 'experience-id',
				contentList,
			},
			app: {
				token: 'blabla',
			},
		};
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should set platform from reactDeviceDetect (web)', () => {
		Object.defineProperty(reactDeviceDetect, 'isAndroid', { get: () => false });
		Object.defineProperty(reactDeviceDetect, 'isIOS', { get: () => false });

		const populatedContentStoreState = {
			content: {
				id: 'experience-id',
				contentList,
			},
			app: {
				token: 'blabla',
			},
		};
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should set platform from x-application cookie', () => {
		const populatedContentStoreState = {
			content: {
				id: 'experience-id',
				contentList,
			},
			app: {
				token: 'blabla',
			},
		};
		document.cookie = 'x-application=N1';
		const store = createMockStore(populatedContentStoreState);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should render Itrade', () => {
		const mockStore = { ...populatedContentStoreState };
		mockStore.content.id = 'itrade';
		const store = createMockStore(mockStore);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should render CcauGenericTemplate', () => {
		const location = new URL('http://localhost:8080/ccau/campaigns?page=accounts&platform=ios&country=DO&language=en');
		location.assign = jest.fn();
		location.replace = jest.fn();
		location.reload = jest.fn();

		delete window.location;
		window.location = location;
		const mockStore = { ...populatedContentStoreState };
		mockStore.content.id = 'ccau_intercept_details';
		const store = createMockStore(mockStore);
		const component = mountWithStore(<ContentContainer location={{}} match={{ params: {} }}/>, store);
		expect(component).toMatchSnapshot();
	});

	it('should render with platform query', async () => {
		const populatedContentStoreState2 = {
			app: {
				token: 'mock-token',
				language: 'en',
				platform: 'ios',
			},
			content: {
				id: 'template-id',
				contentList,
				SAMLToken: {
					token: 'mock-token',
					campaignType: 'scotiahome',
				},
			},
		};
		const store = createMockStore(populatedContentStoreState2);
		render(
			<MemoryRouter initalEntries={[ '/campaigns' ]}>
				<Provider store={store}>
					<ContentContainer location={{}} match={{ params: {} }}
					/>
				</Provider>
			</MemoryRouter>,

		);

		await waitFor(() =>	expect(screen.getByText(/testsubtitle/i)).toBeInTheDocument(),
		);
	});

	it('should get xApplication from headers', async () => {
		const populatedContentStoreState2 = {
			app: {
				token: 'mock-token',
				language: 'en',
			},
			content: {
				id: 'template-id',
				contentList,
				SAMLToken: {
					token: 'mock-token',
					campaignType: 'scotiahome',
				},
			},
		};
		const mockHeaders = JSON.stringify({
			'x-application': 'N1',
		});
		window.headers = mockHeaders;
		const store = createMockStore(populatedContentStoreState2);

		render(
			<MemoryRouter initalEntries={[ '/campaigns' ]}>
				<Provider store={store}>
					<ContentContainer location={{}} match={{ params: {} }}/>
				</Provider>
			</MemoryRouter>,

		);

		await waitFor(() => expect(setPlatform).toHaveBeenCalled());
	});
});
