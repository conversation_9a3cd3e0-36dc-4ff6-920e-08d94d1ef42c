const httpContext = require('express-http-context');
jest.mock('express-http-context');

const {
	getTraceId,
	setTraceId,
	getSpanId,
	setSpanId,
	getPreferredEnvironment,
	setPreferredEnvironment } = require('./httpContext');

describe('Http Context Middleware', () => {
	beforeEach(() => {
		httpContext.get.mockReset();
		httpContext.set.mockReset();
	});

	test('should get context value of Traceid in httpContext', function() {
		httpContext.get.mockImplementationOnce(() => '1234');
		const result = getTraceId();
		expect(result).toBe('1234');
	});

	test('it should set context value of TraceId in httpContext', function() {
		setTraceId('1234');
		expect(httpContext.set.mock.calls[0][1]).toBe('1234');
	});

	test('should get context value of SpanId in httpContext', function() {
		httpContext.get.mockImplementationOnce(() => '1000');
		const result = getSpanId();
		expect(result).toBe('1000');
	});

	test('it should set context value of SpanId in httpContext', function() {
		setSpanId('1000');
		expect(httpContext.set.mock.calls[0][1]).toBe('1000');
	});

	test('should get context value of preferredEnvironment in httpContext', function() {
		httpContext.get.mockImplementationOnce(() => 'istuattest');
		const result = getPreferredEnvironment();
		expect(result).toBe('istuattest');
	});

	test('it should set context value of preferredEnvironment in httpContext', function() {
		setPreferredEnvironment('istuattest');
		expect(httpContext.set.mock.calls[0][1]).toBe('istuattest');
	});
});
