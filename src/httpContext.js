const httpContext = require('express-http-context');

const getTraceId = () => httpContext.get('traceId');
const setTraceId = (traceId) => httpContext.set('traceId', traceId);

const getSpanId = () => httpContext.get('spanId');
const setSpanId = (spanId) => httpContext.set('spanId', spanId);

const getPreferredEnvironment = () => httpContext.get('preferredEnvironment');
const setPreferredEnvironment = (preferredEnvironment) => httpContext.set('preferredEnvironment', preferredEnvironment);

const contextMiddleware = httpContext.middleware;

module.exports = {
	getTraceId,
	setTraceId,
	getSpanId,
	setSpanId,
	getPreferredEnvironment,
	setPreferredEnvironment,
	contextMiddleware,
};
