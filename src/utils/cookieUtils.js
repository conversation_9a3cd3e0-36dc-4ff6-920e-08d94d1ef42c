/* eslint-disable no-undef */
import cookie from 'cookie';
import getWebTrackingId from 'api/getWebTrackingId';
import { SOLEndpoints } from 'store/content/constants';

export const getCookieProperty = (propertyName) => {
	const { headers } = window;
	const parsedHeader = headers && Object.keys(headers).length > 0 ? JSON.parse(headers) : {};
	return parsedHeader[propertyName] || null;
};

export const getLanguageFromCookie = () => {
	const language = getCookieProperty('x-language');
	return language ? language.split('-')[0] : 'en'; // accounting for en-CA, fr-CA, en, fr, etc.
};

const setUniqueUserCookie = (name, value) => {
	const expires = new Date();
	expires.setFullYear(expires.getFullYear() + 2);
	const uniqueUserCookie = cookie.serialize(name, value, {
		httpOnly: false,
		sameSite: 'lax',
		secure: true,
		expires,
	});
	document.cookie = uniqueUserCookie;
};

export const getGUIDFromCookie = async () => {
	let userCookie = localStorage.getItem('UserTrackingID') || localStorage.getItem('UniqueUser');
	if (!userCookie) {
		userCookie = await getWebTrackingId();
	} else {
		setUniqueUserCookie('UniqueUser', userCookie);
		setUniqueUserCookie('UserTrackingID', userCookie);
	}
	return userCookie;
};

export const getXEnvironmentFromCookie = () => getCookieProperty('x-environment');

export const getDeploymentEnvironment = () => getCookieProperty('deploy-environment');

export const getSOLEndpointFromCookie = () => {
	const preferredEnvironment = getCookieProperty('preferred-environment') || process.env.SOL_DEFAULT_ENVIRONMENT || 'prd';
	return SOLEndpoints[preferredEnvironment];
};

export const getEExperienceParamsFromCookie = (isEExpSsoEnabled = false, opaqueToken = undefined) => ({
	...(!isEExpSsoEnabled ? { token: opaqueToken } : {}),
	language: getLanguageFromCookie(),
	...(getCookieProperty('x-channel-id') && { 'x-channel-id': getCookieProperty('x-channel-id') }),
	...(getCookieProperty('x-application') && { 'x-application': getCookieProperty('x-application') }),
});
