import { mount, shallow } from 'enzyme';
import { Provider } from 'react-redux';
import { ThemeProvider } from 'styled-components';
import getCanvasTheme from 'canvas-core-tokens/lib';
import React from 'react';

const themeMock = getCanvasTheme();

export const shallowWithStore = (component, store) => {
	return shallow(<Provider store={store}>{component}</Provider>);
};

export const mountWithStore = (component, store) => {
	return mount(
		<ThemeProvider theme={themeMock}>
			<Provider store={store}>{component}</Provider>
		</ThemeProvider>
	);
};
