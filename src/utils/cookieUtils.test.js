import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import {
	getGUIDFromCookie,
	getEExperienceParamsFromCookie,
	getSOLEndpointFromCookie,
} from './cookieUtils';

const opaqueToken = 'user-token';
const userTrackingId = 'user-tracking-id';
class LocalStorageMock {
	constructor() {
		this.store = {};
	}
	clear() {
		this.store = {};
	}
	getItem(key) {
		return this.store[key] || null;
	}
	setItem(key, value) {
		this.store[key] = String(value);
	}
	removeItem(key) {
		delete this.store[key];
	}
}
global.localStorage = new LocalStorageMock();

describe('CookieUtils', () => {
	test('getGUIDFromCookie - getWebTrackingId', async () => {
		const mock = new MockAdapter(axios);
		mock.onGet().replyOnce(200, { data: { web_track_id: 7 } });
		const response = await getGUIDFromCookie();
		expect(response).toStrictEqual(7);
	});

	test('getGUIDFromCookie - no user cookie', async () => {
		global.localStorage.setItem('UserTrackingID', userTrackingId);
		const response = await getGUIDFromCookie();
		expect(response).toStrictEqual(userTrackingId);
	});

	test('getSOLEndpointFromCookie', async () => {
		process.env.SOL_DEFAULT_ENVIRONMENT = 'IST';
		window.headers = '{"preferred-environment":"IST"}';
		const response = getSOLEndpointFromCookie();
		expect(response).toStrictEqual(undefined);
	});

	test('getEExperienceParamsFromCookie - sso auth + default language', () => {
		window.headers = '{"x-channel-id": "Mobile", "x-application": "N1"}';
		const response = getEExperienceParamsFromCookie(true);
		expect(response).toStrictEqual({ language: 'en', 'x-channel-id': 'Mobile', 'x-application': 'N1' });
	});

	test('getEExperienceParamsFromCookie - opaque token auth + language in cookie', () => {
		window.headers = '{"x-language": "en", "x-channel-id": "Mobile", "x-application": "N1"}';
		const response = getEExperienceParamsFromCookie(false, opaqueToken);
		expect(response).toStrictEqual({ token: opaqueToken, language: 'en', 'x-channel-id': 'Mobile', 'x-application': 'N1' });
	});
});
