import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { FormattedMessage } from 'react-intl';
import { useFlags } from 'launchdarkly-react-client-sdk';
import {
	ITradePreview,
	HarmonyCampaignPreview,
	HarmonyRewardOfferPreview,
	TargetedCampaignPreviewCard,
	StandingCampaignPreviewCard,
	NeoPreview,
	AtlantisPreview,
} from 'pigeon-pigeon-web-renderer';
import TextSubtitle from 'canvas-core-react/lib/TextSubtitle';
import TextIntroduction from 'canvas-core-react/lib/TextIntroduction';
import TextBody from 'canvas-core-react/lib/TextBody';

import { OffersIcon, EmptyBoxIcon } from 'components/icons';

import { campaignPreviewTypes, pageNames } from '../../store/content/constants';
import { getEExperienceParamsFromCookie, getSOLEndpointFromCookie, getLanguageFromCookie } from 'utils/cookieUtils';
import { trackAction } from '../../analytics';
import { getOpaqueToken } from 'api/content';

const OfferPreviews = ({
	offers,
	pageName, // the environment to determine what SOL endpoint is sent to eExperience
	handleDismiss,
	onCtaClick,
	application,
}) => {
	const [ opaqueToken, setOpaqueToken ] = useState('');
	const { pigeonWebFeaturesEexpOauthSso: isEExpSsoEnabled } = useFlags();

	useEffect(() => {
		const init = async () => {
			// need to fetch opaque token to authenticate if sso isn't enabled
			if (isEExpSsoEnabled === false && offers && Object.values(offers).length) {
				const ot = await getOpaqueToken(Object.values(offers)[0].id);
				setOpaqueToken(ot);
			}
		};
		init();
	}, [ offers, isEExpSsoEnabled ]);

	const numOffers = () => offers ? Object.values(offers).filter(offer => !offer.hasBeenDismissed && !offer.snoozed).length : 0;

	const noOffers = () => numOffers() === 0;

	const renderOffers = () => {
		if (!offers) {
			return null;
		}

		return Object.keys(offers).map((key) => {
			const offer = offers[key];
			let previewCard;

			if (offer.hasBeenDismissed || offer.snoozed) {
				return null;
			}

			switch (offer.type) {
				case campaignPreviewTypes.standingCampaign:
					previewCard = (
						<StandingCampaignPreviewCard
							id={offer.id}
							messageId={offer.messageId}
							description={offer.description}
							icon={offer.icon}
							badge={offer.badge}
							linkUrl={offer.linkUrl}
							eExperiencePostParams={{ ...getEExperienceParamsFromCookie(isEExpSsoEnabled, opaqueToken), messageId: offer.messageId }}
							solEndpoint={getSOLEndpointFromCookie()}
							trackAction={(page, href, eventType, inlineLinkText) =>
								trackAction(offer.name, offer.id, page, href, eventType, inlineLinkText)
							}
							language={getLanguageFromCookie()}
						/>
					);
					break;
				case campaignPreviewTypes.targetedCampaign:
					previewCard = (
						<TargetedCampaignPreviewCard
							id={offer.id}
							messageId={offer.messageId}
							title={offer.title}
							description={offer.description}
							image={offer.image}
							imageDark={offer.imageDark}
							imageAltText={offer.imageAltText}
							ctaLink={offer.ctaLink}
							isDismissible={offer.dismissable}
							onDismiss={() => handleDismiss(offer.id, offer.messageId, offer.name)}
							isNew={!offer.viewed}
							eExperiencePostParams={{ ...getEExperienceParamsFromCookie(isEExpSsoEnabled, opaqueToken), messageId: offer.messageId }}
							solEndpoint={getSOLEndpointFromCookie()}
							trackAction={onCtaClick(offer.ctaLink, offer.messageId, offer.name, offer.id)}
						/>
					);
					break;
				case campaignPreviewTypes.iTradePriorityBoxPreview:
					if (application === 'neo') {
						previewCard = (
							<NeoPreview
								id={offer.id}
								messageId={offer.messageId}
								description={offer.description}
								ctaLink={offer.ctaLink}
								isDismissible={offer.dismissable}
								onDismiss={() => handleDismiss(offer.id, offer.messageId, offer.name)}
								trackAction={onCtaClick(offer.ctaLink, offer.messageId, offer.name, offer.id)}
							/>
						);
					} else {
						previewCard = (
							<ITradePreview
								id={offer.id}
								messageId={offer.messageId}
								description={offer.description}
								ctaLink={offer.ctaLink}
								isDismissible={offer.dismissable}
								onDismiss={() => handleDismiss(offer.id, offer.messageId, offer.name)}
								trackAction={onCtaClick(offer.ctaLink, offer.messageId, offer.name, offer.id)}
							/>
						);
					}

					break;
				case campaignPreviewTypes.atlantisPriorityPreview:
					previewCard = (
						<AtlantisPreview
							id={offer.id}
							messageId={offer.messageId}
							description={offer.description}
							ctaLink={offer.ctaLink}
							isDismissible={offer.dismissable}
							onDismiss={() => handleDismiss(offer.id, offer.messageId, offer.name)}
							trackAction={onCtaClick(offer.ctaLink, offer.messageId, offer.name, offer.id)}
						/>
					);
					break;
				case campaignPreviewTypes.harmonyCampaign:
					previewCard = (
						<HarmonyCampaignPreview
							id={offer.id}
							messageId={offer.messageId}
							title={offer.title}
							description={offer.description}
							icon={offer.icon}
							iconAltText={offer.iconAltText}
							ctaLink={offer.ctaLink}
							isDismissible={offer.dismissable}
							onDismiss={() => handleDismiss(offer.id, offer.messageId, offer.name)}
							trackAction={onCtaClick(offer.ctaLink, offer.messageId, offer.name, offer.id)}
							isDarkMode={false}
						/>
					);
					break;
				case campaignPreviewTypes.harmonyReward:
				case campaignPreviewTypes.harmonyOffer:
					previewCard = (
						<HarmonyRewardOfferPreview
							title={offer.title}
							description={offer.description}
							validity={offer.validity}
							image={offer.image}
							imageDark={offer.imageDark}
							imageAltText={offer.imageAltText}
							isDismissible={offer.dismissable}
							onDismiss={() => handleDismiss(offer.id, offer.messageId, offer.name)}
							isDarkMode={false}
						/>
					);
					break;
				default:
					return null;
			}

			return (
				<li key={key} className="offer-previews__list-item">
					{ previewCard }
				</li>
			);
		});
	};

	const renderNumOffersSubheading = () => {
		const activeOffersText = (
			<TextBody component="span" type="2" color="black" bold>
				{ numOffers() === 1
					? <FormattedMessage id="offers.oneActiveOffer"/>
					: <FormattedMessage
						id="offers.otherActiveOffers"
						values={{ num: numOffers() }}
					/>
				}
			</TextBody>
		);

		return (
			<FormattedMessage
				id="offers.activeOffersSubheading"
				values={{ offerText: activeOffersText }}
			/>
		);
	};

	const renderOffersAndProgramsPreviews = () => (
		<div className="offer-previews">
			<div className="offer-previews__text-container">
				<OffersIcon />
				<TextIntroduction component="h1" color="black" className="offer-previews__heading">
					<FormattedMessage id={`offers.${noOffers() ? 'noOffers' : 'recommendedOffers'}`} />
				</TextIntroduction>
				<TextBody component="span" type="2" color="black" className="offer-previews__subheading">
					{ renderNumOffersSubheading() }
				</TextBody>
			</div>
			<ul className="offer-previews__list">
				{ renderOffers() }
			</ul>
			{ noOffers() && <EmptyBoxIcon className="offer-previews__empty-box"/> }
		</div>
	);

	const renderStandingCampaignsPreviews = () => (
		<div className="offer-previews">
			{noOffers() &&
				<TextSubtitle component="h1" type="1" color="black" className="offer-previews__subtitle">
					There are no standing campaigns to preview
				</TextSubtitle>
			}
			<ul className="offer-previews__list">
				{ renderOffers() }
			</ul>
		</div>
	);

	return pageNames.offersAndPrograms.includes(pageName) ? renderOffersAndProgramsPreviews() : renderStandingCampaignsPreviews();
};

OfferPreviews.propTypes = {
	offers: PropTypes.object,
	pageName: PropTypes.string,
	language: PropTypes.string,
	application: PropTypes.string,
};

export default OfferPreviews;
