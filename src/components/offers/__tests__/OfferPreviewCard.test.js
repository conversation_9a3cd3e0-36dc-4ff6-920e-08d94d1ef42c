import React from 'react';
import { IntlProvider } from 'react-intl';
import { shallow } from 'enzyme';

import { TargetedCampaignPreviewCard, StandingCampaignPreviewCard, ITradePreview } from 'pigeon-pigeon-web-renderer';

describe('PreviewCard component testing', () => {
	test('TargetedCampaignPreviewCard matches snapshshot', () => {
		const intlProvider = new IntlProvider({
			locale: 'en',
			messages: { 'offers.learnMoreAriaDescription': 'Learn more button' },
		}, {});
		const { intl } = intlProvider.getChildContext();

		// This test takes the core component and adds the intl provider as a prop,
		// which allows us to snapshot the component itself rather than the IntlProvider
		// https://github.com/yahoo/react-intl/wiki/Testing-with-React-Intl
		const wrapper = shallow(
			<TargetedCampaignPreviewCard
				id="fake-id"
				messageId="fake-message-id"
				imageAltText="super-card"
				image={'http://fakeimage.net'}
				imageDark={'http://fakeimage.net'}
				title="wowweeee"
				description="description"
				ctaLink={{
					accessibilityText: 'test',
					linkText: 'test',
					linkAction: {
						url: 'http://mock.com/',
					},
				}}
				intl={intl}
				isNew={false}
				idDismissible={true}
				onDismiss={jest.fn()}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	test('StandingCampaignPreviewCard matches snapshshot', () => {
		const intlProvider = new IntlProvider({
			locale: 'en',
			messages: { 'offers.learnMoreAriaDescription': 'Learn more button' },
		}, {});
		const { intl } = intlProvider.getChildContext();

		// This test takes the core component and adds the intl provider as a prop,
		// which allows us to snapshot the component itself rather than the IntlProvider
		// https://github.com/yahoo/react-intl/wiki/Testing-with-React-Intl
		const wrapper = shallow(
			<StandingCampaignPreviewCard
				id="fake-id"
				messageId="fake-message-id"
				cardName="super-card"
				cardImage={{ file: { url: 'http://fakeimage.net' } }}
				title="wowweeee"
				description="description"
				intl={intl}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	test('ITradePreview matches snapshshot', () => {
		const intlProvider = new IntlProvider({
			locale: 'en',
			messages: { 'offers.learnMoreAriaDescription': 'Learn more button' },
		}, {});
		const { intl } = intlProvider.getChildContext();

		// This test takes the core component and adds the intl provider as a prop,
		// which allows us to snapshot the component itself rather than the IntlProvider
		// https://github.com/yahoo/react-intl/wiki/Testing-with-React-Intl
		const wrapper = shallow(
			<ITradePreview
				id="fake-id"
				messageId="fake-message-id"
				description="description"
				ctaLink={{
					accessibilityText: 'test',
					linkText: 'test',
					linkAction: {
						url: 'http://mock.com/',
					},
				}}
				intl={intl}
				idDismissible={true}
				onDismiss={jest.fn()}
				trackAction={jest.fn()}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});
});
