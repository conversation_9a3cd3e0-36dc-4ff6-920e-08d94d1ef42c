// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OfferPreviews component testing matches snapshot - no offers 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.noOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.otherActiveOffers"
                values={
                  Object {
                    "num": 0,
                  }
                }
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  />
  <EmptyBoxIcon
    className="offer-previews__empty-box"
  />
</div>
`;

exports[`OfferPreviews component testing matches snapshot for an unsupported previewType 1`] = `
<div
  className="offer-previews"
>
  <ul
    className="offer-previews__list"
  />
</div>
`;

exports[`OfferPreviews component testing matches snapshot for my activity 1`] = `
<div
  className="offer-previews"
>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <WithTheme(r)
        description="description-fake-id-1"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        language="en"
        messageId="fake"
        trackAction={[Function]}
      />
    </li>
    <li
      className="offer-previews__list-item"
      key="fakeid2"
    >
      <WithTheme(r)
        description="description-fake-id-2"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        language="en"
        messageId="fake"
        trackAction={[Function]}
      />
    </li>
    <li
      className="offer-previews__list-item"
      key="fakeid3"
    >
      <WithTheme(r)
        description="description-fake-id-3"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        language="en"
        messageId="fake"
        trackAction={[Function]}
      />
    </li>
    <li
      className="offer-previews__list-item"
      key="fakeid4"
    >
      <WithTheme(r)
        description="description-fake-id-4"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        language="en"
        messageId="fake"
        trackAction={[Function]}
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshot for my activity when null 1`] = `
<div
  className="offer-previews"
>
  <f
    className="offer-previews__subtitle"
    color="black"
    component="h1"
    italic={false}
    type="1"
  >
    There are no standing campaigns to preview
  </f>
  <ul
    className="offer-previews__list"
  />
</div>
`;

exports[`OfferPreviews component testing matches snapshot when empty for offers and programs 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.noOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.otherActiveOffers"
                values={
                  Object {
                    "num": 0,
                  }
                }
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  />
  <EmptyBoxIcon
    className="offer-previews__empty-box"
  />
</div>
`;

exports[`OfferPreviews component testing matches snapshot when for harmony campaign 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.oneActiveOffer"
                values={Object {}}
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <Xu
        ctaLink={Object {}}
        description="description-fake-id-1"
        icon="validity-fake-id-1"
        iconAltText="validity-fake-id-1"
        isDarkMode={false}
        isDismissible={true}
        onDismiss={[Function]}
        title="title-fake-id-1"
        trackAction={[MockFunction]}
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshot when for harmony reward 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.oneActiveOffer"
                values={Object {}}
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <td
        description="description-fake-id-1"
        image="validity-fake-id-1"
        imageAltText="validity-fake-id-1"
        imageDark="validity-fake-id-1"
        isDarkMode={false}
        isDismissible={true}
        onDismiss={[Function]}
        title="title-fake-id-1"
        validity="validity-fake-id-1"
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshot when offer for offers and programs was dismissed 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.noOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.otherActiveOffers"
                values={
                  Object {
                    "num": 0,
                  }
                }
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  />
  <EmptyBoxIcon
    className="offer-previews__empty-box"
  />
</div>
`;

exports[`OfferPreviews component testing matches snapshot when offer has been dismissed 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.noOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.otherActiveOffers"
                values={
                  Object {
                    "num": 0,
                  }
                }
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  />
  <EmptyBoxIcon
    className="offer-previews__empty-box"
  />
</div>
`;

exports[`OfferPreviews component testing matches snapshot when we have have one offer for offers and programs (atlantis campaign preview) 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.oneActiveOffer"
                values={Object {}}
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <sd
        description="description-fake-id-1"
        isDismissible={true}
        messageId="fake"
        onDismiss={[Function]}
        trackAction={[MockFunction]}
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshot when we have have one offer for offers and programs (itrade campaign preview) 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.oneActiveOffer"
                values={Object {}}
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <Tu
        description="description-fake-id-1"
        isDismissible={true}
        messageId="fake"
        onDismiss={[Function]}
        trackAction={[MockFunction]}
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshot when we have have one offer for offers and programs (neo campaign preview) 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.oneActiveOffer"
                values={Object {}}
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <nd
        description="description-fake-id-1"
        isDismissible={true}
        messageId="fake"
        onDismiss={[Function]}
        trackAction={[MockFunction]}
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshot when we have have one offer for offers and programs 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.oneActiveOffer"
                values={Object {}}
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <WithTheme(r)
        description="description-fake-id-1"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        isDismissible={true}
        isNew={false}
        messageId="fake"
        onDismiss={[Function]}
        title="title-fake-id-1"
        trackAction={[MockFunction]}
      />
    </li>
  </ul>
</div>
`;

exports[`OfferPreviews component testing matches snapshshot for offers and programs 1`] = `
<div
  className="offer-previews"
>
  <div
    className="offer-previews__text-container"
  >
    <OffersIcon />
    <f
      bold={false}
      className="offer-previews__heading"
      color="black"
      component="h1"
      italic={false}
    >
      <FormattedMessage
        id="offers.recommendedOffers"
        values={Object {}}
      />
    </f>
    <f
      bold={false}
      className="offer-previews__subheading"
      color="black"
      component="span"
      italic={false}
      numeric={false}
      theme="light"
      type="2"
    >
      <FormattedMessage
        id="offers.activeOffersSubheading"
        values={
          Object {
            "offerText": <f
              bold={true}
              color="black"
              component="span"
              italic={false}
              numeric={false}
              theme="light"
              type="2"
            >
              <FormattedMessage
                id="offers.otherActiveOffers"
                values={
                  Object {
                    "num": 4,
                  }
                }
              />
            </f>,
          }
        }
      />
    </f>
  </div>
  <ul
    className="offer-previews__list"
  >
    <li
      className="offer-previews__list-item"
      key="fakeid1"
    >
      <WithTheme(r)
        description="description-fake-id-1"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        isDismissible={true}
        isNew={false}
        messageId="fake"
        onDismiss={[Function]}
        title="title-fake-id-1"
        trackAction={[MockFunction]}
      />
    </li>
    <li
      className="offer-previews__list-item"
      key="fakeid2"
    >
      <WithTheme(r)
        description="description-fake-id-2"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        isDismissible={true}
        isNew={false}
        messageId="fake"
        onDismiss={[Function]}
        title="title-fake-id-2"
        trackAction={[MockFunction]}
      />
    </li>
    <li
      className="offer-previews__list-item"
      key="fakeid3"
    >
      <WithTheme(r)
        description="description-fake-id-3"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        isDismissible={true}
        isNew={false}
        messageId="fake"
        onDismiss={[Function]}
        title="title-fake-id-3"
        trackAction={[MockFunction]}
      />
    </li>
    <li
      className="offer-previews__list-item"
      key="fakeid4"
    >
      <WithTheme(r)
        description="description-fake-id-4"
        eExperiencePostParams={
          Object {
            "language": "en",
            "messageId": "fake",
            "token": "",
          }
        }
        isDismissible={true}
        isNew={false}
        messageId="fake"
        onDismiss={[Function]}
        title="title-fake-id-4"
        trackAction={[MockFunction]}
      />
    </li>
  </ul>
</div>
`;
