// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PreviewCard component testing ITradePreview matches snapshshot 1`] = `
<f
  lgPadding={36}
  mdPadding={30}
  smPadding={24}
  type="floatLow"
  xsPadding={18}
>
  <div
    className="itrade-preview-card__text"
  >
    <f
      bold={false}
      color="black"
      component="div"
      italic={false}
      numeric={false}
    >
      <ts
        nerfLinks={false}
        renderAsParagraph={true}
        source="description"
      />
    </f>
  </div>
  <WithTheme(r)
    Component="div"
    ariaLabel="test"
    className="itrade-preview-card__link"
    id="fake-id"
    linkUrl="http://mock.com/"
    messageId="fake-message-id"
    onLinkAction={[Function]}
    specificComponentProps={
      Object {
        "className": "itrade-preview-card__link",
      }
    }
  >
    <f
      bold={false}
      className="itrade-preview-card__link-text"
      color="black"
      component="p"
      italic={false}
      numeric={false}
    >
      test
    </f>
    <Component
      className="itrade-preview-card__link-icon"
      color="red"
      size={10}
    />
  </WithTheme(r)>
</f>
`;

exports[`PreviewCard component testing StandingCampaignPreviewCard matches snapshshot 1`] = `
<r
  cardImage={
    Object {
      "file": Object {
        "url": "http://fakeimage.net",
      },
    }
  }
  cardName="super-card"
  description="description"
  id="fake-id"
  intl={
    Object {
      "defaultFormats": Object {},
      "defaultLocale": "en",
      "formatDate": [Function],
      "formatHTMLMessage": [Function],
      "formatMessage": [Function],
      "formatNumber": [Function],
      "formatPlural": [Function],
      "formatRelative": [Function],
      "formatTime": [Function],
      "formats": Object {},
      "formatters": Object {
        "getDateTimeFormat": [Function],
        "getMessageFormat": [Function],
        "getNumberFormat": [Function],
        "getPluralFormat": [Function],
        "getRelativeFormat": [Function],
      },
      "locale": "en",
      "messages": Object {
        "offers.learnMoreAriaDescription": "Learn more button",
      },
      "now": [Function],
      "onError": [Function],
      "textComponent": "span",
      "timeZone": null,
    }
  }
  messageId="fake-message-id"
  theme={
    Object {
      "type": "light",
    }
  }
  title="wowweeee"
/>
`;

exports[`PreviewCard component testing TargetedCampaignPreviewCard matches snapshshot 1`] = `
<r
  ctaLink={
    Object {
      "accessibilityText": "test",
      "linkAction": Object {
        "url": "http://mock.com/",
      },
      "linkText": "test",
    }
  }
  description="description"
  id="fake-id"
  idDismissible={true}
  image="http://fakeimage.net"
  imageAltText="super-card"
  imageDark="http://fakeimage.net"
  intl={
    Object {
      "defaultFormats": Object {},
      "defaultLocale": "en",
      "formatDate": [Function],
      "formatHTMLMessage": [Function],
      "formatMessage": [Function],
      "formatNumber": [Function],
      "formatPlural": [Function],
      "formatRelative": [Function],
      "formatTime": [Function],
      "formats": Object {},
      "formatters": Object {
        "getDateTimeFormat": [Function],
        "getMessageFormat": [Function],
        "getNumberFormat": [Function],
        "getPluralFormat": [Function],
        "getRelativeFormat": [Function],
      },
      "locale": "en",
      "messages": Object {
        "offers.learnMoreAriaDescription": "Learn more button",
      },
      "now": [Function],
      "onError": [Function],
      "textComponent": "span",
      "timeZone": null,
    }
  }
  isDismissible={false}
  isNew={false}
  messageId="fake-message-id"
  onDismiss={[MockFunction]}
  theme={
    Object {
      "type": "light",
    }
  }
  title="wowweeee"
/>
`;
