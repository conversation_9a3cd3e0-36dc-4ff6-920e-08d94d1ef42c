import React from 'react';
import { shallow } from 'enzyme';

import OfferPreviews from 'components/offers/OfferPreviews';
import { pageNames, campaignPreviewTypes } from 'store/content/constants';

const testPreviewProps = {
	name: 'Test',
	token: 'blabla',
	language: 'en',
};

const standingCampaignObject = (uniqueId, type) => ({
	messageId: 'fake',
	title: `title-${uniqueId}`,
	cardName: `cardname-${uniqueId}`,
	cardImage: `cardimage-${uniqueId}`,
	description: `description-${uniqueId}`,
	metadata: {
		ruleName: `ruleName-${uniqueId}`,
		ruleId: `ruleId-${uniqueId}`,
	},
	type: campaignPreviewTypes.standingCampaign,
});

const targetedCampaignObject = (uniqueId, type) => ({
	messageId: 'fake',
	title: `title-${uniqueId}`,
	cardName: `cardname-${uniqueId}`,
	cardImage: `cardimage-${uniqueId}`,
	description: `description-${uniqueId}`,
	type: campaignPreviewTypes.targetedCampaign,
	metadata: {
		ruleName: `ruleName-${uniqueId}`,
		ruleId: `ruleId-${uniqueId}`,
	},
	viewed: true,
	dismissable: true,
	hasBeenDismissed: false,
});

const harmonyObject = (uniqueId, type) => ({
	type,
	title: `title-${uniqueId}`,
	description: `description-${uniqueId}`,
	validity: `validity-${uniqueId}`,
	icon: `validity-${uniqueId}`,
	iconAltText: `validity-${uniqueId}`,
	image: `validity-${uniqueId}`,
	imageDark: `validity-${uniqueId}`,
	imageAltText: `validity-${uniqueId}`,
	ctaLink: {},
	dismissable: true,
	hasBeenDismissed: false,
});

const iTradeCampaignObject = (uniqueId, type) => ({
	messageId: 'fake',
	description: `description-${uniqueId}`,
	type: campaignPreviewTypes.iTradePriorityBoxPreview,
	metadata: {
		ruleName: `ruleName-${uniqueId}`,
		ruleId: `ruleId-${uniqueId}`,
	},
	viewed: true,
	dismissable: true,
	hasBeenDismissed: false,
});

const atlantisCampaignObject = (uniqueId, type) => ({
	messageId: 'fake',
	description: `description-${uniqueId}`,
	type: campaignPreviewTypes.atlantisPriorityPreview,
	metadata: {
		ruleName: `ruleName-${uniqueId}`,
		ruleId: `ruleId-${uniqueId}`,
	},
	viewed: true,
	dismissable: true,
	hasBeenDismissed: false,
});

describe('OfferPreviews component testing', () => {
	it('matches snapshshot for offers and programs', () => {
		const offers = {
			'fakeid1': targetedCampaignObject('fake-id-1'),
			'fakeid2': targetedCampaignObject('fake-id-2'),
			'fakeid3': targetedCampaignObject('fake-id-3'),
			'fakeid4': targetedCampaignObject('fake-id-4'),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when empty for offers and programs', () => {
		const wrapper = shallow(
			<OfferPreviews
				offers={null}
				pageName={pageNames.offersAndPrograms[0]}
				{ ...testPreviewProps }
			/>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when we have have one offer for offers and programs', () => {
		const offers = {
			'fakeid1': targetedCampaignObject('fake-id-1'),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when offer for offers and programs was dismissed', () => {
		const offers = {
			'fakeid1': { ...targetedCampaignObject('fake-id-1'), hasBeenDismissed: true },
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when we have have one offer for offers and programs (itrade campaign preview)', () => {
		const offers = {
			'fakeid1': iTradeCampaignObject('fake-id-1'),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[2]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when we have have one offer for offers and programs (atlantis campaign preview)', () => {
		const offers = {
			'fakeid1': atlantisCampaignObject('fake-id-1'),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[2]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when we have have one offer for offers and programs (neo campaign preview)', () => {
		const offers = {
			'fakeid1': iTradeCampaignObject('fake-id-1'),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[2]}
				onCtaClick={() => jest.fn()}
				application="neo"
				{ ...testPreviewProps }
			/>,
		);
		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot for my activity', () => {
		const offers = {
			'fakeid1': standingCampaignObject('fake-id-1'),
			'fakeid2': standingCampaignObject('fake-id-2'),
			'fakeid3': standingCampaignObject('fake-id-3'),
			'fakeid4': standingCampaignObject('fake-id-4'),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.myActivity[0]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot for an unsupported previewType', () => {
		const offers = {
			'fakeid1': standingCampaignObject('fake-id-1'),
		};

		offers.fakeid1.type = 'blablaCampaignPreview';

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.myActivity[0]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot for my activity when null', () => {
		const wrapper = shallow(
			<OfferPreviews
				offers={null}
				pageName={pageNames.myActivity[0]}
				onCtaClick={() => jest.fn()}
				{ ...testPreviewProps }
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when offer has been dismissed', () => {
		const offers = {
			'fakeid1': targetedCampaignObject('fake-id-1'),
		};

		offers.fakeid1.hasBeenDismissed = true;

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				handleDismiss={jest.fn()}
				onCtaClick={() => jest.fn()}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when for harmony campaign', () => {
		const offers = {
			'fakeid1': harmonyObject('fake-id-1', campaignPreviewTypes.harmonyCampaign),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				handleDismiss={jest.fn()}
				onCtaClick={() => jest.fn()}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot when for harmony reward', () => {
		const offers = {
			'fakeid1': harmonyObject('fake-id-1', campaignPreviewTypes.harmonyReward),
		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				handleDismiss={jest.fn()}
				onCtaClick={() => jest.fn()}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});

	it('matches snapshot - no offers', () => {
		const offers = {

		};

		const wrapper = shallow(
			<OfferPreviews
				offers={offers}
				pageName={pageNames.offersAndPrograms[0]}
				handleDismiss={jest.fn()}
				onCtaClick={() => jest.fn()}
			/>,
		);

		expect(wrapper).toMatchSnapshot();
	});
});
