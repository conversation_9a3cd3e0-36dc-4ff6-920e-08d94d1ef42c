import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { ThemeContext } from 'styled-components';

const iconLight = ({ className }) => (
	<svg className={className} width='176' height='122' viewBox='0 0 176 122' xmlns='http://www.w3.org/2000/svg'>
		<g id='401-Unauthorized-site' fill='none' fillRule='evenodd'>
			<g id='401Unauthorized' transform='translate(-163 -485)'>
				<g id='Unauthorized' transform='translate(163 484)'>
					<g id='Group-6' transform='translate(0 117.548)' stroke='#EB1D26' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.25'>
						<path d='M116.55756,1.15030102 L174.905929,1.15030102' id='Stroke-2' />
						<path d='M25.6338383,1.15030102 L0.624363121,1.15030102' id='Stroke-4'
						/>
					</g>
					<path d='M74.8742695,24.095951 L84.8975319,24.095951' id='Stroke-7' stroke='#E2E8EE' strokeWidth='1.25' strokeLinecap='round' />
					<g id='Group-61' transform='translate(17.475 .823)'>
						<path d='M64.5988652,19.8554633 L54.5756028,19.8554633 C54.5756028,19.8554633 52.4985532,19.6471163 52.4985532,18.2238306 C52.4985532,16.8005449 53.4035177,16.5934531 53.4035177,16.5934531'
						      id='Stroke-8' stroke='#E2E8EE' strokeWidth='1.25' strokeLinecap='round'
						/>
						<path d='M5.1727773,45.9502898 L15.1960397,45.9502898' id='Stroke-10'
						      stroke='#E2E8EE' strokeWidth='1.25' strokeLinecap='round' />
						<path d='M86.8920738,118.297635 C86.8920738,120.187818 71.229322,121.720298 51.908017,121.720298 C32.5879603,121.720298 16.9252085,120.187818 16.9252085,118.297635 C16.9252085,116.406196 32.5879603,114.873716 51.908017,114.873716 C71.229322,114.873716 86.8920738,116.406196 86.8920738,118.297635'
						      id='Fill-12' fill='#D0D7DA' />
						<path d='M12.3720511,42.5318939 L2.34878865,42.5318939 C2.34878865,42.5318939 0.271739007,42.324802 0.271739007,40.9015163 C0.271739007,39.4782306 1.17795177,39.2711388 1.17795177,39.2711388'
						      id='Stroke-14' stroke='#E2E8EE' strokeWidth='1.25' strokeLinecap='round'
						/>
						<polygon id='Fill-16' fill='#FFF' points='39.7398014 117.91646 35.8628085 117.91646 25.1342979 58.9768684 29.4356879 58.9768684'
						/>
						<polygon id='Stroke-18' stroke='#D6D6D6' strokeWidth='1.25' strokeLinecap='round'
						         strokeLinejoin='round' points='39.7398014 117.91646 35.8628085 117.91646 25.1342979 58.9768684 29.4356879 58.9768684'
						/>
						<g id='Group-23' transform='translate(22.468 84.092)'>
							<polyline id='Fill-20' fill='#FFF' points='48.7351489 0.444306122 50.3828085 0.444306122 50.3828085 16.7367857 0.13293617 16.7367857 0.13293617 0.444306122 1.70695035 0.444306122'
							/>
							<polyline id='Stroke-22' stroke='#A0A8AA' strokeWidth='1.25' strokeLinecap='round'
							          strokeLinejoin='round' points='48.7351489 0.444306122 50.3828085 0.444306122 50.3828085 16.7367857 0.13293617 16.7367857 0.13293617 0.444306122 1.70695035 0.444306122'
							/>
						</g>
						<g id='Group-27' transform='translate(42.44 84.092)'>
							<path d='M1.02654184,0.444306122 L28.7633929,0.444306122' id='Fill-24'
							      fill='#FFF' />
							<path d='M1.02654184,0.444306122 L28.7633929,0.444306122' id='Stroke-26'
							      stroke='#DDE5E8' strokeWidth='1.25' strokeLinecap='round' strokeLinejoin='round'
							/>
						</g>
						<g id='Group-31' transform='translate(23.716 84.092)'>
							<path d='M0.458473759,0.444306122 L14.5209986,0.444306122' id='Fill-28'
							      fill='#FFF' />
							<path d='M0.458473759,0.444306122 L14.5209986,0.444306122' id='Stroke-30'
							      stroke='#A0A8AA' strokeWidth='1.25' strokeLinecap='round' strokeLinejoin='round'
							/>
						</g>
						<polygon id='Fill-32' fill='#DDE5E8' points='71.2100993 97.7297755 80.8264397 97.7297755 80.8264397 84.5361429 71.2100993 84.5361429'
						/>
						<polygon id='Fill-34' fill='#FFF' points='25.1218156 78.6898776 77.3725957 78.6898776 77.3725957 62.397398 25.1218156 62.397398'
						/>
						<polygon id='Stroke-35' stroke='#A0A8AA' strokeWidth='1.25' strokeLinecap='round'
						         strokeLinejoin='round' points='25.1218156 78.6898776 77.3725957 78.6898776 77.3725957 62.397398 25.1218156 62.397398'
						/>
						<polygon id='Fill-36' fill='#ED0722' points='67.6012255 61.8278327 77.784261 79.3201898 70.6543887 79.3201898 60.6947858 61.8278327'
						/>
						<polygon id='Fill-37' fill='#ED0722' points='49.7780426 61.8278327 59.9598298 79.3201898 52.8312057 79.3201898 42.8716028 61.8278327'
						/>
						<polygon id='Fill-38' fill='#ED0722' points='31.9542355 61.8278327 42.1372709 79.3201898 35.0073986 79.3201898 25.0477957 61.8278327'
						/>
						<polygon id='Fill-39' fill='#FFF' points='88.3542468 117.91646 84.4785021 117.91646 73.7487433 58.9768684 78.0501333 58.9768684'
						/>
						<polygon id='Stroke-40' stroke='#D6D6D6' strokeWidth='1.25' strokeLinecap='round'
						         strokeLinejoin='round' points='88.3542468 117.91646 84.4785021 117.91646 73.7487433 58.9768684 78.0501333 58.9768684'
						/>
						<path d='M84.4193362,80.687498 C84.4193362,80.687498 85.4566128,92.6574061 74.5695773,87.5767531'
						      id='Stroke-41' stroke='#391572' strokeWidth='1.25' strokeLinecap='round'
						/>
						<g id='Group-44' transform='translate(17.475 57.735)'>
							<polyline id='Fill-42' fill='#FFF' points='6.88734184 7.31912755 7.65125674 1.24192347 11.9513986 1.24192347 4.12127092 60.1815153 0.244278014 60.1815153 5.99111489 14.4531276'
							/>
							<polyline id='Stroke-43' stroke='#A0A8AA' strokeWidth='1.25' strokeLinecap='round'
							          strokeLinejoin='round' points='6.88734184 7.31912755 7.65125674 1.24192347 11.9513986 1.24192347 4.12127092 60.1815153 0.244278014 60.1815153 5.99111489 14.4531276'
							/>
						</g>
						<polygon id='Fill-45' fill='#FFF' points='72.8513929 117.91646 68.9744 117.91646 73.4829957 58.9768684 77.7843858 58.9768684'
						/>
						<polygon id='Stroke-46' stroke='#A0A8AA' strokeWidth='1.25' strokeLinecap='round'
						         strokeLinejoin='round' points='72.8513929 117.91646 68.9744 117.91646 73.4829957 58.9768684 77.7843858 58.9768684'
						/>
						<polygon id='Fill-47' fill='#A0A8AA' points='73.3791433 64.3833459 73.3791433 67.9039071 76.3436823 68.7561214 76.5833418 66.7190908'
						/>
						<polygon id='Fill-48' fill='#F3F4F6' points='26.6364142 85.2408827 24.6954213 100.073679 29.4262014 100.073679 38.2399319 85.2408827'
						/>
						<polygon id='Fill-49' fill='#391572' points='88.5796766 78.3120918 87.7271376 77.1548878 80.6933787 71.3688673 80.2589957 73.8238469 83.8439035 76.5524388 83.8364142 79.3525714 87.0281305 79.9851429'
						/>
						<g id='Group-52' transform='translate(63.66 50.204)'>
							<path d='M9.08759149,3.93625102 L9.02892482,3.97892449 L6.52498156,7.21206735 L3.24214468,5.80886327 C3.24214468,5.80886327 1.68935035,5.08592449 0.879251064,6.04984286 C0.0704,7.01250612 1.14886809,8.45461837 1.14886809,8.45461837 L10.250939,14.2946082 L24.0700596,25.4637612 L26.2856624,28.0141286 L28.6560454,26.8280571 L28.1517617,24.7684347 C28.3914213,24.132098 28.8582582,23.5020367 29.3513078,22.8393429 C29.8156482,22.214302 30.2949674,21.5666694 30.6544567,20.8311796 C33.8886128,14.2230673 30.8479319,6.04231224 23.8790809,2.59454694 C20.9307688,1.13737347 17.4070241,0.846189796 14.1241872,1.6482'
							      id='Fill-50' fill='#FFD400' />
							<path d='M9.08759149,3.93625102 L9.02892482,3.97892449 L6.52498156,7.21206735 L3.24214468,5.80886327 C3.24214468,5.80886327 1.68935035,5.08592449 0.879251064,6.04984286 C0.0704,7.01250612 1.14886809,8.45461837 1.14886809,8.45461837 L10.250939,14.2946082 L24.0700596,25.4637612 L26.2856624,28.0141286 L28.6560454,26.8280571 L28.1517617,24.7684347 C28.3914213,24.132098 28.8582582,23.5020367 29.3513078,22.8393429 C29.8156482,22.214302 30.2949674,21.5666694 30.6544567,20.8311796 C33.8886128,14.2230673 30.8479319,6.04231224 23.8790809,2.59454694 C20.9307688,1.13737347 17.4070241,0.846189796 14.1241872,1.6482'
							      id='Stroke-51' stroke='#8230DF' strokeWidth='1.25' strokeLinecap='round'
							      strokeLinejoin='round' />
						</g>
						<path d='M73.9867801,56.7681398 C73.9867801,56.7681398 77.3557447,53.277701 80.2591206,53.277701 C83.1637447,53.277701 85.6739291,55.3084561 85.6739291,55.3084561 L73.9867801,56.7681398 Z'
						      id='Fill-53' fill='#FFF' />
						<path d='M93.2355631,66.5234204 L84.4193362,69.5959102 C84.4193362,69.5959102 89.1613504,71.6944408 90.0101447,73.7603388 C92.5565277,71.3681143 93.2355631,66.5234204 93.2355631,66.5234204'
						      id='Fill-54' fill='#EDAA14' />
						<g id='Group-57' transform='translate(96.113)'>
							<polyline id='Fill-55' fill='#FFF' points='3.74230922 8.79977143 0.23853617 12.324098 0.23853617 28.2538531 11.4401248 39.5183939 27.2838695 39.5183939 38.4867064 28.2538531 38.4867064 12.324098 27.2838695 1.05830204 11.4401248 1.05830204 8.62412482 3.89106735'
							/>
							<polyline id='Stroke-56' stroke='#DDE5E8' strokeWidth='1.25' strokeLinecap='round'
							          points='3.74230922 8.79977143 0.23853617 12.324098 0.23853617 28.2538531 11.4401248 39.5183939 27.2838695 39.5183939 38.4867064 28.2538531 38.4867064 12.324098 27.2838695 1.05830204 11.4401248 1.05830204 8.62412482 3.89106735'
							/>
						</g>
						<path d='M115.475722,39.6465398 L115.475722,116.619438' id='Stroke-58'
						      stroke='#DDE5E8' strokeWidth='1.875' />
						<polygon id='Fill-59' fill='#F3F4F6' points='121.946031 4.58212653 109.005662 4.58212653 99.8561589 13.7820245 99.8561589 26.7936673 109.005662 35.9935653 121.946031 35.9935653 131.096783 26.7936673 131.096783 13.7820245'
						/>
						<path d='M108.437345,21.1105653 L123.349912,21.1105653' id='Stroke-60'
						      stroke='#FFF' strokeWidth='6.25' />
					</g>
				</g>
			</g>
		</g>
	</svg>
);

const iconDark = ({ className }) => (
	<svg className={className} width="176" height="122" viewBox="0 0 176 122" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path d="M116.933 117.816H175.281" stroke="#972529" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M26.0095 117.816H1" stroke="#972529" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M75.2498 23.2138H85.273" stroke="#525252" strokeWidth="1.25" strokeLinecap="round"/>
		<path d="M82.4491 19.7965H72.427C72.427 19.7965 70.3487 19.5882 70.3487 18.1661C70.3487 16.7429 71.2537 16.5345 71.2537 16.5345" stroke="#525252" strokeWidth="1.25" strokeLinecap="round"/>
		<path d="M23.023	 45.8914H33.0462" stroke="#525252" strokeWidth="1.25" strokeLinecap="round"/>
		<g mask="url(#mask0_665_2657)">
			<path fillRule="evenodd" clipRule="evenodd" d="M104.743 118.239C104.743 120.129 89.08 121.661 69.76 121.661C50.4386 121.661 34.7759 120.129 34.7759 118.239C34.7759 116.349 50.4386 114.815 69.76 114.815C89.08 114.815 104.743 116.349 104.743 118.239Z" fill="#525252"/>
		</g>
		<path d="M30.2222 42.4736H20.2002C20.2002 42.4736 18.1232 42.2652 18.1232 40.8432C18.1232 39.4199 19.0281 39.2116 19.0281 39.2116" stroke="#525252" strokeWidth="1.25" strokeLinecap="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M57.5906 117.858H53.7149L42.9851 58.9179H47.2865L57.5906 117.858Z" fill="black"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M57.5906 117.858H53.7149L42.9851 58.9179H47.2865L57.5906 117.858Z" stroke="#525252" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M89.0534 84.4772H90.7011V100.77H40.4512V84.4772H42.0252" fill="black"/>
		<path d="M89.0534 84.4772H90.7011V100.77H40.4512V84.4772H42.0252" stroke="#808080" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M61.3169 84.4772H89.0538" stroke="#808080" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M42.0255 84.4772H56.088" stroke="#525252" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M89.0607 97.671H98.677V84.4773H89.0607V97.671Z" fill="#525252"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M42.9724 78.6311H95.2231V62.3386H42.9724V78.6311Z" fill="black"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M42.9724 78.6311H95.2231V62.3386H42.9724V78.6311Z" stroke="#808080" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M85.4525 61.7689L95.6343 79.2613H88.5045L78.5461 61.7689H85.4525Z" fill="#56388D"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M67.6289 61.7689L77.8106 79.2613H70.6808L60.7224 61.7689H67.6289Z" fill="#56388D"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M49.805 61.7689L59.9868 79.2613H52.857L42.8986 61.7689H49.805Z" fill="#56388D"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M106.204 117.858H102.329L91.6001 58.9179H95.9002L106.204 117.858Z" fill="black"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M106.204 117.858H102.329L91.6001 58.9179H95.9002L106.204 117.858Z" stroke="#757575" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M102.271 76.6289C102.271 76.6289 103.307 88.5988 92.4198 83.5194" stroke="#543E87" strokeWidth="1.25" strokeLinecap="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M42.2132 64.9949L42.9759 58.9177H47.2773L39.4471 117.857H35.5701L41.3157 72.1289" fill="black"/>
		<path d="M42.2132 64.9949L42.9759 58.9177H47.2773L39.4471 117.857H35.5701L41.3157 72.1289" stroke="#808080" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M90.7015 117.858H86.8257L91.3343 58.9179H95.6345L90.7015 117.858Z" fill="black"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M90.7015 117.858H86.8257L91.3343 58.9179H95.6345L90.7015 117.858Z" stroke="#808080" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M91.2293 64.3257V67.845L94.1951 68.6972L94.4335 66.6614L91.2293 64.3257Z" fill="#808080"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M44.4866 85.1819L42.5456 100.015H47.2764L56.0901 85.1819H44.4866Z" fill="#525252"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M105.695 76.3163L104.842 75.1591L97.8097 69.373L97.3754 71.8293L100.959 74.5566L100.952 77.3568L104.143 77.9906L105.695 76.3163Z" fill="#543E87"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M90.5979 54.0827L90.5392 54.1241L88.0352 57.3572L84.7524 55.954C84.7524 55.954 83.1996 55.2323 82.3908 56.195C81.5794 57.1577 82.6591 58.601 82.6591 58.601L91.7612 64.4398L105.58 75.6089L107.795 78.1593L110.166 76.9732L109.661 74.9136C109.902 74.2772 110.369 73.6472 110.86 72.9845C111.325 72.3595 111.805 71.7118 112.165 70.9763C115.398 64.3682 112.358 56.1875 105.389 52.7397C102.441 51.2825 98.916 50.9913 95.6344 51.7934" fill="#CCA300"/>
		<path d="M90.5979 54.0827L90.5392 54.1241L88.0352 57.3572L84.7524 55.954C84.7524 55.954 83.1996 55.2323 82.3908 56.195C81.5794 57.1577 82.6591 58.601 82.6591 58.601L91.7612 64.4398L105.58 75.6089L107.795 78.1593L110.166 76.9732L109.661 74.9136C109.902 74.2772 110.369 73.6472 110.86 72.9845C111.325 72.3595 111.805 71.7118 112.165 70.9763C115.398 64.3682 112.358 56.1875 105.389 52.7397C102.441 51.2825 98.916 50.9913 95.6344 51.7934" stroke="#56388D" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M91.8376 56.7093C91.8376 56.7093 95.2053 53.2189 98.1099 53.2189C101.015 53.2189 103.523 55.2497 103.523 55.2497L91.8376 56.7093Z" fill="#FEFEFE"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M111.086 66.4645L102.271 69.537C102.271 69.537 107.012 71.6355 107.861 73.7014C110.407 71.3092 111.086 66.4645 111.086 66.4645Z" fill="#947601"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M117.707 8.74147L114.202 12.2645V28.1956L125.406 39.4588H141.247L152.451 28.1956V12.2645L141.247 1H125.406L122.588 3.83151" fill="black"/>
		<path d="M117.707 8.74147L114.202 12.2645V28.1956L125.406 39.4588H141.247L152.451 28.1956V12.2645L141.247 1H125.406L122.588 3.83151" stroke="#525252" strokeWidth="1.25" strokeLinecap="round"/>
		<path d="M133.327 39.5877V116.561" stroke="#525252" strokeWidth="1.875"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M139.797 4.52332H126.856L117.707 13.7245V26.7349L126.856 35.936H139.797L148.946 26.7349V13.7245L139.797 4.52332Z" fill="#525252"/>
		<path d="M126.288 21.0516H141.199" stroke="black" strokeWidth="6.25"/>
	</svg>
);

const UnauthorizedIcon = ({ className }) => {
	const theme = useContext(ThemeContext);
	const icon = theme && theme.type === 'dark' ? iconDark({ className }) : iconLight({ className });
	return icon;
};

UnauthorizedIcon.propTypes = {
	className: PropTypes.string,
};

export default UnauthorizedIcon;
