import React, { useContext }  from 'react';
import PropTypes from 'prop-types';
import { ThemeContext } from 'styled-components';

const iconLight = ({ className }) => (
	<svg className={className} xmlns='http://www.w3.org/2000/svg' width='82' height='107' viewBox='0 0 82 107'>
		<g fill='none' fillRule='evenodd'>
			<path stroke='#C3D3D8' strokeLinecap='round' d='M33.1344 12.6814L33.1344 15.0644M33.1344 19.8269L33.1344 22.2099M37.8981 17.4451L35.5151 17.4451M30.7526 17.4451L28.3696 17.4451M65.1725 22.2088L65.1725 24.2928M65.1725 28.4617L65.1725 30.5457M69.3414 26.3767L67.2574 26.3767M63.0885 26.3767L61.0045 26.3767'
			      transform='translate(0 1)' />
			<g transform='translate(0 1.202)'>
				<path stroke='#C3D3D8' d='M20.4899 2.1054C20.4899 3.1044 19.6789 3.9154 18.6809 3.9154 17.6829 3.9154 16.8739 3.1044 16.8739 2.1054 16.8739 1.1074 17.6829.2974 18.6809.2974 19.6789.2974 20.4899 1.1074 20.4899 2.1054zM46.3756 39.9755C46.3756 40.9735 45.5646 41.7845 44.5666 41.7845 43.5686 41.7845 42.7596 40.9735 42.7596 39.9755 42.7596 38.9765 43.5686 38.1675 44.5666 38.1675 45.5646 38.1675 46.3756 38.9765 46.3756 39.9755z'
				/>
				<path fill='#F3F4F6' d='M78.4879,99.0536 C78.4879,102.3376 67.5849,104.9996 54.1359,104.9996 C40.6869,104.9996 29.7839,102.3376 29.7839,99.0536 C29.7839,95.7706 40.6869,93.1086 54.1359,93.1086 C67.5849,93.1086 78.4879,95.7706 78.4879,99.0536'
				/>
				<polygon fill='#FCFCFC' points='50.92 103.641 8.648 97.635 8.648 58.524 50.92 62.788'
				/>
				<polygon stroke='#C3D3D8' strokeLinecap='round' strokeLinejoin='round'
				         points='50.92 103.641 8.648 97.635 8.648 58.524 50.92 62.788' />
				<polygon fill='#F3F4F6' points='72.236 94.507 50.921 103.642 50.921 62.788 72.236 55.679'
				/>
				<polygon stroke='#C3D3D8' strokeLinecap='round' strokeLinejoin='round'
				         points='72.236 94.507 50.921 103.642 50.921 62.788 72.236 55.679' />
				<polygon fill='#F3F4F6' points='50.92 62.788 8.648 58.522 34.368 51.303 34.255 61.106'
				/>
				<polygon stroke='#C3D3D8' strokeLinecap='round' strokeLinejoin='round'
				         points='50.92 62.788 8.648 58.522 34.368 51.303 34.255 61.106' />
				<polyline stroke='#C3D3D8' strokeLinecap='round' strokeLinejoin='round'
				          points='72.236 55.679 34.369 51.302 25.85 41.573 .5 47.144 8.647 58.524'
				/>
				<polygon fill='#FCFCFC' points='50.92 62.788 61.578 48.731 80.894 44.223 72.236 55.68'
				/>
				<polygon stroke='#C3D3D8' strokeLinecap='round' strokeLinejoin='round'
				         points='50.92 62.788 61.578 48.731 80.894 44.223 72.236 55.68' />
				<polyline stroke='#C3D3D8' points='37.655 61.449 37.655 73.765 44.386 74.703 44.386 62.13'
				/>
			</g>
		</g>
	</svg>
);

const iconDark = ({ className }) => (
	<svg className={className} width="82" height="106" viewBox="0 0 82 106" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path fillRule="evenodd" clipRule="evenodd" d="M9.14795 59.2246L34.868 52.0056L34.755 61.8086L9.14795 59.2246Z" fill="black"/>
		<path d="M63.5885 26.8773H61.5045M33.6344 13.182V15.565V13.182ZM33.6344 20.3275V22.7105V20.3275ZM38.3981 17.9457H36.0151H38.3981ZM31.2526 17.9457H28.8696H31.2526ZM65.6725 22.7094V24.7934V22.7094ZM65.6725 28.9623V31.0463V28.9623ZM69.8414 26.8773H67.7574H69.8414Z" stroke="#949494" strokeLinecap="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M20.99 2.808C20.99 3.807 20.179 4.618 19.181 4.618C18.183 4.618 17.374 3.807 17.374 2.808C17.374 1.81 18.183 1 19.181 1C20.179 1 20.99 1.81 20.99 2.808ZM46.8757 40.6781C46.8757 41.6761 46.0647 42.4871 45.0667 42.4871C44.0687 42.4871 43.2597 41.6761 43.2597 40.6781C43.2597 39.6791 44.0687 38.8701 45.0667 38.8701C46.0647 38.8701 46.8757 39.6791 46.8757 40.6781Z" stroke="#949494"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M78.9879 99.7562C78.9879 103.04 68.0849 105.702 54.6359 105.702C41.1869 105.702 30.2839 103.04 30.2839 99.7562C30.2839 96.4732 41.1869 93.8112 54.6359 93.8112C68.0849 93.8112 78.9879 96.4732 78.9879 99.7562Z" fill="#333333"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M51.4199 104.344L9.14795 98.3376V59.2266L51.4199 63.4906V104.344Z" fill="#333333"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M51.4199 104.344L9.14795 98.3376V59.2266L51.4199 63.4906V104.344Z" stroke="#949494" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M72.7359 95.2096L51.4209 104.345V63.4906L72.7359 56.3816V95.2096Z" fill="black"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M72.7359 95.2096L51.4209 104.345V63.4906L72.7359 56.3816V95.2096Z" stroke="#949494" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M9.147 59.2266L34.869 52.0046L26.35 42.2756L1 47.8466L9.147 59.2266Z" fill="#333333"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M51.4199 63.4906L58 55L34.8679 52.0056L34.7549 61.8086L51.4199 63.4906Z" fill="#333333"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M51.4199 63.4906L9.14795 59.2246L34.868 52.0056L34.755 61.8086L51.4199 63.4906Z" stroke="#949494" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M72.736 56.3816L34.869 52.0046L26.35 42.2756L1 47.8466L9.147 59.2266" stroke="#949494" strokeLinecap="round" strokeLinejoin="round"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M51.4199 63.4906L62.0779 49.4336L81.3939 44.9256L72.7359 56.3826L51.4199 63.4906Z" fill="#333333"/>
		<path fillRule="evenodd" clipRule="evenodd" d="M51.4199 63.4906L62.0779 49.4336L81.3939 44.9256L72.7359 56.3826L51.4199 63.4906Z" stroke="#949494" strokeLinecap="round" strokeLinejoin="round"/>
		<path d="M38.155 62.1516V74.4676L44.886 75.4056V62.8326" stroke="#949494"/>
	</svg>
);

const EmptyBoxIcon = ({ className }) => {
	const theme = useContext(ThemeContext);
	const icon = theme && theme.type === 'dark' ? iconDark({ className }) : iconLight({ className });
	return icon;
};

EmptyBoxIcon.propTypes = {
	className: PropTypes.string,
};

export default EmptyBoxIcon;
