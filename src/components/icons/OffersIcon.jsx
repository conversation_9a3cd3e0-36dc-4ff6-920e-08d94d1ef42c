import React from 'react';
import PropTypes from 'prop-types';

const OffersIcon = ({ className }) => (
	<svg className={className} xmlns='http://www.w3.org/2000/svg' width='43' height='47' viewBox='0 0 43 47'>
		<g fill='none' fillRule='evenodd' strokeLinecap='round' strokeLinejoin='round'
		   strokeWidth='1.5'>
			<path stroke='#7462E0' d='M40.661157,21.5791364 C40.661157,22.4014091 40.1874545,23.5509545 39.6100661,24.1318636 L19.9178678,44.5618636 C19.3425124,45.1448182 18.3930744,45.1448182 17.817719,44.5618636 L2.46203306,29.1166364 C1.88667769,28.5336818 1.88667769,27.5845909 2.46203306,26.9995909 L22.7702479,7.19345455 C23.3476364,6.61254545 24.4902149,6.13595455 25.3075041,6.13595455 L39.1749917,6.13595455 C39.9902479,6.13595455 40.661157,6.80890909 40.661157,7.63118182 L40.661157,21.5791364 Z'
			      transform='translate(1 1)' />
			<path stroke='#660' d='M0,20.9998636 L20.2228264,1.06077273 C20.804281,0.477818182 21.9488926,-0.000818181818 22.7702479,-0.000818181818 L38.6280992,-0.000818181818'
			      transform='translate(1 1)' />
			<path stroke='#7462E0' d='M32.5289256,15.3409091 C32.5289256,15.9054545 32.0735207,16.3636364 31.5123967,16.3636364 C30.9512727,16.3636364 30.4958678,15.9054545 30.4958678,15.3409091 C30.4958678,14.7763636 30.9512727,14.3181818 31.5123967,14.3181818 C32.0735207,14.3181818 32.5289256,14.7763636 32.5289256,15.3409091 Z'
			      transform='translate(1 1)' />
		</g>
	</svg>
);

OffersIcon.propTypes = {
	className: PropTypes.string,
};

export default OffersIcon;
