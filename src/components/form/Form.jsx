import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import { fulfillableTargetEndpoints } from 'store/content/constants';
import { getDeploymentEnvironment } from 'utils/cookieUtils';

const mapStateToProps = (state) => {
	let SAMLToken, campaignType;
	if (state.content && state.content.SAMLToken) {
		SAMLToken = state.content.SAMLToken.token;
		campaignType = state.content.SAMLToken.campaignType;
	}
	return {
		SAMLToken,
		campaignType,
	};
};

@connect(mapStateToProps)
class Form extends React.Component {
	static propTypes = {
		SAMLToken: PropTypes.string,
		campaignType: PropTypes.string,
	};

	componentDidMount() {
		this.checkFormSubmission();
	}

	componentDidUpdate() {
		this.checkFormSubmission();
	}

	checkFormSubmission = () => {
		const { SAMLToken, campaignType } = this.props;
		if (SAMLToken && campaignType) {
			this.form.submit();
		}
	}

	render() {
		const { SAMLToken, campaignType } = this.props;
		if (!SAMLToken || !campaignType) {
			return null;
		}
		const formAction = fulfillableTargetEndpoints[campaignType][getDeploymentEnvironment()];
		const paths = {
			scotiahome: 'verify.bns',
			scrl: 'validate',
		};
		return (
			<form
				action={`${formAction}/${paths[campaignType]}`}
				method="post"
				ref={form => { this.form = form; }}
			>
				<input type="text" hidden readOnly name="SAMLResponse" value={SAMLToken || ''} />
			</form>
		);
	}
}

export default Form;
