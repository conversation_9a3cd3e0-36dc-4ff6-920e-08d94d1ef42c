import { getLanguageFromCookie } from 'utils/cookieUtils';

export const detailsPageLoad = (
	messageId,
	campaignId,
	contentName,
	ruleName,
	guid,
	url,
	env,
) => {
	const language = getLanguageFromCookie();
	const { appEventData } = window;
	if (!appEventData) {
		return false;
	}
	const event = {
		event: 'pigeon.singleOfferPageView',
		page: {
			language,
			pageName: `pigeon: Offer Details:${ruleName}`,
			referringSource: 'nova',
			url,
			section: 'pigeon',
			platform: 'pigeon-web',
			platformType: 'pigeon',
		},
		offers: {
			messageId,
			campaignId,
			contentName,
			ruleNames: ruleName,
		},
		customer: {
			guid,
			status: 'customer',
		},
		site: {
			env,
			country: 'CA',
		},
	};
	appEventData.push(event);
};

export const listPageLoad = (offers, guid, url, env) => {
	const { appEventData } = window;
	if (!appEventData) {
		return false;
	}
	const stringifiedOffers = Object.values(offers || {}).map(({ name }) => `pwo-${name}` || 'unspecified');
	const listValue = stringifiedOffers.length ? stringifiedOffers.join(',') : '';
	const language = getLanguageFromCookie();
	const event = {
		event: 'pigeon.offersPageView',
		page: {
			language,
			pageName: 'pigeon: Pigeon Web Offers',
			referringSource: 'nova',
			url,
			section: 'pigeon',
			platform: 'pigeon-web',
			platformType: 'pigeon',
		},
		offers: {
			ruleNames: listValue,
		},
		customer: {
			guid,
			status: 'customer',
		},
		site: {
			env,
			country: 'CA',
		},
	};
	appEventData.push(event);
};

export const trackAction = (ruleName, campaignId, page, href, eventType, inlineLinkText) => {
	// inlineLinkText is for inline links tracking only
	const { appEventData } = window;
	if (!appEventData) {
		return false;
	}
	const event = {
		event: 'pigeon.offerDetailsPage.click',
		click: {
			name: `pigeon:${page}:${ruleName}`,
			ruleNames: ruleName,
			campaignId: campaignId,
			href: href,
			type: eventType,
		},
	};
	if (inlineLinkText) {
		event.click.inlinetext = inlineLinkText;
	}
	appEventData.push(event);
};

export const trackDismiss = (ruleName) => {
	// inlineLinkText is for inline links tracking only
	const { appEventData } = window;
	if (!appEventData) {
		return false;
	}
	const event = {
		event: 'pigeon.offerClose.click',
		click: {
			name: `pigeon:offers programs:${ruleName}:close`,
		},
	};
	appEventData.push(event);
};

export const trackError = (code, guid, url, env) => {
	const { appEventData } = window;
	if (!appEventData) {
		return false;
	}
	const codeToMessage = {
		'401': 'expired token',
		'408': 'time-out',
	};
	const language = getLanguageFromCookie();
	const event = {
		event: 'pigeon.errorPage',
		page: {
			language,
			pageName: 'pigeon: error',
			errorType: codeToMessage[code] || code,
			referringSource: 'nova',
			url,
			section: 'pigeon',
			platform: 'pigeon-web',
			platformType: 'pigeon',
		},
		customer: {
			guid,
			status: 'customer',
		},
		site: {
			env,
			country: 'CA',
		},
	};
	appEventData.push(event);
};

export const trackInlineLink = clickEvent => (
	ruleName,
	campaignId,
	page,
) => {
	const href = clickEvent.currentTarget.getAttribute('href');
	const text = clickEvent.currentTarget.innerText;
	const type = clickEvent.currentTarget.getAttribute('data-link-type');
	const eventPage = clickEvent.currentTarget.getAttribute('data-link-page');
	trackAction(ruleName, campaignId, eventPage || page, href, type, text);
};
