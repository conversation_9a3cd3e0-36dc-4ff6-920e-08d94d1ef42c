import { listPageLoad, detailsPageLoad, trackError, trackAction, trackInlineLink, trackDismiss } from 'analytics';

describe('analytics testing', () => {
	const resetGlobal = () => {
		global.appEventData = [];
	};

	beforeEach(resetGlobal);
	resetGlobal();

	const offers = {
		0: { id: 'id1', messageId: 'messId1' },
		1: { id: 'id2', messageId: 'messId2' },
		2: { id: 'id3', messageId: 'messId3' },
	};

	it('returns expected values on list page load', () => {
		listPageLoad([ { id: 'rule-id-mock', messageId: 'message-id-mock', name: 'name-mock' } ], 'guid-mock', 'http://test.jest/listPageLoad', 'jest-env');
		const expected = {
			event: 'pigeon.offersPageView',
			page: {
				language: 'en',
				pageName: 'pigeon: Pigeon Web Offers',
				referringSource: 'nova',
				url: 'http://test.jest/listPageLoad',
				section: 'pigeon',
				platform: 'pigeon-web',
				platformType: 'pigeon',
			},
			offers: {
				ruleNames: 'pwo-name-mock',
			},
			customer: {
				guid: 'guid-mock',
				status: 'customer',
			},
			site: {
				env: 'jest-env',
				country: 'CA',
			},
		};
		expect(global.appEventData[0]).toEqual(expected);
	});

	it('returns expected values on details page load', () => {
		detailsPageLoad('message-id-mock', 'rule-id-mock', 'content-name-mock', 'rule-name-mock', 'guid-mock', 'http://test.jest/singleCampaign', 'jest-env');
		const expected = {
			event: 'pigeon.singleOfferPageView',
			page: {
				language: 'en',
				pageName: `pigeon: Offer Details:rule-name-mock`,
				referringSource: 'nova',
				url: 'http://test.jest/singleCampaign',
				section: 'pigeon',
				platform: 'pigeon-web',
				platformType: 'pigeon',
			},
			offers: {
				messageId: 'message-id-mock',
				campaignId: 'rule-id-mock',
				contentName: 'content-name-mock',
				ruleNames: 'rule-name-mock',
			},
			customer: {
				guid: 'guid-mock',
				status: 'customer',
			},
			site: {
				env: 'jest-env',
				country: 'CA',
			},
		};
		expect(global.appEventData[0]).toEqual(expected);
	});

	it('returns expected values on trackError', () => {
		trackError(408, 'guid-mock', 'http://test.jest/error', 'jest-env');
		const expected = {
			event: 'pigeon.errorPage',
			page: {
				language: 'en',
				pageName: 'pigeon: error',
				errorType: 'time-out',
				referringSource: 'nova',
				url: 'http://test.jest/error',
				section: 'pigeon',
				platform: 'pigeon-web',
				platformType: 'pigeon',
			},
			customer: {
				guid: 'guid-mock',
				status: 'customer',
			},
			site: {
				env: 'jest-env',
				country: 'CA',
			},
		};
		expect(global.appEventData[0]).toEqual(expected);
	});

	it('returns expected values on trackAction', () => {
		trackAction('rule-name-mock', 'rule-id-mock', 'Pigeon Web Offers Mock', 'https://www.scotiabank.com', 'web');
		const expected = {
			event: 'pigeon.offerDetailsPage.click',
			click: {
				name: `pigeon:Pigeon Web Offers Mock:rule-name-mock`,
				ruleNames: 'rule-name-mock',
				campaignId: 'rule-id-mock',
				href: 'https://www.scotiabank.com',
				type: 'web',
			},
		};
		expect(global.appEventData[0]).toEqual(expected);
	});

	it('returns expected values on trackInlineLink', () => {
		trackInlineLink({
			currentTarget: {
				getAttribute: (attr) => attr === 'data-link-page' ? 'test-page' : 'test-attribute',
				innerText: 'test-inner-text',
			},
		})('test-rule-name', 'test-campaign-id', 'test-page');

		const expected = {
			click: {
				campaignId: 'test-campaign-id',
				href: 'test-attribute',
				inlinetext: 'test-inner-text',
				name: 'pigeon:test-page:test-rule-name',
				ruleNames: 'test-rule-name',
				type: 'test-attribute',
			},
			event: 'pigeon.offerDetailsPage.click',
		};

		expect(global.appEventData[0]).toEqual(expected);
	});

	it('returns expected values on trackDismiss', () => {
		trackDismiss('test-rule-name');

		const expected = {
			click: {
				name: 'pigeon:offers programs:test-rule-name:close',
			},
			event: 'pigeon.offerClose.click',
		};

		expect(global.appEventData[0]).toEqual(expected);
	});

	it('these analytics functions return false when window.appEventData is not defined', () => {
		delete global.appEventData;

		expect(detailsPageLoad('productCategory', 'messageId', 'campaignId', 'contentName', 'ruleName', 'guid')).toBe(false);
		expect(trackAction()).toBe(false);
		expect(listPageLoad('something', offers, 'guid-id-id')).toBe(false);
		expect(trackError(408, 'guid-mock', 'http://test.jest/error', 'jest-env')).toBe(false);
		expect(trackDismiss('test-rule-name')).toBe(false);
	});
});
