import Enzyme from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';
import dotenv from 'dotenv';
import '@testing-library/jest-dom';

// The newer versions of axios use native web APIs like TextEncoder for handling form data streams
// Polyfill the TextEncoder in the setup for Jest to use
import { TextEncoder } from 'util';
global.TextEncoder = TextEncoder;

dotenv.config();
process.env.PCF_ENV = 'IST';
process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY = 'TEST';
process.env.CDP_SECRET_PASSPORT_PUBLIC_KEY = 'TEST';
process.env.PASSPORT_PUBLIC_KEY = 'LOCAL_TEST';
process.env.CDP_SECRET_CARD_PEPPER = 'SCOTCH BONNET';
process.env.CDP_SECRET_SSO_PUBLIC_KEY = 'TEST';
process.env.CDP_SECRET_SSO_PRIVATE_KEY = 'TEST';
process.env.PASSPORT_SSO_ONYX_CLIENT_ID = 'TEST';
process.env.CDP_SECRET_SSO_ONYX_PUBLIC_KEY = 'TEST';
process.env.CDP_SECRET_SSO_ONYX_PRIVATE_KEY = 'TEST';
process.env.FEDERATION_SESSION_SECRET = 'TEST';
process.env.CLIENT_SESSION_TIMEOUT = 3600000;
process.env.REDIS_TTL = 43100;
process.env.VALID_RETURN_DOMAIN = '.domain';
window.ldcid = 'LD_CLIENT_ID';
global.fetch = require('jest-fetch-mock');
Enzyme.configure({ adapter: new Adapter() });
