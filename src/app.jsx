import React from 'react';
import { IntlProvider, addLocaleData } from 'react-intl';
import { Provider } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { Router } from './containers/routes';
import { getLanguageFromCookie, getCookieProperty } from './utils/cookieUtils';
import { withLDProvider } from 'launchdarkly-react-client-sdk';
import enMessages from 'messages/en';
import frMessages from 'messages/fr';
import esMessages from 'messages/es';
import CanvasThemeProvider from 'canvas-core-react/lib/CanvasThemeProvider';

import en from 'react-intl/locale-data/en';
import fr from 'react-intl/locale-data/fr';
import es from 'react-intl/locale-data/es';

import 'assets/favicon.ico';
import 'app.scss';

import configureStore from 'store/configureStore';
const store = configureStore();

addLocaleData([ ...en, ...fr, ...es ]);

const messages = {
	en: enMessages,
	fr: frMessages,
	es: esMessages,
};

const App = () => {
	const history = useHistory();
	const dmQuery = new URLSearchParams(history.location.search).get('dark-mode');
	const dmCookie = getCookieProperty('dark-mode');
	const theme = [ dmQuery, dmCookie ].some(v => v && v.toLowerCase() === 'true') ? 'dark' : 'light';
	const language = getLanguageFromCookie();

	document.documentElement.setAttribute('data-dark-mode', theme === 'dark');

	return (
		<CanvasThemeProvider theme={{ mode: theme }}>
			<IntlProvider locale={language} messages={messages[language]}>
				<Provider store={store}>
					<Router />
				</Provider>
			</IntlProvider>
		</CanvasThemeProvider>
	);
};

export default withLDProvider({ clientSideID: window.ldcid })(App);
