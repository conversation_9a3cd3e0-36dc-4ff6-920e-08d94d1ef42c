
describe('IGNORED_ROUTES_FOR_LOGGING test', () => {
	beforeEach(() => {
		jest.resetModules(); // Most important - it clears the cache
	});
	it('Should return valid input when valid IGNORED_ROUTES_FOR_LOGGING is passed', () => {
		process.env['IGNORED_ROUTES_FOR_LOGGING'] = '["/health"]';
		const config = require('./config');
		expect(config.logging.ignoredLogRoutes).toEqual([ '/health' ]);
	});

	it('Should return [] when invalid IGNORED_ROUTES_FOR_LOGGING is passed', () => {
		process.env['IGNORED_ROUTES_FOR_LOGGING'] = 'someincorrectthing';
		const config = require('./config');
		expect(config.logging.ignoredLogRoutes).toEqual([]);
	});

	it('Should config content security policy with parsed and default policies', () => {
		process.env.CSP_IMAGE_SRC = '["https://*.ctfassets.net","https://assets.adobedtm.com"]';
		process.env.CSP_FRAME_SRC = '["https://*.demdex.net"]';
		process.env.CSP_SCRIPT_SRC = '[]';
		process.env.CSP_CONNECT_SRC = '[]';
		const config = require('./config');
		const defaults = [ "'self'", 'https://*.scotiabank.com' ];
		const helmet = require('helmet');
		const defaultDirectives = helmet.contentSecurityPolicy.getDefaultDirectives();
		delete defaultDirectives['script-src-attr'];

		expect(config.contentSecurityPolicy).toEqual({
			directives: {
				...defaultDirectives,
				'img-src': [ ...defaults, 'https://*.ctfassets.net', 'https://assets.adobedtm.com' ],
				'frame-src': [ ...defaults, 'https://*.demdex.net' ],
				'script-src': [ ...defaults, "'unsafe-inline'" ],
				'connect-src': [ ...defaults ],
			},
			useDefaults: false,
		});
	});

	it('Should config content security policy with defaults if not provided', () => {
		process.env.CSP_IMAGE_SRC = '';
		process.env.CSP_FRAME_SRC = '';
		process.env.CSP_SCRIPT_SRC = '';
		process.env.CSP_CONNECT_SRC = '';
		const config = require('./config');
		const defaults = [ "'self'", 'https://*.scotiabank.com' ];
		const helmet = require('helmet');
		const defaultDirectives = helmet.contentSecurityPolicy.getDefaultDirectives();
		delete defaultDirectives['script-src-attr'];

		expect(config.contentSecurityPolicy).toEqual({
			directives: {
				...defaultDirectives,
				'img-src': [ ...defaults ],
				'frame-src': [ ...defaults ],
				'script-src': [ ...defaults, "'unsafe-inline'" ],
				'connect-src': [ ...defaults ],
			},
			useDefaults: false,
		});
	});

	it('should throw error when secert CDP_SECRET_PASSPORT_PRIVATE_KEY is missing', () => {
		process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY = '';
		expect(() => require('./config')).toThrow('service to service authentication private key is missing');
		process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY = 'TEST';
	});

	it('should throw not error when secert CDP_SECRET_PASSPORT_PRIVATE_KEY is available', () => {
		expect(() => require('./config')).not.toThrow();
	});

	it('should throw error when secert CDP_SECRET_PASSPORT_PRIVATE_KEY is missing', () => {
		process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY = '';
		expect(() => require('./config')).toThrow('service to service authentication private key is missing');
		process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY = 'TEST';
	});

	it('should throw not error when secert CDP_SECRET_PASSPORT_PRIVATE_KEY is available', () => {
		expect(() => require('./config')).not.toThrow();
	});
});
