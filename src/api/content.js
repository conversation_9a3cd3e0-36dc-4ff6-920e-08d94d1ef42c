import qs from 'qs';
import axios from 'axios';
import { getDeploymentEnvironmentHeader, getCampaignApiUrl } from './common';

const campaignAPI = getCampaignApiUrl();

const getCampaignWithId = async (id, messageId, selectContents, channel) => {
	const url = `${campaignAPI}/${id}${qs.stringify({ message_id: messageId, select_contents: selectContents, channel }, { addQueryPrefix: true })}`;
	try {
		const response = await axios.get(url, {
			headers: { ...getDeploymentEnvironmentHeader() },
		});
		return response.data && response.data.data;
	} catch (error) {}
};

const getSAMLToken = async (id, messageId) => {
	const url = `${campaignAPI}/${id}/token${qs.stringify({ message_id: messageId }, { addQueryPrefix: true })}`;
	try {
		const response = await axios.get(url, {
			headers: { ...getDeploymentEnvironmentHeader() },
		});
		return response.data && response.data.data;
	} catch (error) {}
};

const getOpaqueToken = async (id) => {
	const url = `${campaignAPI}/${id}/token${qs.stringify({ ot: true }, { addQueryPrefix: true })}`;
	try {
		const response = await axios.get(url, {
			headers: { ...getDeploymentEnvironmentHeader() },
		});
		return response.data && response.data.opaqueToken;
	} catch (error) {}
};

export { getCampaignWithId, getSAMLToken, getOpaqueToken };
