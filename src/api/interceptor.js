/* eslint-disable no-undef */
import axios from 'axios';

import { addError } from 'store/errors/errorActions';
import { webTrackIdUrl } from 'api/getWebTrackingId';

axios.defaults.timeout = process.env.API_TIMEOUT_THRESHOLD || 10000;

const interceptor = (store) => {
	const onRequest = (config) => {
		// check if offline
		if (typeof window !== 'undefined' && !window.navigator.onLine) {
			store.dispatch(addError({ errorCode: 'offline', errorMessage: 'DEVICE_OFFLINE' }));
		}
		return config;
	};

	const onResponse = (response) => {
		if (response && response.headers && response.headers.uniqueuser && response.headers.usertrackingid) {
			localStorage.setItem('UserTrackingID', response.headers['usertrackingid']);
			localStorage.setItem('UniqueUser', response.headers['uniqueuser']);
		}
		return response;
	};

	// eslint-disable-next-line handle-callback-err
	const onResponseError = (error) => {
		const errorUrl = error.config.url;
		const errorStatus = (error.response && error.response.status) || 500;

		// do not show error page on web tracking id call fail
		if (errorUrl === webTrackIdUrl) {
			return;
		}

		switch (errorStatus) {
			case 400: // bad request
			case 401: // unauthorized
				store.dispatch(addError({ errorCode: 401, errorMessage: 'UNAUTHORIZED_ACCESS' }));
				break;
			case 500:
			case 503:
			case 504:
				store.dispatch(addError({ errorCode: 503, errorMessage: 'SERVICE_UNAVAILABLE' }));
				break;
		}
	};

	axios.interceptors.request.use(onRequest);
	axios.interceptors.response.use(onResponse, onResponseError);
};

export default interceptor;
