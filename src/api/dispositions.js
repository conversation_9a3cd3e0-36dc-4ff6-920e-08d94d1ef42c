import axios from 'axios';
import { getDeploymentEnvironmentHeader, getCampaignApiUrl } from './common';

const renderedCampaignAPI = getCampaignApiUrl();
const dispositionsAPI = 'dispositions';

const postDisposition = async (ruleId, reqBody) => {
	const url = `${renderedCampaignAPI}/${ruleId}/${dispositionsAPI}`;
	try {
		const response = await axios.post(url, reqBody, {
			headers: { ...getDeploymentEnvironmentHeader() },
		});
		return response.data && response.data.data;
	} catch (error) {}
};

export { postDisposition };
