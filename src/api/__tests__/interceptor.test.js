import axios from 'axios';
import <PERSON>ck<PERSON>dapter from 'axios-mock-adapter';

import interceptor from 'api/interceptor';
import { addError } from 'store/errors/errorActions';

describe('fetch intercept', () => {
	const mock = new MockAdapter(axios);

	test('should intercept response errors', async () => {
		const mockStore = {
			dispatch: jest.fn(),
		};
		interceptor(mockStore);

		const interceptMethods = axios.interceptors;
		expect(typeof interceptMethods).toBe('object');
		expect(interceptMethods).toHaveProperty('response');
		expect(interceptMethods).toHaveProperty('request');

		mock.onGet().replyOnce(400, new Error('fake error'));
		await axios.get('/');

		expect(mockStore.dispatch).toBeCalled();
		expect(mockStore.dispatch.mock.calls[0][0]).toEqual(addError({ errorCode: 401, errorMessage: 'UNAUTHORIZED_ACCESS' }));

		mock.onGet().replyOnce(500, new Error('fake error'));
		await axios.get('/');
		expect(mockStore.dispatch).toBeCalled();
		expect(mockStore.dispatch.mock.calls[1][0]).toEqual(addError({ errorCode: 503, errorMessage: 'SERVICE_UNAVAILABLE' }));
	});

	test('should intercept requests when offline', async () => {
		Object.defineProperty(window.navigator, 'onLine', { value: false, configurable: true });
		const mockStore = {
			dispatch: jest.fn(),
		};
		interceptor(mockStore);
		await axios.get('/');

		expect(mockStore.dispatch).toBeCalled();
		expect(mockStore.dispatch.mock.calls[0][0]).toEqual(addError({ errorCode: 'offline', errorMessage: 'DEVICE_OFFLINE' }));
	});

	test('on request errors', async () => {
		const mockStore = {
			dispatch: jest.fn(),
		};
		interceptor(mockStore);

		mock.onGet().networkErrorOnce();
		await axios.get('/');

		expect(mockStore.dispatch).toBeCalled();
	});
});
