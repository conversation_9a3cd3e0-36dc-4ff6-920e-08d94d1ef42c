import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

import getWebTrackingId, { webTrackIdUrl } from 'api/getWebTrackingId';

describe('getWebTrackingId', () => {
	const mock = new MockAdapter(axios);

	it('makes the call', async () => {
		mock.onGet().replyOnce(200, { data: { data: { web_track_id: 7 } } });
		await getWebTrackingId();
		expect(mock.history.get).toHaveLength(1);
		expect(mock.history.get[0].url).toEqual(webTrackIdUrl);
	});
});
