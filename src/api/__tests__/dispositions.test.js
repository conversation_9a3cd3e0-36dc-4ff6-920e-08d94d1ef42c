import { postDisposition } from 'api/dispositions';

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

describe('disposition - api call', () => {
	const mock = new MockAdapter(axios);
	beforeEach(() => {
		mock.onPost().reply(200, JSON.stringify({ data: {} }));
	});

	test('postDisposition should have data defined', async () => {
		const requestBody = {
			message_id: 'messageId',
			disposition: 'D',
			page: 'accounts',
			container: 'offers-and-programs',
			platform: 'ios',
			application: 'nova',
		};
		await postDisposition('ruleId', requestBody);
		expect(mock.history.post[0].url).toEqual('/v1/campaigns/ruleId/dispositions');
		expect(mock.history.post[0].data).toEqual(JSON.stringify(requestBody));
	});
});
