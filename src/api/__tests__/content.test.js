import { getCampaignWithId, getSAMLToken, getOpaqueToken } from 'api/content';

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

describe('content - api call', () => {
	const mock = new MockAdapter(axios);
	beforeEach(() => {
		mock.onGet().reply(200, JSON.stringify({ data: {} }));
	});

	test('getCampaignWithId should have data defined', async () => {
		await getCampaignWithId('testId', 'messageId', 'selectContents');
		expect(mock.history.get[0].url).toEqual(`/v1/campaigns/testId?message_id=messageId&select_contents=selectContents`);
	});

	test('getSAMLToken should have data defined', async () => {
		await getSAMLToken('testId', 'messageId');
		expect(mock.history.get[1].url).toEqual(`/v1/campaigns/testId/token?message_id=messageId`);
	});

	test('getOpaqueToken should have data defined', async () => {
		await getOpaqueToken('testId');
		expect(mock.history.get[2].url).toEqual(`/v1/campaigns/testId/token?ot=true`);
	});
});
