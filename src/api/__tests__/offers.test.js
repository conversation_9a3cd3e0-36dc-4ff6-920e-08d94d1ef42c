import { getOffers } from 'api/offers';

import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';

describe('getOffers - api call', () => {
	it('should load offer data', async () => {
		const mock = new MockAdapter(axios);
		mock.onGet().replyOnce(200, JSON.stringify({ offers: [] }));
		await getOffers({
			page: 'fakePage',
			container: 'fakeContainer',
			limit: '3',
			platform: 'ios',
			app_version: '1.1.0',
			device_model: 'iPhone 7',
			os_version: '13.2.1',
		});
		expect(mock.history.get).toHaveLength(1);
		expect(mock.history.get[0].url).toEqual(`/v1/campaigns?page=fakePage&container=fakeContainer&limit=3&platform=ios&app_version=1.1.0&device_model=iPhone%207&os_version=13.2.1`);
	});
});
