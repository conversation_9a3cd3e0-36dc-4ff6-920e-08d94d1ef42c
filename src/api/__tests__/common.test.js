import { getDeploymentEnvironmentHeader, getCampaignApiUrl } from 'api/common';

describe('API Common Functions', () => {
	test('getDeploymentEnvironmentHeader - no environment set', async () => {
		const res = getDeploymentEnvironmentHeader();
		expect(res).toEqual(null);
	});

	test('getDeploymentEnvironmentHeader - environment set on cookie', async () => {
		document.cookie = 'x-environment=istred';
		const res = getDeploymentEnvironmentHeader();
		expect(res).toEqual(null);
	});

	test('getDeploymentEnvironmentHeader should return canadian url ', () => {
		const url = getCampaignApiUrl();
		expect(url).toBe('/v1/campaigns');
	});

	test('getDeploymentEnvironmentHeader should return ccau url', () => {
		const location = new URL('http://localhost:8080/ccau/campaigns?page=accounts&platform=ios&country=DO&language=en');
		location.assign = jest.fn();
		location.replace = jest.fn();
		location.reload = jest.fn();

		delete window.location;
		window.location = location;
		const url = getCampaignApiUrl();
		expect(url).toBe('/ccau/v1/campaigns');
	});
});
