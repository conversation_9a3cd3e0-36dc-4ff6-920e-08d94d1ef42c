import qs from 'qs';
import axios from 'axios';
import { getDeploymentEnvironmentHeader, getCampaignApiUrl } from './common';

const campaignURI = getCampaignApiUrl();

const getOffers = async (query) => {
	const url = campaignURI + qs.stringify(query, { addQueryPrefix: true });
	try {
		const response = await axios.get(url, {
			headers: { ...getDeploymentEnvironmentHeader() },
		});
		return response.data && response.data.data;
	} catch (error) {
		console.error('API call to fetch offers has failed');
	}
};

export { getOffers };
