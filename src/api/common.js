import { getXEnvironmentFromCookie } from 'utils/cookieUtils';

export const getDeploymentEnvironmentHeader = () => {
	const deployEnvironment = getXEnvironmentFromCookie();
	if (!deployEnvironment) {
		return null;
	}

	return { 'x-environment': deployEnvironment };
};

export const getCampaignApiUrl = () => {
	const currentUrl = new URL(window.location.href);
	const ccauCampaignsType = currentUrl.pathname.split('/')[1] === 'ccau' ? '/ccau' : '';
	return `${ccauCampaignsType}/v1/campaigns`;
};
