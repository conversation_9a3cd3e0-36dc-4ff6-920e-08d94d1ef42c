const { HttpError } = require('../errors');
const { xssMiddleware } = require('./xssMiddleware');

const mockNext = jest.fn();
const mockReq = {};
const mockRes = {};

describe('xss middleware', () => {
	beforeEach(() => {
		mockNext.mockClear();
	});
	test('test good request ', () => {
		xssMiddleware(mockReq, mockRes, mockNext);
		expect(mockNext.mock.calls.length).toEqual(1);
	});

	test('test bad request ', () => {
		expect(() => xssMiddleware({ ...mockReq, cookies: { 'cookieName': "<script>alert('xss')</script>" } }, mockRes, mockNext)).toThrow(HttpError.badRequest('Bad request'));
	});
});
