
const defaultCookieOpts = (req = {}) => {
	const secureProtocol = !!req.get && req.get('x-forwarded-proto') === 'https';
	return {
		httpOnly: true,
		secure: req.secure || secureProtocol,
		maxAge: 24 * 3600 * 1000,
	};
};

/**
 * Sets request headers from cookies
 * @param {Express.Request} req Request
 * @param {Express.Response} res Response
 * @param {Function} next Next
 */
const setHeadersFromCookies = launchDarklyService => async (req, res, next) => {
	const setHeaderFromCookie = (key, request) => {
		const keyLC = key.toLowerCase();
		if (request.cookies[keyLC] && !request.get(keyLC)) {
			request.headers[keyLC] = request.cookies[keyLC];
		}
	};
	const pcfEnvrionment = req.cookies['deploy-environment'];
	const cookies = [
		'authorization',
		'x-language',
		'x-mock-insight',
		'x-channel-id',
		'x-application',
		'Embedded',
		'x-feature-flag-uid',
	];
	if (pcfEnvrionment && pcfEnvrionment !== 'prd') {
		cookies.push('preferred-environment');
	}
	cookies.forEach((key) => setHeaderFromCookie(key, req));
	next();
};

/**
 * Set cookies from request headers
 * @param {object} config Config
 * @property {string} config.pcfEnv PCF environment value
 * @returns ((req, res, next) => void) Middleware
 */
const setCookiesMiddleware = ({ pcfEnv, logger }) => async (req, res, next) => {
	const getDeviceFlags = value => {
		let flagsArray;
		try {
			flagsArray = JSON.parse(value); // this line will support ["sec=true","other=false"] format
		} catch (err) {
			// check if the value is in an array format and :- starts with `[` and ends with `]`
			if (value[0] === '[' && value.substr(-1) === ']') {
				flagsArray = value.substring(1).slice(0, -1).split(','); // this line will support [sec=true,other=false] format
			} else {
				return logger.warn({ message: `can't parse ${value} to array` });
			}
		}
		flagsArray.forEach(flag => {
			const [ key, val ] = flag.trim().split('=');
			const keyLC = key.trim().toLowerCase();
			res.cookie(keyLC, val.trim(), defaultCookieOpts(req));
		});
	};
	const setCookieFromHeader = key => {
		const keyLC = key.toLowerCase();
		const value = req.get(keyLC);
		if (value) {
			// Temporary fix to handle old Nova clients which send BCCY instead of N1/N2
			if (keyLC === 'x-application' && value.toLowerCase() === 'bccy') {
				if (req.query && req.query.platform === 'ios') {
					res.cookie(keyLC, 'N1', defaultCookieOpts(req));
				} else if (req.query && req.query.platform === 'android') {
					res.cookie(keyLC, 'N2', defaultCookieOpts(req));
				}
			} else {
				res.cookie(keyLC, value, defaultCookieOpts(req));
			}
		}
	};

	const pcfEnvrionment = pcfEnv.toLowerCase();
	res.cookie('deploy-environment', pcfEnvrionment, defaultCookieOpts(req));
	const headers = [
		'authorization',
		'dark-mode',
		'x-language',
		'x-mock-insight',
		'x-environment',
		'x-channel-id',
		'x-application',
		'Embedded',
		'x-feature-flag-uid',
	];
	if (pcfEnvrionment && pcfEnvrionment !== 'prd') {
		headers.push('preferred-environment');
	}
	if (req.get('x-device-flags')) {
		getDeviceFlags(req.get('x-device-flags'));
	}
	headers.forEach(key => setCookieFromHeader(key));
	next();
};

/**
 * Set query params from cookies
 * @param {Express.Request} req Request
 * @param {Express.Response} res Response
 * @param {Function} next Next
 */

const setQueryParamsFromCookies = (req, res, next) => {
	const mapParams = { 'sec': 'secure_device' };
	const setQueryParamsFormCookie = (key, request) => {
		const keyLC = key.toLowerCase();
		const paramsKey = mapParams[keyLC];
		if (request.cookies[keyLC]) {
			request.query[paramsKey] = request.cookies[keyLC];
		}
	};
	const cookies = [
		'sec',
	];
	cookies.forEach((key) => setQueryParamsFormCookie(key, req));
	next();
};

const getTemplateCookies = (req) => {
	const isCCAU = req.originalUrl.split('/')[1] === 'ccau';
	return {
		'x-channel-id': req.cookies['x-channel-id'],
		'x-application': req.cookies['x-application'],
		'preferred-environment': req.cookies['preferred-environment'],
		'x-language': isCCAU ? req.res.locals?.passport?.customer?.locale?.split('_')[0] : req.cookies['x-language'],
		'dark-mode': req.cookies['dark-mode'],
		'deploy-environment': req.cookies['deploy-environment'],
	};
};

module.exports = {
	defaultCookieOpts,
	setHeadersFromCookies,
	setCookiesMiddleware,
	setQueryParamsFromCookies,
	getTemplateCookies,
};
