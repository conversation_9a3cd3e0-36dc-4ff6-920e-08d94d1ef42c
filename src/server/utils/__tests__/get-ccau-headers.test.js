import { getCCAUHeaders } from '../get-ccau-headers';

describe('getCCAUHeaders', () => {
	it('should return object with country code, and user context to be base64 encoded', () => {
		const request = {
			id: '00000',
			get: name => (name === 'true-client-ip:') ? '123' : undefined,
			query: { channel: 'WAVE' },
		};
		const response = {
			locals: {
				passport: {
					customer: {
						locale: 'en_DO',
						countryCode: '',
						cid: '12345',
						customerKey: 'key',
						login: 'dev.breeze2',
						transitId: '70755',
						firstName: 'dev',
						lastName: 'breeze',
						email: '<EMAIL>',
						cardType: 'U',
						dob: '1980-01-01',
						leapClientId: '7258',
						localCurrency: 'DOP',
						cardNumber: '00000',
					},
				},
			},
		};
		expect(getCCAUHeaders(request, response)).toMatchObject({
			'x-channel-id': 'WAVE',
			'x-country-code': 'DO',
			'x-language': 'en',
			'x-user-context': '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
		});
	});
});
