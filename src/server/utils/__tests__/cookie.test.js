import {
	defaultCookieOpts,
	setHeadersFromCookies,
	setCookiesMiddleware,
	setQueryParamsFromCookies,
} from 'server/utils/cookie';

const launchDarklyService = {
	isFeatureEnabled: () => Promise.resolve(true),
};

const mockLogger = {
	warn: jest.fn(),
};

describe('server cookie util', () => {
	it('defaultCookieOpts', async () => {
		// happy path
		const expectedSecure = { secure: true, maxAge: 86400000, httpOnly: true };
		const happyPath = defaultCookieOpts({ secure: true });
		expect(happyPath).toEqual(expectedSecure);
		const happyPath2 = defaultCookieOpts({
			get: (k) => (k === 'x-forwarded-proto' ? 'https' : undefined),
		});
		expect(happyPath2).toEqual(expectedSecure);

		// missing input
		const expectedInsecure = { secure: false, maxAge: 86400000, httpOnly: true };
		const missingReq = defaultCookieOpts(undefined);
		expect(missingReq).toEqual(expectedInsecure);
		const notSecure = defaultCookieOpts({ secure: false });
		expect(notSecure).toEqual(expectedInsecure);
		const invalidForwardProto = defaultCookieOpts({
			get: (k) => (k === 'x-forwarded-proto' ? 'x' : undefined),
		});
		expect(invalidForwardProto).toEqual(expectedInsecure);
	});

	it('setHeadersFromCookies', async () => {
		const req = {
			cookies: {
				'preferred-environment': 'istred',
				'deploy-environment': 'ist',
				'x-channel-id': 'Mobile',
				'x-application': 'N1',
				authorization: 'test-token',
				'x-feature-flag-uid': '58823D5D-67A7-44E7-860E-2D7D7AA3D313',
			},
			headers: {},
			get: () => false,
		};
		const next = jest.fn();

		await setHeadersFromCookies(launchDarklyService)(req, null, next);
		expect(req.headers['preferred-environment']).toEqual('istred');
		expect(req.headers['x-channel-id']).toEqual('Mobile');
		expect(req.headers['x-application']).toEqual('N1');
		expect(req.headers['x-feature-flag-uid']).toEqual('58823D5D-67A7-44E7-860E-2D7D7AA3D313');
		expect(next).toHaveBeenCalled();

		// oauth off
		await setHeadersFromCookies({
			isFeatureEnabled: () => Promise.resolve(false),
		})(req, null, next);
		expect(req.headers['preferred-environment']).toEqual('istred');
		expect(req.headers['x-channel-id']).toEqual('Mobile');
		expect(req.headers['x-application']).toEqual('N1');
		expect(req.headers['authorization']).toEqual('test-token');
		expect(req.headers['x-feature-flag-uid']).toEqual('58823D5D-67A7-44E7-860E-2D7D7AA3D313');
		expect(next).toHaveBeenCalled();

		// no pref env header in prod
		req.cookies['deploy-environment'] = 'prd';
		req.headers = {};
		await setHeadersFromCookies(launchDarklyService)(req, null, next);
		expect(req.headers['preferred-environment']).toEqual(undefined);
		expect(next).toHaveBeenCalled();
	});

	it('setQueryParamsFromCookies', async () => {
		const req = {
			cookies: {
				sec: 'true',
			},
			query: {},
		};
		const next = jest.fn();
		setQueryParamsFromCookies(req, null, next);
		expect(req.query['secure_device']).toEqual('true');
		expect(next).toHaveBeenCalled();
	});

	describe('setCookiesMiddleware', () => {
		const pcfEnv = 'IST';
		const req = {
			get: (param) => {
				switch (param) {
					case 'preferred-environment':
						return 'istred';
					case 'authorization':
						return 'test-token';
					case 'x-application':
						return 'bccy';
					case 'x-device-flags':
						return '["sec=true","other=false"]';
					default:
						return null;
				}
			},
			query: {
				platform: 'ios',
			},
			secure: true,
		};
		const res = {
			cookie: jest.fn(),
		};
		const next = jest.fn();
		const defaultCookieOpts = { secure: true, maxAge: 24 * 3600 * 1000, httpOnly: true };

		beforeEach(() => {
			next.mockReset();
			res.cookie.mockReset();
		});

		it('defaults - lower environments with preferred-environment header', async () => {
			await setCookiesMiddleware({ pcfEnv })(req, res, next);
			expect(res.cookie).toHaveBeenCalledWith('preferred-environment', 'istred', defaultCookieOpts);
			expect(res.cookie).toHaveBeenCalledWith('x-application', 'N1', defaultCookieOpts);
			expect(res.cookie).toHaveBeenCalledWith('sec', 'true', defaultCookieOpts);
			expect(res.cookie).toHaveBeenCalledWith('other', 'false', defaultCookieOpts);
			expect(next).toHaveBeenCalledTimes(1);
		});

		it('prd environment', async () => {
			await setCookiesMiddleware({ pcfEnv: 'prd' })(req, res, next);
			expect(res.cookie).toHaveBeenCalledWith('deploy-environment', 'prd', defaultCookieOpts);
			expect(next).toHaveBeenCalledTimes(1);
		});

		it('android platform query param', async () => {
			const scopedReq = { ...req };
			scopedReq.query = { platform: 'android' };
			await setCookiesMiddleware({ pcfEnv })(scopedReq, res, next);
			expect(res.cookie).toHaveBeenCalledWith('x-application', 'N2', defaultCookieOpts);
			expect(next).toHaveBeenCalledTimes(1);
		});

		it('unknown platform query param', async () => {
			const scopedReq = { ...req };
			scopedReq.query = { platform: 'unknown' };
			await setCookiesMiddleware({ pcfEnv })(scopedReq, res, next);
			expect(res.cookie).not.toHaveBeenCalledWith('x-application', 'N1', defaultCookieOpts);
			expect(res.cookie).not.toHaveBeenCalledWith('x-application', 'N2', defaultCookieOpts);
		});

		it('x-device-flags [sec=true,other=false] - no quotes, no spaces', async () => {
			const scopedReq = {
				...req,
				get: (param) => {
					switch (param) {
						case 'x-device-flags':
							return '[sec=true,other=false]';
						default:
							return null;
					}
				},
			};
			await setCookiesMiddleware({ pcfEnv })(scopedReq, res, next);
			expect(res.cookie).toHaveBeenCalledWith('sec', 'true', defaultCookieOpts);
			expect(res.cookie).toHaveBeenCalledWith('other', 'false', defaultCookieOpts);
		});

		it('x-device-flags [sec = true, other = false] - with spaces ', async () => {
			const scopedReq = {
				...req,
				get: (param) => {
					switch (param) {
						case 'x-device-flags':
							return '[sec = true ,  other = false ]';
						default:
							return null;
					}
				},
			};
			await setCookiesMiddleware({ pcfEnv })(scopedReq, res, next);
			expect(res.cookie).toHaveBeenCalledWith('sec', 'true', defaultCookieOpts);
			expect(res.cookie).toHaveBeenCalledWith('other', 'false', defaultCookieOpts);
		});

		it('x-device-flags sec=true,other=false - invalid format should log warning message', async () => {
			const scopedReq = {
				...req,
				get: (param) => {
					switch (param) {
						case 'x-device-flags':
							return 'sec = true ,  other = false';
						default:
							return null;
					}
				},
			};
			await setCookiesMiddleware({ pcfEnv, logger: mockLogger })(scopedReq, res, next);
			expect(mockLogger.warn).toHaveBeenCalledWith({
				message: `can't parse sec = true ,  other = false to array`,
			});
		});
	});
});
