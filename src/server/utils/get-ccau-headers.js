const getCCAUHeaders = (req, res) => {
	const customer = res.locals?.passport?.customer;
	const headers = {
		'x-country-code': customer?.locale?.substring(3),
		'x-user-context': Buffer.from(
			JSON.stringify({
				countryCode: customer?.countryCode,
				cid: customer?.cid,
				customerId: customer?.customerKey,
				locale: customer?.locale,
				login: customer?.login,
				transitId: customer?.transitId,
				remoteAddress: req.get['true-client-ip'] || req.ip,
				firstName: customer?.firstName,
				lastName: customer?.lastName,
				email: customer?.email,
				cardType: customer?.cardType,
				dob: customer?.dob,
				leapClientId: customer?.leapClientId,
				localCurrency: customer?.localCurrency,
				...(customer?.cardNumber && {
					cardNumber: customer?.cardNumber,
				}),
			}),
		).toString('base64'),
		'x-language': customer?.locale?.split('_')[0],
		'x-channel-id': req.query?.channel,
	};
	return headers;
};

module.exports = {
	getCCAUHeaders,
};
