const createSSOMiddleware = require('../sso');

const opts = {
	clientID: '57de5eb9-f40f-4ad3-ac21-dd99a11234ce',
	redirectURI: 'https://pigeon-web-ist.apps.stg.azr-cc-pcf.cloud.bns/auth/authorization',
	authorizeURI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/authorize',
	tokenURI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/token',
	tokenInfoURI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/tokeninfo',
	privateKey: process.env.CDP_SECRET_SSO_PRIVATE_KEY,
	logger: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
	heimdall: jest.fn(),
};
const next = jest.fn();

describe('SSO middleware', () => {
	beforeEach(() => {
		next.mockReset();
		opts.logger.info.mockReset();
		opts.logger.error.mockReset();
		opts.logger.warn.mockReset();
		opts.heimdall.mockReset();
	});

	test('Authenticating via heimdall', async () => {
		const req = { method: 'GET', csrfToken: jest.fn(), get: () => 'uatgreen' };
		const res = { locals: { state: { } } };

		opts.heimdall.mockReturnValueOnce(() => {});
		await createSSOMiddleware(opts)(req, res, next);

		expect(opts.logger.info).toHaveBeenCalledWith({ message: 'Request to SSO enabled page' });
		expect(opts.heimdall).toHaveBeenCalledTimes(1);
		expect(opts.logger.warn).not.toHaveBeenCalled();
		expect(opts.logger.error).not.toHaveBeenCalled();
		expect(next).not.toHaveBeenCalled();
	});

	test('Authentication error', async () => {
		const req = { method: 'GET', csrfToken: jest.fn(), get: () => 'uatgreen' };
		const res = { locals: { state: { } } };

		opts.heimdall.mockImplementation(() => new Error('err'));
		await createSSOMiddleware(opts)(req, res, next);

		expect(opts.logger.info).toHaveBeenCalledWith({ message: 'Request to SSO enabled page' });
		expect(opts.heimdall).toHaveBeenCalledTimes(1);
		expect(opts.logger.warn).not.toHaveBeenCalled();
		expect(opts.logger.error).toHaveBeenCalledTimes(1);
		expect(next).toHaveBeenCalledTimes(1);
	});
});
