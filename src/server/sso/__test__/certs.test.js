const createCertHandler = require('../certs');
const httpMocks = require('node-mocks-http');

const ssoClientId = '57de5eb9-f40f-4ad3-ac21-dd99a11234ce';
const ssoPublicKey = process.env.CDP_SECRET_SSO_PUBLIC_KEY;
const pem2jwk = jest.fn();
const result = {
	keys: [
		{
			kid: process.env.CDP_SECRET_SSO_PUBLIC_KEY,
			kty: 'RSA',
			n: 'z_RhYvPyDQU4Nu4SvSkDEJZhhuEdMjeAyULV5NWKDPlJhyV1uX1O6oBfybw42UW7gGIatM-5xco08GFg5tj7Ysq2sil_LvmBgR3I5Dka85Esi_J2JtYZftNKq2WZ9E-TbYihjij8oCS67-OsTrVpjZbFA1yHl-fPZLWNk5UXJUnHkDsliLbqDRpQ3316mdZmBiAsHUiZ2AWYFZMaV2jVxZu-ZqAkxhlWjhY-55E83oyzjBwwvcD447CnO1vZ0DJ48xxyAK6bWkQjpNXBc8E4lG3D7p1HBTzjE9CLwZusPmtR4uhuhbYjAod9p_e_zgXQkxHu5wAXdwsX7bP-w1XHkQ',
			e: 'AQAB',
		},
	],
};

describe('Cert Handler', () => {
	test('Should convert pem to jwk', async () => {
		pem2jwk.mockReturnValue(result.keys[0]);
		const req = { method: 'GET' };
		const res = httpMocks.createResponse();

		await createCertHandler({ ssoClientId, ssoPublicKey, pem2jwk })(req, res);

		expect(res.statusCode).toEqual(200);
		expect(res._getJSONData()).toEqual(result);
	});

	test('Should not throw error if missing public key', async () => {
		pem2jwk.mockReturnValue(result.keys[0]);
		const req = { method: 'GET' };
		const res = httpMocks.createResponse();

		await createCertHandler({ ssoClientId, pem2jwk })(req, res);

		expect(res.statusCode).toEqual(200);
		expect(res._getJSONData()).toEqual(result);
	});
});
