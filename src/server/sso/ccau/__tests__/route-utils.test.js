const routeUtils = require('../route-utils');
const HttpError = require('../../../errors/http-error');

describe('route-utils', () => {
	const res = {};
	const next = jest.fn();

	afterEach(() => {
		next.mockClear();
	});
	describe('internalRoute', () => {
		routeUtils.INTERNAL_ROUTES.forEach(route => {
			test(`Should return true when internal route (${route})`, () => {
				expect(routeUtils.internalRoute(route)).toBe(true);
			});
		});

		test('Should return false when not internal route', () => {
			expect(routeUtils.internalRoute('/not-internal')).toBe(false);
		});
	});

	describe('unauthenticatedRoute', () => {
		routeUtils.UNAUTHENTICATED_ROUTES.forEach(route => {
			test(`Should return true when unauthenticated route (${route})`, () => {
				expect(routeUtils.unauthenticatedRoute(route)).toBe(true);
			});
		});

		test('Should return false when authenticated route', () => {
			expect(routeUtils.unauthenticatedRoute('/authenticated')).toBe(false);
		});
	});

	describe('filterInternalRoutes', () => {
		const federationClientURI = ' https://fed-client-test.bns';
		const logger = {
			warn: jest.fn(),
		};
		let filterInternalRoutesMiddleware;

		beforeEach(() => {
			filterInternalRoutesMiddleware = routeUtils.filterInternalRoutes({ federationClientURI, logger });
		});

		test(`Should call next when not internal route`, () => {
			const request = {
				path: '/not-internal',
				headers: {},
			};
			filterInternalRoutesMiddleware(request, res, next);
			expect(next).toHaveBeenCalledTimes(1);
		});

		test('Should call next when call originated from federation client', () => {
			const request = {
				hostname: federationClientURI,
				headers: {},
			};
			filterInternalRoutesMiddleware(request, res, next);
			expect(next).toHaveBeenCalledTimes(1);
		});

		test('Should call next when call originated from federation client', () => {
			const request = {
				headers: {
					':authority': federationClientURI,
				},
			};
			filterInternalRoutesMiddleware(request, res, next);
			expect(next).toHaveBeenCalledTimes(1);
		});

		test('Should return 404 and call logger when illegal request', () => {
			const request = {
				path: routeUtils.INTERNAL_ROUTES[0],
				headers: {},
			};
			filterInternalRoutesMiddleware(request, res, next);
			expect(next).toBeCalledWith(HttpError.notFound('Resource does not exist.'));
			expect(logger.warn).toHaveBeenCalledTimes(1);
		});
	});

	describe('routeExcludes', () => {
		const mockMiddleware = jest.fn();
		test(`Should call next when excluded`, () => {
			const middleware = routeUtils.routeExcludes(() => true, mockMiddleware);
			middleware({}, res, next);
			expect(mockMiddleware).toHaveBeenCalledTimes(0);
			expect(next).toHaveBeenCalledTimes(1);
		});

		test('Should call middleware when excluded', () => {
			const middleware = routeUtils.routeExcludes(() => false, mockMiddleware);
			middleware({}, res, next);
			expect(mockMiddleware).toHaveBeenCalledTimes(1);
			expect(next).toHaveBeenCalledTimes(0);
		});
	});
});
