const requestSetAuthParams = require('../request-set-auth-params');

describe('request-set-auth-params', () => {
	const res = {};
	const next = jest.fn();
	const middleware = requestSetAuthParams();

	afterEach(() => {
		next.mockClear();
	});

	test('language is set if req has lang and country query param', () => {
		const req = {
			query: {
				language: 'en',
				country: 'DO',
			},
		};
		middleware(req, res, next);
		expect(req.language).toBe('en-DO');
	});

	test('language is not set if query params', () => {
		const req = {
			query: {},
		};
		middleware(req, res, next);
		expect(req.language).toBeUndefined();
	});
});
