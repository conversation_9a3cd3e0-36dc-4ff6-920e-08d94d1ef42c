const providerHeimdall = require('nrlw-express-heimdall/dist/fed-initializer');

const config = require('../../../../config');
const { createSSOMiddleware } = require('../index');

describe('CCAU SSO', () => {
	describe('createSSOMiddleware', () => {
		const logger = jest.fn();
		const redisClient = jest.fn();

		const middlewareConfig = {
			redisClient,
			sessionSecret: config.onyxSso.sessionSecret,
			sessionDomain: config.onyxSso.sessionDomain,
			clientID: config.onyxSso.clientID,
			redirectURI: config.onyxSso.redirectURI,
			authorizeURI: config.onyxSso.authorizeURI,
			tokenURI: config.onyxSso.tokenURI,
			privateKey: config.onyxSso.privateKey,
			revokeURI: config.onyxSso.revokeURI,
			jwksURI: config.onyxSso.jwksURI,
			sessionTTL: config.onyxSso.sessionTTL,
			tokenInfoURI: config.onyxSso.tokenInfoURI,
			logger,
		};

		test('Should configure sso middleware with heimdal expected heimdal configuration', () => {
			createSSOMiddleware(middlewareConfig);
			expect(providerHeimdall.default).toHaveBeenCalledWith({
				redisClient,
				redisOptions: {
					ttl: 600,
				},
				sessionSecret: middlewareConfig.sessionSecret,
				sessionDomain: middlewareConfig.sessionDomain,
				csurfCookie: {
					httpOnly: true,
					secure: true,
					domain: middlewareConfig.sessionDomain,
					key: '_csrf',
				},
				clientID: middlewareConfig.clientID,
				redirectURI: middlewareConfig.redirectURI,
				authorizeURI: middlewareConfig.authorizeURI,
				tokenURI: middlewareConfig.tokenURI,
				privateKey: middlewareConfig.privateKey,
				revokeURI: middlewareConfig.revokeURI,
				jwksURI: middlewareConfig.jwksURI,
				logger,
				sessionTTL: middlewareConfig.sessionTTL,
				tokenInfoURI: middlewareConfig.tokenInfoURI,
			});
		});
	});
});
