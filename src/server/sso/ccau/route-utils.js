const HttpError = require('../../errors/http-error');
const API = {
	HEALTH: '/health',
	HEALTH_SELF: '/health/self',
	SESSION: '/session',
	SESSION_REFRESH: '/session/refresh',
};

const INTERNAL_ROUTES = [ API.SESSION, API.SESSION_REFRESH ];

const UNAUTHENTICATED_ROUTES = [ API.HEALTH, API.HEALTH_SELF ];

const internalRoute = (path) => INTERNAL_ROUTES.some(internalRoute => path?.endsWith(internalRoute));
const unauthenticatedRoute = (path) => UNAUTHENTICATED_ROUTES.some(unauthenticatedRoute => path?.endsWith(unauthenticatedRoute));

/**
 * Ensure requests to the internal heimdal endpoints are originating from federation client app.
 *
 * @param federationClientUri
 * @param logger
 * @returns {(function(*, *, *): void)|*}
 */
const filterInternalRoutes = ({ federationClientURI, logger }) => (req, res, next) => {
	const isInternalRoute = internalRoute(req.path);
	const isRequestFromFederationClient = federationClientURI?.includes(req.hostname) || federationClientURI.includes(req.headers[':authority']);
	if (!isInternalRoute || isRequestFromFederationClient) {
		next();
	} else {
		next(HttpError.notFound('Resource does not exist.'));
		logger.warn({ message: 'Illegal attempt to access internal routes from external link' });
	}
};

const routeExcludes = (excluded, middleware) => (req, res, next) => excluded(req) ? next() : middleware(req, res, next);

module.exports = {
	INTERNAL_ROUTES,
	UNAUTHENTICATED_ROUTES,
	internalRoute,
	unauthenticatedRoute,
	filterInternalRoutes,
	routeExcludes,
};
