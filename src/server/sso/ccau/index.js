const heimdallInitializer = require('nrlw-express-heimdall/dist/fed-initializer').default;

const { filterInternalRoutes, unauthenticatedRoute, routeExcludes } = require('./route-utils');
const requestSetAuthParams = require('./request-set-auth-params');
const express = require('express');

const federationInitializer = ({
	redisClient,
	sessionSecret,
	sessionDomain,
	clientID,
	redirectURI,
	authorizeURI,
	tokenURI,
	revokeURI,
	jwksURI,
	tokenInfoURI,
	sessionTTL,
	privateKey,
	logger,
}) => {
	return routeExcludes(
		(req) => unauthenticatedRoute(req.path),
		heimdallInitializer({
			redisClient,
			redisOptions: {
				ttl: 600,
			},
			sessionSecret,
			sessionDomain,
			csurfCookie: {
				httpOnly: true,
				secure: true,
				domain: sessionDomain,
				key: '_csrf',
			},
			clientID,
			redirectURI,
			authorizeURI,
			tokenURI,
			revokeURI,
			jwksURI,
			tokenInfoURI,
			sessionTTL,
			privateKey,
			logger,
		}));
};

const createSSOMiddleware = ({
	redisClient,
	sessionSecret,
	sessionDomain,
	clientID,
	redirectURI,
	authorizeURI,
	tokenURI,
	revokeURI,
	jwksURI,
	tokenInfoURI,
	sessionTTL,
	privateKey,
	logger,
}) => {
	const heimdal = federationInitializer({
		redisClient,
		sessionSecret,
		sessionDomain,
		clientID,
		redirectURI,
		authorizeURI,
		tokenURI,
		revokeURI,
		jwksURI,
		sessionTTL,
		tokenInfoURI,
		privateKey,
		logger,
	});
	return express.Router().use([
		filterInternalRoutes({ federationClientURI: redirectURI, logger }),
		requestSetAuthParams(),
		heimdal ]);
};

module.exports = {
	createSSOMiddleware,
};
