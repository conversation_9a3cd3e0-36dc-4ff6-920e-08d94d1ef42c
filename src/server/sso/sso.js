const HttpError = require('../errors/http-error');

const createSSOMiddleware = ({ clientID, redirectURI, authorizeURI, tokenURI, tokenInfoURI, jwksURI, ssoPrivateKey, validReturnDomain, logger, heimdall }) => {
	return async (req, res, next) => {
		const csurfCookie = { httpOnly: true, secure: true, domain: validReturnDomain };
		logger.info({ message: 'Request to SSO enabled page' });

		try {
			// Heim<PERSON><PERSON> will write the session to req.session since 'session' is the default used by express-session
			heimdall({
				clientID,
				redirectURI,
				authorizeURI,
				tokenURI,
				tokenInfoURI,
				jwksURI,
				privateKey: ssoPrivateKey,
				csurfCookie,
				logger,
			})(req, res, next);
		} catch (err) {
			logger.error({ message: `SSO error: ' ${err.message}` });
			next(HttpError.unauthorized('SSO error'));
		}
	};
};

module.exports = createSSOMiddleware;
