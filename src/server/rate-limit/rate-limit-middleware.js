const { omit } = require('lodash');
const { isIP, inRange } = require('range_check');

/**
 * Middleware to add LD rate limiting config & true client IP to the req object
 * 	True client IP set to Akamai's true-client-ip header if it exists otherwise falls back to first IP in x-forwarded-for header
 * 	If the resultant IP is invalid or the IP of an CDP load balancer/instance, client rate limiting will be disabled
 * @param {*} launchDarklyService
 * @param {*} rateLimitConfigs - Manifest file rate limit configs
 * @returns
 */
const rateLimitMiddleware = (launchDarklyService, logger, rateLimitConfigs, checkTrueClientIp = false) => async (req, res, next) => {
	try {
		req.ldRateLimitConfig = await launchDarklyService.isFeatureEnabled('pigeon-web.config.rateLimit', omit(rateLimitConfigs, [ 'cdpTrustedIp' ]));
	} catch (err) {
		req.ldRateLimitConfig = omit(rateLimitConfigs, [ 'cdpTrustedIp' ]);
		logger.error({ message: 'Unable to call Launch Darkly for rate limiting middleware', err });
	}

	if (!checkTrueClientIp) {
		// if route doesn't implement client rate limiting, do not need to run logic to determine client ip
		return next();
	}

	const akamaiTrueClientIP = req.header('true-client-ip');
	const xForwardedFor = req.header('x-forwarded-for');
	let trueClientIp = '';
	if (akamaiTrueClientIP && isIP(akamaiTrueClientIP) && !inRange(akamaiTrueClientIP, rateLimitConfigs.cdpTrustedIp)) {
		trueClientIp = akamaiTrueClientIP;
	} else if (xForwardedFor) {
		try {
			const firstIP = xForwardedFor.split(',').map(ip => ip.trim())[0];
			if (firstIP && isIP(firstIP) && !inRange(firstIP, rateLimitConfigs.cdpTrustedIp)) {
				trueClientIp = firstIP;
			}
		} catch (err) {
			logger.error({ err, message: `Error while parsing client ip from ${xForwardedFor}` });
		}
	}
	if (!trueClientIp) {
		logger.warn({ message: `Unable to determine client ip from ${akamaiTrueClientIP} or ${xForwardedFor}` });
	}
	req.trueClientIp = trueClientIp;

	next();
};

module.exports = rateLimitMiddleware;
