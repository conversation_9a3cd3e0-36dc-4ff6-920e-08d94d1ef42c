const rateLimit = require('express-rate-limit');

/**
 * Rate limiter middlewares:
    * Limit the total number of requests allowed & the number of requests allowed by the client ip
    * Defined per route. Note: Client limiter for rendered-campaigns omitted since the client ip resolved to a limited number of ABM serivce IP's
 * Rate limit params:
    * windowMs - Time frame for which requests are checked/remembered. Set as the variable pulled from LD on service start (does not take a function),
    *   would require a service restart if the value is changed in LD.
    * max - The maximum number of connections to allow during the window before rate limiting the client. Pulled from LD on every request.
 * @param {*} rateLimitConfigs - rate limit configs defined in manifest files
 * @param {*} logger - rate limit configs defined in manifest files
 * @returns rate limiter middleware
 */

const campaignsTotalRateLimiter = (rateLimitConfigs, logger) => rateLimit({
	windowMs: rateLimitConfigs.window,
	max: (req, res) => req.ldRateLimitConfig.overall.campaigns,
	standardHeaders: true,
	legacyHeaders: false,
	requestPropertyName: 'campaignsTotalRateLimit',
	handler: (req, res, next, options) => {
		logger.warn({ message: `Total /campaigns rate limit hit for instance ${req.ip}` });
		return res.status(options.statusCode).send(options.message);
	},
});

const campaignsClientRateLimiter = (rateLimitConfigs, logger) => rateLimit({
	windowMs: rateLimitConfigs.window,
	max: (req, res) => {
		if (req.trueClientIp) {
			return req.ldRateLimitConfig.client.campaigns;
		}
		return 0; // if unable to determine client ip, client rate limiting is disabled
	},
	standardHeaders: true,
	legacyHeaders: false,
	requestPropertyName: 'campaignsClientRateLimit',
	keyGenerator: (req, res) => req.trueClientIp,
	handler: (req, res, next, options) => {
		logger.warn({ message: `Client /campaigns rate limit hit for ip ${req.trueClientIp}` });
		return res.status(options.statusCode).send(options.message);
	},
});

const renderedCampaignsTotalRateLimiter = (rateLimitConfigs, logger) => rateLimit({
	windowMs: rateLimitConfigs.window,
	max: (req, res) => req.ldRateLimitConfig.overall['rendered-campaigns'],
	standardHeaders: true,
	legacyHeaders: false,
	requestPropertyName: 'renderedCampaignsTotalRateLimit',
	handler: (req, res, next, options) => {
		logger.warn({ message: `Total /rendered-campaigns rate limit hit for instance ${req.ip}` });
		return res.status(options.statusCode).send(options.message);
	},
});

module.exports = {
	campaignsTotalRateLimiter,
	campaignsClientRateLimiter,
	renderedCampaignsTotalRateLimiter,
};
