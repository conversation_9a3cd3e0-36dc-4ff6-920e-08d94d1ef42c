const rateLimitMiddleware = require('./rate-limit-middleware');

describe('Rate Limit Middleware', () => {
	const next = jest.fn();
	const json = jest.fn();
	const status = jest.fn().mockReturnValue({ json });
	const logger = {
		error: jest.fn(),
		warn: jest.fn(),
	};
	const res = { status, json };

	const rateLimitConfigs = {
		client: { campaigns: 5 },
		overall: { campaigns: 50, 'rendered-campaigns': 40 },
		cdpTrustedIp: '***********/23',
	};

	beforeEach(() => {
		status.mockClear();
		json.mockClear();
		next.mockClear();
	});

	test('should set the client ip as the Akamai true-client-ip header', async () => {
		const req = {
			header: () => '**********',
		};
		const launchDarklyService = {
			isFeatureEnabled: () => {
				return {
					client: { campaigns: 10 },
					overall: { campaigns: 100, 'rendered-campaigns': 80 },
				};
			},
		};

		await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs, true)(req, res, next);
		expect(req.trueClientIp).toEqual('**********');
		expect(req.ldRateLimitConfig.client).toEqual({ campaigns: 10 });
		expect(req.ldRateLimitConfig.overall).toEqual({ campaigns: 100, 'rendered-campaigns': 80 });
		expect(next).toHaveBeenCalled();
	});

	test('should set the client ip as the x-forwarded-for header is Akamai header does not exist', async () => {
		const header = jest.fn();
		header.mockImplementationOnce(() => null); // true-client-ip
		header.mockImplementationOnce(() => '**********'); // x-forwarded-for
		const req = {
			header,
		};
		const launchDarklyService = {
			isFeatureEnabled: () => {
				return {
					client: { campaigns: 10 },
					overall: { campaigns: 100, 'rendered-campaigns': 80 },
				};
			},
		};

		await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs, true)(req, res, next);
		expect(req.trueClientIp).toEqual('**********');
		expect(next).toHaveBeenCalled();
	});

	test('should log error if both ip headers are null', async () => {
		const header = jest.fn();
		header.mockImplementationOnce(() => null); // true-client-ip
		header.mockImplementationOnce(() => null); // x-forwarded-for
		const req = {
			header,
		};
		const launchDarklyService = {
			isFeatureEnabled: () => {
				return {
					client: { campaigns: 10 },
					overall: { campaigns: 100, 'rendered-campaigns': 80 },
				};
			},
		};

		await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs, true)(req, res, next);
		expect(logger.warn).toHaveBeenCalledWith({ message: 'Unable to determine client ip from null or null' });
		expect(next).toHaveBeenCalled();
	});

	test('should use manifest file configs if call to launch darkly fails', async () => {
		const req = {
			header: () => '**********',
		};
		const launchDarklyService = {
			isFeatureEnabled: () => { throw new Error('timeout'); },
		};

		await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs, true)(req, res, next);
		expect(req.trueClientIp).toEqual('**********');
		expect(req.ldRateLimitConfig.client).toEqual({ campaigns: 5 });
		expect(req.ldRateLimitConfig.overall).toEqual({ campaigns: 50, 'rendered-campaigns': 40 });
		expect(next).toHaveBeenCalled();
	});

	test('should skip client ip parsing if implemented on rendered-campaigns route', async () => {
		const req = {
			header: () => '**********',
		};
		const launchDarklyService = {
			isFeatureEnabled: () => {
				return {
					client: { campaigns: 10 },
					overall: { campaigns: 100, 'rendered-campaigns': 80 },
				};
			},
		};

		await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs)(req, res, next);
		expect(req).not.toHaveProperty('trueClientIp');
		expect(next).toHaveBeenCalled();
	});
});
