const { campaignsTotalRateLimiter } = require('./rate-limiters');

describe('campaignsTotalRateLimiter', () => {
	let rateLimitConfigs, logger, middleware;

	beforeEach(() => {
		rateLimitConfigs = {
			window: 60000, // 1 minute window for testing
		};
		logger = {
			warn: jest.fn(),
		};
		middleware = campaignsTotalRateLimiter(rateLimitConfigs, logger);
	});

	it('should return a rate limiter middleware function', () => {
		expect(typeof middleware).toBe('function');
	});
});
