const { Router } = require('express');
const getRenderedCampaigns = require('./get-rendered-campaigns');
const getRenderedCampaign = require('./get-rendered-campaign');
const setRenderedCampaignDisposition = require('./set-rendered-campaign-disposition');

const scopes = {
	campaignsRead: 'cdb.pigeon.campaigns.read',
	dispositionsWrite: 'cdb.pigeon.campaigns.dispositions.write',
	temp: {
		campaignsRead: 'ca:baas:campaign-rules:read',
	},
};

const createRoutes = ({
	authenticate,
	authorize,
	fetch,
	logger,
	launchDarklyService,
	pigeonApi,
	rateLimitMiddleware,
	renderedCampaignsTotalRateLimiter,
}) => {
	const router = new Router();
	router.use(authenticate);
	router.use(rateLimitMiddleware, renderedCampaignsTotalRateLimiter);
	router.get(
		'/',
		authorize([ scopes.campaignsRead, scopes.temp.campaignsRead ]),
		getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi }));
	router.get(
		'/:ruleId',
		authorize([ scopes.campaignsRead, scopes.temp.campaignsRead ]),
		getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi }));
	router.post(
		'/:ruleId/dispositions',
		authorize([ scopes.dispositionsWrite, scopes.temp.campaignsRead ]),
		setRenderedCampaignDisposition({ logger, launchDarklyService, pigeonApi }));
	return router;
};

module.exports = {
	createRoutes,
};
