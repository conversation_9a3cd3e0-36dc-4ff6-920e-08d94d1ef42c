const crypto = require('crypto');
const Joi = require('@hapi/joi');
const { dispositionPathSchema, dispositionBodySchema } = require('../../common/validation');
const { pickReqHeaders, pickTruthy } = require('../../common');
const HttpError = require('../errors/http-error');

const msgValidationError = 'Validation Error';

const setRenderedCampaignDisposition = ({
	logger,
	launchDarklyService,
	pigeonApi,
}) => async (req, res, next) => {
	try {
		let { error, value: opts } = Joi.validate(req.params, dispositionPathSchema, {
			stripUnknown: true,
		});
		if (error) {
			next(
				HttpError.badRequest(
					msgValidationError,
					error.details.map((err) => ({
						path: err.path.join('.'),
						message: err.message,
					})),
				),
			);
			return;
		}

		const isEnabledByDefault = false;
		try {
			const isEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.rendered-campaigns', isEnabledByDefault);
			if (!isEnabled) {
				next(HttpError.notImplemented('Not implemented'));
				return;
			}
		} catch (err) {
			logger.error({ message: `Unable to call Launch Darkly for rendered-campaigns feature flag: ${err.message}` });
			if (!isEnabledByDefault) {
				next(HttpError.notImplemented('Not implemented'));
				return;
			}
		}

		({ error, value: opts } = Joi.validate(
			{ rule_id: opts.ruleId, ...req.body },
			dispositionBodySchema,
			{ stripUnknown: true },
		));

		if (error) {
			next(
				HttpError.badRequest(
					msgValidationError,
					error.details.map((err) => ({
						path: err.path.join('.'),
						message: err.message,
					})),
				),
			);
			return;
		}
		const passthroughHeaders = pickReqHeaders(req, [ // passthrough headers
			'Preferred-Environment',
			'x-language',
			'x-mock-insight',
			'x-channel-id',
			'x-application',
		]);
		const headers = pickTruthy({
			'x-customer-scotiacard': req.get('x-customer-scotiacard'),
			'x-originating-appl-code': req.get('x-originating-appl-code') || 'BFB6',
			'x-b3-spanid': req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex'),
			'x-b3-traceid': req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex'),
			...passthroughHeaders,
		});
		const result = await pigeonApi.setDisposition(headers, opts);
		res.status(200).json(result);
	} catch (err) {
		next(err);
	}
};

module.exports = setRenderedCampaignDisposition;
