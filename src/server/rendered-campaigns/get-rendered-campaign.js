const crypto = require('crypto');
const Joi = require('@hapi/joi');
const { renderedCampaignSchema } = require('../../common/validation');
const { pickTruthy } = require('../../common');
const { getSSRContent } = require('../ssr/ssrHelpers');
const HttpError = require('../errors/http-error');
const config = require('../../config');
const msgValidationError = 'Validation Error';

const getRenderedCampaign = ({ fetch, logger, launchDarklyService, pigeonApi }) => async (req, res, next) => {
	try {
		const { query, params: { ruleId } } = req;
		const { value, error } = Joi.validate({
			ruleId,
			...query,
			'x-customer-scotiacard': req.get('x-customer-scotiacard'),
			'x-channel-id': req.get('x-channel-id'),
			'x-application': req.get('x-application'),
			'Preferred-Environment': req.get('Preferred-Environment'),
			'x-language': req.get('x-language'),
			'x-mock-insight': req.get('x-mock-insight'),
			'x-b3-spanid': req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex'),
			'x-b3-traceid': req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex'),
			'x-originating-appl-code': req.get('x-originating-appl-code') || 'BFB6',
		},
		renderedCampaignSchema,
		{ stripUnknown: true });

		if (error) {
			next(
				HttpError.badRequest(
					msgValidationError,
					error.details.map((err) => ({
						path: err.path.join('.'),
						message: err.message,
					})),
				),
			);
			return;
		}

		const isEnabledByDefault = false;
		try {
			const isEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.rendered-campaigns', isEnabledByDefault);
			if (!isEnabled) {
				next(HttpError.notImplemented('Not implemented'));
				return;
			}
		} catch (err) {
			logger.error({ message: `Unable to call Launch Darkly for rendered-campaigns feature flag: ${err.message}` });
			if (!isEnabledByDefault) {
				next(HttpError.notImplemented('Not implemented'));
				return;
			}
		}

		const {
			ruleId: validRuleId,
			'x-customer-scotiacard': scotiaCard,
			'x-channel-id': channelId,
			'x-application': xApplication,
			'Preferred-Environment': preferredEnv,
			'x-language': xlanguage,
			'x-mock-insight': xMockInsight,
			'x-b3-traceid': traceId,
			'x-b3-spanid': spanId,
			'x-originating-appl-code': xOriginatingApplCode,
			...validQueries
		} = value;

		const queryString = `?${new URLSearchParams(validQueries).toString()}`;
		const headers = pickTruthy({
			'Preferred-Environment': preferredEnv,
			'x-language': xlanguage,
			'x-channel-id': channelId,
			'x-application': xApplication,
			'x-customer-scotiacard': scotiaCard,
			'x-mock-insight': xMockInsight,
			'x-b3-traceid': traceId,
			'x-b3-spanid': spanId,
			'x-originating-appl-code': xOriginatingApplCode,
		});

		const result = await pigeonApi.getCampaign(headers, validRuleId, queryString);

		if (result.data.content) {
			result.data.content = { ...result.data.content, language: xlanguage || 'en', type: result.data.type };
			result.data.content = await getSSRContent({ fetch, logger }, result.data.content);
		}

		if (req.get('debug-abm') && [ 'ist', 'uat' ].includes(config.env)) {
			// add a stylesheet if this is 'production' since one isn't bundled in the content
			const productionStylesheet = config.nodeEnv === 'production'
				? `<link rel="stylesheet" href="/static/app.${config.buildHash}.css">`
				: '';
			res.status(200).send(`
				<html>
					<head>
						${productionStylesheet}
						<title>ABM Debug</title>
					</head>
					<body>${result.data.content}</body>
				</html>`,
			);
		} else {
			return res.status(200).json(result);
		}
	} catch (err) {
		next(err);
	}
};

module.exports = getRenderedCampaign;
