jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
process.env.CDP_SECRET_CARD_PEPPER = 'NWM0N2Q5ZWRmNjg0ZThmNTVmYjAxNzliNDI2ZWJlY2Q5YTY4NzY2NjIyYTA1ZGQ0MGNiMWZmMDFlYTBiOTIyYQ==';
process.env.HTTPS_PROXY = 'http://webproxy.bns:8080';
const renderedCampaigns = require('../index');
const pigeonURL = 'https://pigeon.apps.bns';

const authorize = () => (req, res, next) => next();
const authenticate = (req, res, next) => next();
const getServiceToken = jest.fn().mockResolvedValue('sample.service.token');
const logger = {
	info: jest.fn(),
	error: jest.fn(),
};
const rateLimitMiddleware = jest.fn();
const renderedCampaignsTotalRateLimiter = jest.fn();

describe('Rendered campaigns', () => {
	test('should have `createRoutes`', () => {
		expect(renderedCampaigns.createRoutes).toBeInstanceOf(Function);
	});
	test('should successfully call a route', () => {
		const router = renderedCampaigns.createRoutes({
			authenticate,
			authorize,
			getServiceToken,
			fetch,
			logger,
			pigeonURL,
			rateLimitMiddleware,
			renderedCampaignsTotalRateLimiter,
		});
		expect(router).toBeDefined();
	});
});
