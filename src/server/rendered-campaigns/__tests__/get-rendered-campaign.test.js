jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const getRenderedCampaign = require('../get-rendered-campaign');
const PigeonApi = require('../../service-clients/pigeon-api');

const pigeonURL = 'https://pigeon.apps.bns';
const pigeonAtlasURL = 'https://pigeon.apps.atlas.bns';
const serviceToken = 'sample.service.token';
const preferredEnv = 'istgreen';
const language = 'en';
const channelId = 'ABM';
const cardNumber = '****************';
const campaign1 = {
	id: 'Vp4YWkCvFaw5',
	name: 'pigeon-qa-activities',
	type: 'targetedCampaignPreview',
	container: 'offers-and-programs-it',
	pages: [
		'accounts-it',
	],
	urgent: true,
	viewed: true,
	external_ref: {
		campaign_id: 'MESSAGE',
		message_id: 'Vp4YWkCvFaw5',
		message_source: 'DMS',
	},
	start_date: '2020-08-06T08:00:00Z',
	content: {
		name: 'QA - Bruce - Preview: Scotia Home',
		language: 'en',
	},
};

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const launchDarklyService = {
	isFeatureEnabled: jest.fn().mockImplementation(flagName => {
		if (flagName === 'pigeon-web.downstreams.pigeon-api-atlas') {
			return Promise.resolve(false);
		}
		return Promise.resolve(true);
	}),
};

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const pigeonApi = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

describe('Get rendered campaign', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});
	test('should return a campaign if all required queries, params, and headers are set', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm&platform=web&insight=false`, { data: campaign1 });
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.id).toEqual(campaign1.id);
	});
	test('should fail validation if query contains illegal query values', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: '#abm',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).toBeCalled();
		expect(fetch).not.toBeCalled();
	});
	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm&platform=web&insight=false`, { throws: new Error('timeout') });
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');
	});
	test('should return rendered campaign html if the `abm-debug` flag is present', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'debug-abm': 'y',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm&platform=web&insight=false`, { data: campaign1 });
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getData();
		expect(response).toContain('<html>');
		expect(response).toContain('<title>ABM Debug</title>');
		expect(response).toContain('<body>');
		expect(response).toContain(campaign1.content.name);
	});
	test('should return 501 not implemented if endpoint is disabled in launch darkly', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockResolvedValue(false),
		};
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Not implemented');
	});
	test('should return 501 not implemented if call to launch darkly fails', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockRejectedValue(new Error('Unable to connect to Launch Darkly')),
		};
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Not implemented');
	});

	test('should throw an error pigeon call has failed with internal server error', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm&platform=web&insight=false`, { throws: new Error('Internal Server Error') });
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Internal Server Error');
	});

	test('should throw an Invalid json error if response received is html', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm&platform=web&insight=false`, '<div>Some random div</div>');
		const f = getRenderedCampaign({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toContain('invalid json response body');
	});
});
