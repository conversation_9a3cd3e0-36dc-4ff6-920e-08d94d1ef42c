jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const getRenderedCampaigns = require('../get-rendered-campaigns');
const PigeonApi = require('../../service-clients/pigeon-api');

const pigeonURL = 'https://pigeon.apps.bns';
const pigeonAtlasURL = 'https://pigeon.apps.atlas.bns';
const serviceToken = 'sample.service.token';
const preferredEnv = 'istgreen';
const language = 'en';
const channelId = 'ABM';
const cardNumber = '****************';
const campaign1 = {
	id: 'Vp4YWkCvFaw5',
	name: 'pigeon-qa-activities',
	type: 'targetedCampaignPreview',
	container: 'offers-and-programs-it',
	pages: [
		'accounts-it',
	],
	urgent: true,
	viewed: true,
	external_ref: {
		campaign_id: 'MESSAGE',
		message_id: 'Vp4YWkCvFaw5',
		message_source: 'DMS',
	},
	start_date: '2020-08-06T08:00:00Z',
	content: 'QA - Bruce - Preview: Scotia Home',
};
const campaigns = {
	data: {
		total: 1,
		limit: 1,
		items: [ campaign1 ],
	},
};

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const launchDarklyService = {
	isFeatureEnabled: jest.fn().mockImplementation(flagName => {
		if (flagName === 'pigeon-web.downstreams.pigeon-api-atlas') {
			return Promise.resolve(false);
		}
		return Promise.resolve(true);
	}),
};

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const pigeonApi = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

describe('Get rendered campaigns', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});
	test('should return a list of campaigns', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns?application=abm&platform=web&insight=false`, campaigns);
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.items[0].id).toEqual(campaign1.id);
	});
	test('should fail validation', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			query: {
				application: '#abm',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).toHaveBeenCalled();
	});
	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns?application=abm&platform=web&insight=false`, { throws: new Error('timeout') });
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetch).toBeCalledTimes(1);
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');
	});
	test('should return a rendered item preview if `debug-abm` flag is present', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'debug-abm': '0',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns?application=abm&platform=web&insight=false`, campaigns);
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getData();
		expect(response).toContain('<html>');
		expect(response).toContain('<title>ABM Debug</title>');
		expect(response).toContain('<body>');
		expect(response).toContain(campaign1.content);
	});

	test('should return a message indicating when `debug-abm` item is out of bounds', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'debug-abm': '1',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns?application=abm&platform=web&insight=false`, campaigns);
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(res.statusCode).toEqual(200);
		const response = res._getData();
		expect(response).toContain('<html>');
		expect(response).toContain('<title>ABM Debug</title>');
		expect(response).toContain('<body>');
		expect(response).toContain('Preview at this index does not exist');
	});
	test('should return 501 not implemented if endpoint is disabled in launch darkly', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockResolvedValue(false),
		};
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Not implemented');
	});
	test('should return 501 not implemented if call to launch darkly fails', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockRejectedValue(new Error('Unable to connect to Launch Darkly')),
		};
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Not implemented');
	});

	test('should throw an error pigeon call has failed with internal server error', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns?application=abm&platform=web&insight=false`, { throws: new Error('Internal Server Error') });
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Internal Server Error');
	});

	test('should throw an Invalid json error if response received is html', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			query: {
				application: 'abm',
				platform: 'web',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.get(`${pigeonURL}/v1/campaigns?application=abm&platform=web&insight=false`, '<div>Random html</div>');
		const f = getRenderedCampaigns({ fetch, logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toContain('invalid json response body');
	});
});
