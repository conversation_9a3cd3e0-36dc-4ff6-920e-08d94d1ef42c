jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const setRenderedCampaignDisposition = require('../set-rendered-campaign-disposition');
const PigeonApi = require('../../service-clients/pigeon-api');

const pigeonURL = 'https://pigeon.apps.bns';
const pigeonAtlasURL = 'https://pigeon.apps.atlas.bns';
const serviceToken = 'sample.service.token';
const preferredEnv = 'istgreen';
const language = 'en';
const channelId = 'ABM';
const cardNumber = '****************';
const ruleId = 'hGdu2Bdg2Jm';
const messageId = '12345678';

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const launchDarklyService = {
	isFeatureEnabled: jest.fn().mockImplementation(flagName => {
		if (flagName === 'pigeon-web.downstreams.pigeon-api-atlas') {
			return Promise.resolve(false);
		}
		return Promise.resolve(true);
	}),
};

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const pigeonApi = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

describe('Set rendered campaign disposition', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});
	test('should set a disposition', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: ruleId,
			},
			query: {},
			body: {
				message_id: messageId,
				application: 'abm',
				platform: 'web',
				page: 'page',
				container: 'container',
				disposition: 'V',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.post(`${pigeonURL}/v1/dispositions`, { data: {}, notifications: [] });
		const f = setRenderedCampaignDisposition({ logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			body: JSON.stringify({
				rule_id: ruleId,
				message_id: messageId,
				application: 'abm',
				platform: 'web',
				page: 'page',
				container: 'container',
				disposition: 'V',
			}),
		});
		expect(res.statusCode).toEqual(200);
	});
	test('should fail path validation', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: `#${ruleId}`,
			},
			query: {},
			body: {
				message_id: messageId,
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const f = setRenderedCampaignDisposition({ fetch, logger, getServiceToken, launchDarklyService, pigeonURL });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalled();
	});
	test('should fail payload validation', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: ruleId,
			},
			query: {},
			body: {
				message_id: messageId,
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const f = setRenderedCampaignDisposition({ fetch, logger, getServiceToken, launchDarklyService, pigeonURL });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toHaveBeenCalled();
	});
	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: ruleId,
			},
			query: {},
			body: {
				message_id: messageId,
				application: 'abm',
				platform: 'web',
				page: 'page',
				container: 'container',
				disposition: 'V',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		fetch.post(`${pigeonURL}/v1/dispositions`, { throws: new Error('timeout') });
		const f = setRenderedCampaignDisposition({ logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-mock-insight': '1',
				'x-originating-appl-code': 'BFB6',
			},
			body: JSON.stringify({
				rule_id: ruleId,
				message_id: messageId,
				application: 'abm',
				platform: 'web',
				page: 'page',
				container: 'container',
				disposition: 'V',
			}),
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');
	});
	test('should return 501 not implemented if endpoint is disabled in launch darkly', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: ruleId,
			},
			query: {},
			body: {
				message_id: messageId,
				application: 'abm',
				platform: 'web',
				page: 'page',
				container: 'container',
				disposition: 'V',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockResolvedValue(false),
		};
		const f = setRenderedCampaignDisposition({ logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Not implemented');
	});
	test('should return 501 not implemented if call to launch darkly fails', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-mock-insight': '1',
			},
			params: {
				ruleId: ruleId,
			},
			query: {},
			body: {
				message_id: messageId,
				application: 'abm',
				platform: 'web',
				page: 'page',
				container: 'container',
				disposition: 'V',
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockRejectedValue(new Error('Unable to connect to Launch Darkly')),
		};
		const f = setRenderedCampaignDisposition({ logger, launchDarklyService, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Not implemented');
	});
});
