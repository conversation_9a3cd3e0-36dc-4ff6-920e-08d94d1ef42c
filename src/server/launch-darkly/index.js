const createLaunchDarkly = (LaunchDarkly, secret, user, ldConfig) => {
	let ldclient;

	const init = async () => {
		if (!secret) {
			throw new Error('Missing Launch Darkly CDP secret SDK key');
		}
		ldclient = LaunchDarkly.init(secret, ldConfig);
		return ldclient.waitForInitialization();
	};

	return {
		init,
		isFeatureEnabled: (featureName, defaultValue = false) => ldclient.variation(featureName, { key: user, name: 'pigeon-web' }, defaultValue),
		close: () => ldclient.close(),
	};
};

module.exports = createLaunchDarkly;
