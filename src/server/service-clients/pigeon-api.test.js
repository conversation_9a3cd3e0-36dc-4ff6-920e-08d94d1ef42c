const { HttpError } = require('../errors');
const PigeonApi = require('./pigeon-api');

const mockPigeonApiResBody = { data: {}, notifications: [] };
const mockPigeonApiRes = { ok: true, json: () => Promise.resolve(mockPigeonApiResBody) };
const fetch = jest.fn().mockImplementation(() => Promise.resolve(mockPigeonApiRes));
const pigeonURL = 'http://localhost';
const pigeonAtlasURL = 'http://localhost.atlas';
const getServiceToken = jest.fn().mockImplementation(() => Promise.resolve('testaccesstoken'));

const launchDarklyService = {
	isFeatureEnabled: jest.fn().mockResolvedValue(false),
};

describe('pigeon api', () => {
	const api = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

	beforeEach(() => {
		fetch.mockClear();
	});

	it('getCampaign', async () => {
		const res = await api.getCampaign({}, 'rule-id-123', '?param1=value1');
		expect(res).toBe(mockPigeonApiResBody);
	});

	it('getCampaign - with atlas flag on', async () => {
		const api = PigeonApi({ fetch,
			pigeonURL,
			pigeonAtlasURL,
			getServiceToken,
			launchDarklyService: {
				isFeatureEnabled: jest.fn().mockResolvedValueOnce(true),
			} });
		await api.getCampaign({}, 'rule-id-123', '?param1=value1');
		expect(fetch).toHaveBeenCalledWith('http://localhost.atlas/v1/campaigns/rule-id-123?param1=value1', { 'headers': { 'Accept': 'application/json', 'Authorization': 'Bearer testaccesstoken', 'Content-Type': 'application/json' }, 'method': 'GET' });
	});

	it('getCampaign - downstream error', async () => {
		const mockPigeonApiRes = { ok: false, status: 400, text: () => Promise.resolve("{ notifications: [ 'err' ]}") };
		const fetch = jest.fn().mockImplementation(() => Promise.resolve(mockPigeonApiRes));
		const api = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });
		let errThrown;
		try {
			await api.getCampaign({}, 'rule-id-123', '?param1=value1');
		} catch (err) {
			errThrown = err;
		}
		expect(errThrown).toEqual(HttpError.badRequest('Bad request'));
	});

	it('getCampaigns', async () => {
		const res = await api.getCampaigns({}, '?param1=value1');
		expect(res).toBe(mockPigeonApiResBody);
	});

	it('getCampaigns - downstream error', async () => {
		const mockPigeonApiRes = { ok: false, status: 400, text: () => Promise.resolve("{ notifications: [ 'err' ]}") };
		const fetch = jest.fn().mockImplementation(() => Promise.resolve(mockPigeonApiRes));
		const api = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });
		let errThrown;
		try {
			await api.getCampaigns({}, '?param1=query1');
		} catch (err) {
			errThrown = err;
		}
		expect(errThrown).toEqual(HttpError.badRequest('Bad request'));
	});

	it('setDisposition', async () => {
		const headers = {};
		const body = {
			disposition: 'V',
			platform: 'web',
			application: 'abm',
			page: 'preMainMenu',
			container: 'mainOffer',
		};
		const res = await api.setDisposition(headers, body);
		expect(res).toBe(mockPigeonApiResBody);
	});

	it('setDisposition - downstream error', async () => {
		const headers = {};
		const body = {
			disposition: 'V',
			platform: 'web',
			application: 'abm',
			page: 'preMainMenu',
			container: 'mainOffer',
		};
		const mockPigeonApiRes = { ok: false, status: 400, text: () => Promise.resolve("{ notifications: [ 'err' ]}") };
		const fetch = jest.fn().mockImplementation(() => Promise.resolve(mockPigeonApiRes));
		const api = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });
		let errThrown;
		try {
			await api.setDisposition(headers, body);
		} catch (err) {
			errThrown = err;
		}
		expect(errThrown).toEqual(HttpError.badRequest('Bad request'));
	});
});
