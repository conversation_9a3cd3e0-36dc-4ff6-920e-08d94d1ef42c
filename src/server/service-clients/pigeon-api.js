const { mapBnsApiError, pickTruthy } = require('../../common');
const { hostingEnv } = require('../../config');

const _reqHeaders = async (headers, getServiceToken) => pickTruthy({
	'Content-Type': 'application/json',
	'Accept': 'application/json',
	Authorization: `Bearer ${await getServiceToken()}`,
	...headers,
});

// log non-pii passthrough headers
const _reqHeadersLoggable = headers => ({
	...headers,
	authorization: undefined,
	'x-customer-authorization': undefined,
	'x-customer-scotiacard': undefined,
});

const getBaseUrl = async (pigeonURL, pigeonAtlasURL, launchDarklyService) => {
	if (hostingEnv === 'PCF') {
		const isPigeonAtlasFlagEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.downstreams.pigeon-api-atlas', false);
		if (isPigeonAtlasFlagEnabled) {
			return pigeonAtlasURL;
		}
	}
	return pigeonURL;
};

const getCampaign = ({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }) => async (
	headers, ruleId, queryString,
) => {
	const method = 'GET';
	const baseUrl = await getBaseUrl(pigeonURL, pigeonAtlasURL, launchDarklyService);
	const url = `${baseUrl}/v1/campaigns/${ruleId}${queryString}`;
	const response = await fetch(url, {
		method,
		headers: await _reqHeaders(headers, getServiceToken),
	});

	if (!response.ok) {
		throw (await mapBnsApiError({
			message: 'Failed to get campaign by rule id from pigeon-api.',
			request: { method, url, headers: _reqHeadersLoggable(headers) },
			response,
		}));
	}
	return response.json();
};

const getCampaigns = ({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }) => async (
	headers, queryString,
) => {
	const method = 'GET';
	const baseUrl = await getBaseUrl(pigeonURL, pigeonAtlasURL, launchDarklyService);
	const url = `${baseUrl}/v1/campaigns${queryString}`;
	const response = await fetch(url, {
		method,
		headers: await _reqHeaders(headers, getServiceToken),
	});

	if (!response.ok) {
		throw (await mapBnsApiError({
			message: 'Failed to get campaigns list from pigeon-api.',
			request: { method, url, headers: _reqHeadersLoggable(headers) },
			response,
		}));
	}
	return response.json();
};

const setDisposition = ({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }) => async (headers, body) => {
	const baseUrl = await getBaseUrl(pigeonURL, pigeonAtlasURL, launchDarklyService);
	const url = `${baseUrl}/v1/dispositions`;
	const method = 'POST';

	const response = await fetch(url, {
		method,
		headers: await _reqHeaders(headers, getServiceToken),
		body: JSON.stringify(body),
	});

	if (!response.ok) {
		throw (await mapBnsApiError({
			message: 'Failed to set campaign disposition via pigeon-api.',
			request: { method, url, body, headers: _reqHeadersLoggable(headers) },
			response,
		}));
	}
	return response.json();
};

const pigeonApi = ({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }) => {
	return {
		getCampaign: getCampaign({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }),
		getCampaigns: getCampaigns({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }),
		setDisposition: setDisposition({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService }),
	};
};

module.exports = pigeonApi;
