const { getQueryString } = require('../../common');

const getCampaign = ({ headersBuilder, pigeonApi }) => async (req, res, next) => {
	try {
		const headers = {
			...(await headersBuilder(req, res)),
			'x-session-id': req.get('x-session-id'),
		};
		const ruleId = req.params.ruleId;
		const queryString = getQueryString(req.query);

		const result = await pigeonApi.getCampaign(headers, ruleId, queryString);
		res.status(200).json(result);
	} catch (err) {
		next(err);
	}
};

module.exports = getCampaign;
