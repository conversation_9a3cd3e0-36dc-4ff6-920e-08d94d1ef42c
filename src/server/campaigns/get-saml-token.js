const R = require('ramda');
const { mapBnsApi<PERSON>rror, pickTruthy, getQueryString } = require('../../common');
const HttpError = require('../errors/http-error');
const { hostingEnv } = require('../../config');

const getSamlToken = ({
	fetch,
	launchDarklyService,
	logger,
	getServiceToken,
	pigeonURL,
	pigeonAtlasURL,
	jwksURI,
	jwksClient,
	jwt,
	tokenPath,
	tokenClaimsPath,
}) => async (req, res, next) => {
	const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
	const customerToken = isOauth2SsoEnabled ? R.path([ 'passport', 'jwt' ], res.locals) : R.path(tokenPath.split('.'), res.locals);
	if (!customerToken) {
		next(HttpError.unauthorized('Unauthorized - Missing token'));
		return;
	}

	// check if opaque token request - required to support legacy eexperience authentication
	if (req.query.ot) {
		const parseAuthHeader = v => {
			if (!v) return v;
			if (!v.toLowerCase().startsWith('bearer ')) return v;
			const vParts = v.split(' ');
			return vParts[vParts.length - 1];
		};
		const authHeader = req.get('authorization');
		let opaqueToken = parseAuthHeader(authHeader);
		if (!opaqueToken) {
			logger.warn({ message: 'Fall back to SSO session OT - auth header missing OT' });
			opaqueToken = req.session && req.session.passport && req.session.passport.opaqueToken;
		}
		return res.status(200).json({ opaqueToken });
	}

	let customerScotiaCard;
	if (isOauth2SsoEnabled) {
		const jwkResponse = await fetch(jwksURI, { method: 'GET' });
		if (!jwkResponse.ok) {
			next(HttpError.internalServer('Error - Could not fetch jwks'));
			return;
		}
		const jwk = await jwkResponse.json();
		const client = jwksClient({ jwksUri: jwksURI });
		const key = await client.getSigningKey(jwk.keys[0].kid);
		const signingKey = key.getPublicKey();
		const decoded = jwt.verify(customerToken, signingKey, { algorithms: 'RS256' });
		customerScotiaCard = decoded.sub;
	} else {
		customerScotiaCard = R.path([ ...tokenClaimsPath.split('.'), 'sub' ], res.locals);
	}

	const preferredEnv = req.get('Preferred-Environment');
	const language = req.get('x-language');
	const mockInsights = req.get('x-mock-insight');
	const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6'; // Pigeon application EPM code
	let baseUrl = pigeonURL;
	if (hostingEnv === 'PCF') {
		const isPigeonAtlasFlagEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.downstreams.pigeon-api-atlas', false);
		if (isPigeonAtlasFlagEnabled) {
			baseUrl = pigeonAtlasURL;
		}
	}
	try {
		const url = `${baseUrl}/v1/campaigns/${req.params.ruleId}/token${getQueryString(req.query)}`;
		const method = 'GET';
		const response = await fetch(url, {
			method,
			headers: pickTruthy({
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${await getServiceToken()}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': customerScotiaCard,
				'x-language': language,
				'x-mock-insight': mockInsights,
				'x-originating-appl-code': xOriginatingApplCode,
			}),
		});
		if (response.ok) {
			const result = await response.json();
			res.status(200).json(result);
		} else {
			next(await mapBnsApiError({
				message: 'Failed to get saml token for given rule from pigeon-api.',
				request: { method, url },
				response,
			}));
		}
	} catch (err) {
		next(err);
	}
};

module.exports = getSamlToken;
