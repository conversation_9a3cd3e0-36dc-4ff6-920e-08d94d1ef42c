jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const setCampaignDisposition = require('../set-campaign-disposition');
const PigeonApi = require('../../service-clients/pigeon-api');

const pigeonURL = 'https://pigeon.apps.bns';
const pigeonAtlasURL = 'https://pigeon.apps.atlas.bns';
const launchDarklyService = {
	isFeatureEnabled: jest.fn().mockImplementation(flagName => {
		if (flagName === 'pigeon-web.downstreams.pigeon-api-atlas') {
			return Promise.resolve(false);
		}
		return Promise.resolve(true);
	}),
};
const serviceToken = 'sample.service.token';
const customerToken = 'sample.customer.token';
const preferredEnv = 'istgreen';
const language = 'en';
const channelId = 'Mobile';
const cardNumber = '****************';
const campaign1 = {
	id: 'Vp4YWkCvFaw5',
	name: 'pigeon-qa-activities',
	type: 'targetedCampaignPreview',
	container: 'offers-and-programs-it',
	pages: [ 'accounts-it' ],
	urgent: true,
	viewed: true,
	external_ref: {
		campaign_id: 'MESSAGE',
		message_id: 'Vp4YWkCvFaw5',
		message_source: 'DMS',
	},
	start_date: '2020-08-06T08:00:00Z',
	content: {
		name: 'QA - Bruce - Preview: Scotia Home',
		title: 'QA - Bruce - Preview: Scotia Home',
	},
};

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const headersBuilder = jest.fn().mockResolvedValue({
	'Preferred-Environment': preferredEnv,
	'x-language': language,
	'x-channel-id': channelId,
	'x-originating-appl-code': 'BFB6',
	'x-customer-authorization': customerToken,
	'x-customer-scotiacard': cardNumber,
});

const pigeonApi = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

describe('Set campaign disposition', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});

	test('should set a disposition', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {},
			body: {
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		fetch.post(`${pigeonURL}/v1/dispositions`, { data: {}, notifications: [] });
		const f = setCampaignDisposition({ headersBuilder, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-channel-id': channelId,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
			body: JSON.stringify({
				rule_id: campaign1.id,
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			}),
		});
		expect(res.statusCode).toEqual(200);
	});

	test('should fail path validation', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId: `#${campaign1.id}`,
			},
			query: {},
			body: {
				message_id: campaign1.external_ref.message_id,
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const f = setCampaignDisposition({ headersBuilder, pigeonApi });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toBeCalled();
	});

	test('should fail payload validation', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {},
			body: {
				message_id: campaign1.external_ref.message_id,
			},
		});
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { auth: {} } });
		const f = setCampaignDisposition({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, headersBuilder });
		await f(req, res, next);
		expect(fetch).not.toBeCalled();
		expect(next).toHaveBeenCalled();
	});

	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {},
			body: {
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		fetch.post(`${pigeonURL}/v1/dispositions`, { throws: new Error('timeout') });
		const f = setCampaignDisposition({ headersBuilder, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-channel-id': channelId,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
			body: JSON.stringify({
				rule_id: campaign1.id,
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			}),
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');
	});

	test('should return an error if the campaign does not exist - 404', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {},
			body: {
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		fetch.post(`${pigeonURL}/v1/dispositions`, { status: 404 });
		const f = setCampaignDisposition({ headersBuilder, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-channel-id': channelId,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
			body: JSON.stringify({
				rule_id: campaign1.id,
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			}),
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.statusCode).toEqual(404);
		expect(err.message).toEqual('Not found');
	});

	test('should return an error if the request times out - 408', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {},
			body: {
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: {
					jwt: customerToken,
				},
			},
		});
		fetch.post(`${pigeonURL}/v1/dispositions`, { status: 408 });
		const f = setCampaignDisposition({ headersBuilder, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-channel-id': channelId,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
			body: JSON.stringify({
				rule_id: campaign1.id,
				message_id: campaign1.external_ref.message_id,
				application: 'nova',
				platform: 'ios',
				page: 'page',
				container: 'container',
				disposition: 'D',
			}),
		});
		expect(next).toBeCalledTimes(1);
	});
});
