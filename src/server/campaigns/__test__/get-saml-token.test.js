jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const getSamlToken = require('../get-saml-token');

const pigeonURL = 'https://pigeon.apps.bns';
const serviceToken = 'sample.service.token';
const customerToken = 'sample.customer.token';
const samlToken = 'sample.saml.token';
const preferredEnv = 'istgreen';
const language = 'en';
const channelId = 'ABM';
const cardNumber = '****************';
const ruleId = 'hGf94kmNdkL';
const messageId = '12345678';
const jwksURI = 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs';
const jwksResponse = {
	keys: [ {
		kty: 'RSA',
		e: 'AQAB',
		use: 'sig',
		kid: 'QWo32GiQbjJ-8_ciRzJoGGNQG0cCGpdeNu5afhJAAiA',
		alg: 'RS256',
		n: 'way67Wsm3PwL13FFQv8pB-_rPPOXq0ARxTW4fDPtpn3GmnCiY7f1X4Sk5Iye8YfsRLZYBhUtkdRrZ3bPJKjuIcTEC2Iz_th86Y-6jm05EEYERh7iOKZMCLtVslkkRW5x8MNajoTnbvPYlFpaZ9lU7vylbtR0DVg9NhlnBSnLZx0BdMXM8oApoYqS7UxqvrpSS70pQoQqGXjki2iO0fYSfNlosTt6mIBnD6PWESdLpQDb739Qzy1auXOc8T4JQFWznNVgS921-8xbfR7D3yNSPSaduYwXP8hML3o-vcCcPkYglhzywK7vDUpB1bsZoeoEW76RXFiyB-jEaUfJpuGq3Q',
	} ],
};
const tokenPath = 'auth.token';
const tokenClaimsPath = 'auth.claims';

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const jwksClient = jest.fn().mockReturnValue({
	getSigningKey: jest.fn().mockResolvedValue({
		getPublicKey: jest.fn().mockReturnValue('mock-signing-key'),
	}),
});
const jwt = {
	verify: jest.fn().mockReturnValue({ sub: cardNumber }),
};
const logger = {
	info: jest.fn(),
	error: jest.fn(),
	warn: jest.fn(),
};
const launchDarklyService = {
	isFeatureEnabled: jest.fn(),
};

describe('Get campaign', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		logger.warn.mockClear();
		fetch.mockReset();
		launchDarklyService.isFeatureEnabled.mockReset();
	});

	test('should return a campaign', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId,
			},
			query: {
				message_id: messageId,
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${pigeonURL}/v1/campaigns/${ruleId}/token?message_id=${messageId}&application=abm`, { data: { token: samlToken } });
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(2);
		const fetchOpts = fetch.mock.calls[1][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.token).toEqual(samlToken);
	});

	test('should return an opaque token if auth header exists', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Authorization': 'R01zxosnE7gMxLEZ4Sm4ZWBWe4WO8nV8zeVwUpHcC94',
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId,
			},
			query: {
				ot: 'true',
			},
			session: {
				passport: {
					opaqueToken: 'klgb6rNKT5NX1yqpC6Whjtc3cIaApj3S51DuQeBzqKY',
				},
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true);
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).not.toHaveBeenCalled();
		expect(logger.warn).not.toHaveBeenCalled();
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.opaqueToken).toEqual('R01zxosnE7gMxLEZ4Sm4ZWBWe4WO8nV8zeVwUpHcC94');
	});

	test('should fall back to sso opaque token if authorization header doesn\'t exist', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId,
			},
			session: {
				passport: {
					opaqueToken: 'klgb6rNKT5NX1yqpC6Whjtc3cIaApj3S51DuQeBzqKY',
				},
			},
			query: {
				ot: 'true',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true);
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).not.toHaveBeenCalled();
		expect(logger.warn).toHaveBeenCalledWith({ message: 'Fall back to SSO session OT - auth header missing OT' });
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.opaqueToken).toEqual('klgb6rNKT5NX1yqpC6Whjtc3cIaApj3S51DuQeBzqKY');
	});

	test('should throw an error if customer token does not exist', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId,
			},
			query: {
				ot: 'true',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				auth: {
					token: null,
				},
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(false);
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Unauthorized - Missing token');
	});

	test('should return Error - Could not fetch jwks', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId,
			},
			session: {
				passport: {
					opaqueToken: 'klgb6rNKT5NX1yqpC6Whjtc3cIaApj3S51DuQeBzqKY',
				},
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true);
		fetch.get(jwksURI, 500);
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Error - Could not fetch jwks');
	});

	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-customer-scotiacard': cardNumber,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId,
			},
			query: {
				message_id: messageId,
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${pigeonURL}/v1/campaigns/${ruleId}/token?message_id=${messageId}&application=abm`, { throws: new Error('timeout') });
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(2);
		const fetchOpts = fetch.mock.calls[1][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');
	});

	test('should return Failed to get saml token for given rule from pigeon-api.', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-customer-scotiacard': cardNumber,
			},
			params: {
				ruleId,
			},
			query: {
				message_id: messageId,
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${pigeonURL}/v1/campaigns/${ruleId}/token?message_id=${messageId}&application=abm`, { status: 500, body: { message: 'Internal Server Error' },
		});
		const f = getSamlToken({ fetch, launchDarklyService, logger, getServiceToken, pigeonURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(2);
		const fetchOpts = fetch.mock.calls[1][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'Preferred-Environment': preferredEnv,
				'x-customer-authorization': customerToken,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('Bad response from downstream service. Failed to get saml token for given rule from pigeon-api.');
	});
});
