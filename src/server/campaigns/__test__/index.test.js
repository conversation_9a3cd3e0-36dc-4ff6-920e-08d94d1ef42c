jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const campaigns = require('../index');

const pigeonURL = 'https://pigeon.apps.bns';
const tokenPath = 'auth.token';
const tokenClaimsPath = 'auth.claims';
const isOauth2SsoEnabled = true;
const authorize = () => (req, res, next) => next();
const authenticate = (req, res, next) => next();
const sso = () => (req, res, next) => next();
const getServiceToken = jest.fn().mockResolvedValue('sample.service.token');
const logger = {
	info: jest.fn(),
	error: jest.fn(),
};
const rateLimitMiddleware = jest.fn();
const campaignsTotalRateLimiter = jest.fn();
const campaignsClientRateLimiter = jest.fn();

describe('Campaigns', () => {
	test('should have `createRoutes`', () => {
		expect(campaigns.createRoutes).toBeInstanceOf(Function);
	});
	test('should successfully call a route', () => {
		const router = campaigns.createRoutes({
			authenticate,
			authorize,
			getServiceToken,
			fetch,
			isOauth2SsoEnabled,
			logger,
			pigeonURL,
			sso,
			tokenPath,
			tokenClaimsPath,
			rateLimitMiddleware,
			campaignsTotalRateLimiter,
			campaignsClientRateLimiter,
			campaignsType: 'canada',
		});
		expect(router).toBeDefined();
	});

	test('should successfully call a route for campaignType ccau', () => {
		campaigns.createRoutes = jest.fn();
		campaigns.createRoutes({
			authenticate,
			authorize,
			getServiceToken,
			fetch,
			isOauth2SsoEnabled,
			logger,
			pigeonURL,
			sso,
			tokenPath,
			tokenClaimsPath,
			rateLimitMiddleware,
			campaignsTotalRateLimiter,
			campaignsClientRateLimiter,
			campaignsType: 'ccau',
		});
		expect(campaigns.createRoutes).toHaveBeenCalledWith(expect.objectContaining({
			campaignsType: 'ccau',
		}));
	});

	test('getMiddleware should call next if oAuth SSO is enabled', async () => {
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockResolvedValue(true),
		};
		const mockNext = jest.fn();
		const mockAuthorize = () => (req, res, next) => next();
		await campaigns.getMiddleware(launchDarklyService, mockAuthorize)({}, {}, mockNext);
		expect(mockNext).toBeCalled();
	},
	);

	test('getMiddleware should call authorize if oAuth SSO is disabled', async () => {
		const launchDarklyService = {
			isFeatureEnabled: jest.fn().mockResolvedValue(false),
		};
		const mockNext = jest.fn();
		const mockAuthorize = () => (req, res, next) => next();
		await campaigns.getMiddleware(launchDarklyService, mockAuthorize)({}, {}, mockNext);
		expect(mockNext).toBeCalled();
	},
	);
});
