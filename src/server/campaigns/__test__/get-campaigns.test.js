jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const getCampaigns = require('../get-campaigns');
const PigeonApi = require('../../service-clients/pigeon-api');

const pigeonURL = 'https://pigeon.apps.bns';
const pigeonAtlasURL = 'https://pigeon.apps.atlas.bns';
const channelId = 'Mobile';
const serviceToken = 'sample.service.token';
const customerToken = 'sample.customer.token';
const preferredEnv = 'istgreen';
const language = 'en';
const cardNumber = '****************';
const campaign1 = {
	id: 'Vp4YWkCvFaw5',
	name: 'pigeon-qa-activities',
	type: 'targetedCampaignPreview',
	container: 'offers-and-programs-it',
	pages: [
		'accounts-it',
	],
	urgent: true,
	viewed: true,
	external_ref: {
		campaign_id: 'MESSAGE',
		message_id: 'Vp4YWkCvFaw5',
		message_source: 'DMS',
	},
	start_date: '2020-08-06T08:00:00Z',
	content: {
		name: 'QA - Bruce - Preview: Scotia Home',
		title: 'QA - Bruce - Preview: Scotia Home',
	},
};
const campaigns = {
	data: {
		total: 1,
		limit: 1,
		items: [ campaign1 ],
	},
};

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const launchDarklyService = {
	isFeatureEnabled: jest.fn(),
};

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const headersBuilder = jest.fn().mockResolvedValue({
	'preferred-environment': preferredEnv,
	'x-language': language,
	'x-channel-id': channelId,
	'x-originating-appl-code': 'BFB6',
	'x-customer-scotiacard': cardNumber,
});

const headersBuilderAbm = jest.fn().mockResolvedValue({
	'preferred-environment': preferredEnv,
	'x-language': language,
	'x-channel-id': 'ABM',
	'x-originating-appl-code': 'BFB6',
	'x-customer-scotiacard': cardNumber,
});

const pigeonApi = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

describe('Get campaigns', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});
	test('should return a list of campaigns', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-customer-scotiacard': cardNumber,
			},
			query: {
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});

		fetch.get(`${pigeonURL}/v1/campaigns?application=abm`, campaigns);
		const f = getCampaigns({ headersBuilder: headersBuilderAbm, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'preferred-environment': preferredEnv,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-channel-id': 'ABM',
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.items[0].id).toEqual(campaign1.id);
	});

	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-customer-scotiacard': cardNumber,
			},
			query: {
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});

		const pigeonEndpt = `${pigeonURL}/v1/campaigns?application=abm`;
		fetch.get(pigeonEndpt, { throws: new Error('timeout') });
		const f = getCampaigns({ headersBuilder, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'preferred-environment': preferredEnv,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
				'x-channel-id': channelId,
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');

		// error should be 404 if call to pigeon api failed to find a campaign
		fetch.get(pigeonEndpt, 404, { overwriteRoutes: true });
		const f404 = getCampaigns({ headersBuilder, pigeonApi });
		await f404(req, res, next);
		expect(next.mock.calls[1][0].statusCode).toEqual(404);
		expect(next.mock.calls[1][0].message).toEqual('Not found');

		// error should be gateway error if call to pigoen api failed with unexpected error types
		fetch.get(pigeonEndpt, 500, { overwriteRoutes: true });
		const f500 = getCampaigns({ headersBuilder, pigeonApi });
		await f500(req, res, next);
		expect(next.mock.calls[2][0].statusCode).toEqual(502);
		expect(next.mock.calls[2][0].message).toMatch(/Bad response from downstream service/i);
	});
});
