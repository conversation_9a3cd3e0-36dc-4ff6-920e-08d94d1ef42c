jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const createHeadersBuilder = require('../headers-builder');
const { Region } = require('../../utils/constants');
const HttpError = require('../../errors/http-error');
const launchDarklyService = {
	isFeatureEnabled: () => Promise.resolve(true),
};
const customerToken = 'sample.customer.token';
const cardNumber = '****************';
const jwksURI = 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs';
const jwksResponse = {
	keys: [ {
		kty: 'RSA',
		e: 'AQAB',
		use: 'sig',
		kid: 'QWo32GiQbjJ-8_ciRzJoGGNQG0cCGpdeNu5afhJAAiA',
		alg: 'RS256',
		n: 'way67Wsm3PwL13FFQv8pB-_rPPOXq0ARxTW4fDPtpn3GmnCiY7f1X4Sk5Iye8YfsRLZYBhUtkdRrZ3bPJKjuIcTEC2Iz_th86Y-6jm05EEYERh7iOKZMCLtVslkkRW5x8MNajoTnbvPYlFpaZ9lU7vylbtR0DVg9NhlnBSnLZx0BdMXM8oApoYqS7UxqvrpSS70pQoQqGXjki2iO0fYSfNlosTt6mIBnD6PWESdLpQDb739Qzy1auXOc8T4JQFWznNVgS921-8xbfR7D3yNSPSaduYwXP8hML3o-vcCcPkYglhzywK7vDUpB1bsZoeoEW76RXFiyB-jEaUfJpuGq3Q',
	} ],
};

const jwksClient = jest.fn().mockReturnValue({
	getSigningKey: jest.fn().mockResolvedValue({
		getPublicKey: jest.fn().mockReturnValue('mock-signing-key'),
	}),
});
const jwt = {
	verify: jest.fn().mockReturnValue({ sub: cardNumber }),
};

const logger = {
	warn: jest.fn(),
};

describe('headers-builder', () => {
	afterEach(() => {
		fetch.mockReset();
		logger.warn.mockClear();
	});

	describe('Region.CCAU', () => {
		test('should return built headers', async () => {
			const req = httpMocks.createRequest({
				query: { channel: 'WAVE' },
				headers: { 'x-originating-appl-code': 'BF94' },
			});
			const res = httpMocks.createResponse({
				locals: {
					passport: {
						customer: {
							lastName: 'lastname',
							customerKey: '494aad34-5ed3-4ee8-8ff3-b5c097100f43',
							cardType: 'U',
							lastAccess: '2023-08-15T21:01:36.092Z',
							localCurrenc: 'DOP',
							login: 'leapautod48',
							locale: 'en_DO',
							ssoSessionId: '2d410d1c-a8aa-4e60-95b2-9846fcee467d',
							authSessionId: '555a9d08-563b-4301-9621-40e1ee066ce3',
							firstName: 'VÉNKfirstname',
							transitId: '70755',
							customerType: 'CIF',
							countryCode: 'DOM',
							dob: '1980-10-10',
							leapClientId: '6399',
							email: '<EMAIL>',
							cardNumber: null,
							cid: '214000000001478',
						},
					},
				},
			});

			const headersBuilder = createHeadersBuilder({
				fetch,
				launchDarklyService,
				jwksURI,
				jwksClient,
				jwt,
				region: Region.CCAU,
				logger,
			});
			const result = await headersBuilder(req, res);
			expect(result).toEqual({
				'x-channel-id': 'WAVE',
				'x-country-code': 'DO',
				'x-language': 'en',
				'x-originating-appl-code': 'BF94',
				'x-user-context': 'eyJjb3VudHJ5Q29kZSI6IkRPTSIsImNpZCI6IjIxNDAwMDAwMDAwMTQ3OCIsImN1c3RvbWVySWQiOiI0OTRhYWQzNC01ZWQzLTRlZTgtOGZmMy1iNWMwOTcxMDBmNDMiLCJsb2NhbGUiOiJlbl9ETyIsImxvZ2luIjoibGVhcGF1dG9kNDgiLCJ0cmFuc2l0SWQiOiI3MDc1NSIsImZpcnN0TmFtZSI6IlbDiU5LwplmaXJzdG5hbWUiLCJsYXN0TmFtZSI6Imxhc3RuYW1lIiwiZW1haWwiOiJFTlBURVNUQFNDT1RJQUJBTksuQ09NIiwiY2FyZFR5cGUiOiJVIiwiZG9iIjoiMTk4MC0xMC0xMCIsImxlYXBDbGllbnRJZCI6IjYzOTkifQ==',
			});
		});

		test('should log warning country code mismatch', async () => {
			const req = httpMocks.createRequest({
				query: {
					country: 'TT',
				},
			});
			const res = httpMocks.createResponse({
				locals: {
					passport: {
						customer: {
							lastName: 'lastname',
							customerKey: '494aad34-5ed3-4ee8-8ff3-b5c097100f43',
							cardType: 'U',
							lastAccess: '2023-08-15T21:01:36.092Z',
							localCurrenc: 'DOP',
							login: 'leapautod48',
							locale: 'en_DO',
							ssoSessionId: '2d410d1c-a8aa-4e60-95b2-9846fcee467d',
							authSessionId: '555a9d08-563b-4301-9621-40e1ee066ce3',
							firstName: 'VÉNKfirstname',
							transitId: '70755',
							customerType: 'CIF',
							countryCode: 'DOM',
							dob: '1980-10-10',
							leapClientId: '6399',
							email: '<EMAIL>',
							cardNumber: null,
							cid: '214000000001478',
						},
					},
				},
			});

			const headersBuilder = createHeadersBuilder({
				fetch,
				launchDarklyService,
				jwksURI,
				jwksClient,
				jwt,
				region: Region.CCAU,
				logger,
			});
			await headersBuilder(req, res);
			expect(logger.warn).toHaveBeenCalledTimes(1);
		});

		test('should log warning language mismatch', async () => {
			const req = httpMocks.createRequest({
				query: {
					country: 'DO',
					language: 'es',
				},
			});
			const res = httpMocks.createResponse({
				locals: {
					passport: {
						customer: {
							lastName: 'lastname',
							customerKey: '494aad34-5ed3-4ee8-8ff3-b5c097100f43',
							cardType: 'U',
							lastAccess: '2023-08-15T21:01:36.092Z',
							localCurrenc: 'DOP',
							login: 'leapautod48',
							locale: 'en_DO',
							ssoSessionId: '2d410d1c-a8aa-4e60-95b2-9846fcee467d',
							authSessionId: '555a9d08-563b-4301-9621-40e1ee066ce3',
							firstName: 'VÉNKfirstname',
							transitId: '70755',
							customerType: 'CIF',
							countryCode: 'DOM',
							dob: '1980-10-10',
							leapClientId: '6399',
							email: '<EMAIL>',
							cardNumber: null,
							cid: '214000000001478',
						},
					},
				},
			});

			const headersBuilder = createHeadersBuilder({
				fetch,
				launchDarklyService,
				jwksURI,
				jwksClient,
				jwt,
				region: Region.CCAU,
				logger,
			});
			await headersBuilder(req, res);
			expect(logger.warn).toHaveBeenCalledTimes(1);
		});
	});

	describe('Region.CANADA', () => {
		test('should return built headers', async () => {
			const req = httpMocks.createRequest({
				headers: {
					'Preferred-Environment': 'uatred', // test case insensitivity on mixed case data
					'x-language': 'en',
					'x-mock-insight': 'false',
					'x-channel-id': 'Mobile',
					'x-application': 'N1',
					'x-b3-spanid': '123',
					'x-b3-traceid': '456',
					'x-originating-appl-code': 'ABC1',
					'Embedded': 'test',
					'x-feature-flag-uid': '58823D5D-67A7-44E7-860E-2D7D7AA3D313',
				},
			});
			const res = httpMocks.createResponse({
				cookie: {},
				cookies: {},
				locals: {
					passport: { jwt: customerToken },
				},
			});

			const headersBuilder = createHeadersBuilder({
				fetch,
				launchDarklyService,
				jwksURI,
				jwksClient,
				jwt,
				region: Region.CANADA,
				logger,
			});
			fetch.get(jwksURI, jwksResponse);
			const result = await headersBuilder(req, res);
			expect(fetch).toHaveBeenCalledTimes(1);
			expect(result).toEqual({
				'x-customer-scotiacard': '****************',
				'preferred-environment': 'uatred',
				'x-language': 'en',
				'x-mock-insight': 'false',
				'x-channel-id': 'Mobile',
				'x-application': 'N1',
				'x-b3-spanid': '123',
				'x-b3-traceid': '456',
				'x-originating-appl-code': 'ABC1',
				'Embedded': 'test',
				'x-feature-flag-uid': '58823D5D-67A7-44E7-860E-2D7D7AA3D313',
			});
		});

		test('should reject when customer token missing', async () => {
			const req = httpMocks.createRequest({});
			const res = httpMocks.createResponse({});

			const headersBuilder = createHeadersBuilder({
				fetch,
				launchDarklyService,
				jwksURI,
				jwksClient,
				jwt,
				region: Region.CANADA,
				logger,
			});
			fetch.get(jwksURI, jwksResponse);
			try {
				await headersBuilder(req, res);
				throw new Error('should not reach');
			} catch (err) {
				expect(err).toEqual(HttpError.unauthorized('Unauthorized - Missing token'));
			}
			expect(fetch).toHaveBeenCalledTimes(0);
		});

		test('should reject when could not fetch certs from passport', async () => {
			const req = httpMocks.createRequest({});
			const res = httpMocks.createResponse({
				cookie: {},
				cookies: {},
				locals: {
					passport: { jwt: customerToken },
				},
			});

			const headersBuilder = createHeadersBuilder({
				fetch,
				launchDarklyService,
				jwksURI,
				jwksClient,
				jwt,
				region: Region.CANADA,
				logger,
			});
			fetch.get(jwksURI, { status: 404 });
			try {
				await headersBuilder(req, res);
				throw new Error('should not reach');
			} catch (err) {
				expect(err).toEqual(HttpError.internalServer('Error - Could not fetch jwks'));
			}
			expect(fetch).toHaveBeenCalledTimes(1);
		});
	});
});
