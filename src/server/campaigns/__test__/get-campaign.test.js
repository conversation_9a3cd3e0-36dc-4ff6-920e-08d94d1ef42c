jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const getCampaign = require('../get-campaign');
const PigeonApi = require('../../service-clients/pigeon-api');

const pigeonURL = 'https://pigeon.apps.bns';
const pigeonAtlasURL = 'https://pigeon.apps.atlas.bns';
const serviceToken = 'sample.service.token';
const customerToken = 'sample.customer.token';
const preferredEnv = 'istgreen';
const language = 'en';
const channelId = 'Mobile';
const xApplication = 'N1';
const sessionId = 'session-id-123';
const cardNumber = '****************';
const campaign1 = {
	id: 'Vp4YWkCvFaw5',
	name: 'pigeon-qa-activities',
	type: 'targetedCampaignPreview',
	container: 'offers-and-programs-it',
	pages: [
		'accounts-it',
	],
	urgent: true,
	viewed: true,
	external_ref: {
		campaign_id: 'MESSAGE',
		message_id: 'Vp4YWkCvFaw5',
		message_source: 'DMS',
	},
	start_date: '2020-08-06T08:00:00Z',
	content: {
		name: 'QA - Bruce - Preview: Scotia Home',
		title: 'QA - Bruce - Preview: Scotia Home',
	},
};

const next = jest.fn();
const getServiceToken = jest.fn().mockResolvedValue(serviceToken);
const launchDarklyService = {
	isFeatureEnabled: jest.fn(),
};

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const headersBuilder = jest.fn().mockResolvedValue({
	'preferred-environment': preferredEnv,
	'x-language': language,
	'x-application': 'N1',
	'x-channel-id': channelId,
	'x-originating-appl-code': 'BFB6',
	'x-customer-scotiacard': cardNumber,
});

const headersBuilderABM = jest.fn().mockResolvedValue({
	'preferred-environment': preferredEnv,
	'x-language': language,
	'x-channel-id': 'ABM',
	'x-originating-appl-code': 'BFB6',
	'x-customer-scotiacard': cardNumber,
});

const headersBuilderCCAU = jest.fn().mockResolvedValue({
	'preferred-environment': preferredEnv,
	'x-originating-appl-code': 'BF94',
	'x-country-code': 'DO',
	'x-language': 'es',
	'x-channel-id': 'WAVE',
});

const pigeonApi = PigeonApi({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });

describe('Get campaign', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});
	test('should return a campaign', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': language,
				'x-channel-id': channelId,
				'x-customer-scotiacard': cardNumber,
				'x-application': xApplication,
				'x-session-id': sessionId,
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});

		const pigeonEndpt = `${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm`;
		fetch.get(pigeonEndpt, { data: campaign1 });
		const f = getCampaign({ headersBuilder: headersBuilderABM, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				// preferred env header field name converted to lowercase by node's req.headers due to:
				// https://datatracker.ietf.org/doc/html/rfc7540#section-8.1.2
				'preferred-environment': preferredEnv,
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-channel-id': 'ABM',
				'x-session-id': sessionId,
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.id).toEqual(campaign1.id);
	});

	test('should return an error if call to pigeon has failed with an exception', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'preferred-environment': preferredEnv,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});

		const pigeonEndpt = `${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm`;
		fetch.get(pigeonEndpt, { throws: new Error('timeout') });
		const f = getCampaign({ headersBuilder: headersBuilderABM, pigeonApi });
		await f(req, res, next);
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				'preferred-environment': preferredEnv,
				'x-channel-id': 'ABM',
				'x-customer-scotiacard': cardNumber,
				'x-language': language,
				'x-originating-appl-code': 'BFB6',
			},
		});
		expect(next).toBeCalledTimes(1);
		const err = next.mock.calls[0][0];
		expect(err.message).toEqual('timeout');

		// error should be 404 if call to pigeon api failed to find a campaign
		fetch.get(pigeonEndpt, 404, { overwriteRoutes: true });
		const f404 = getCampaign({ headersBuilder, pigeonApi });
		await f404(req, res, next);
		expect(next.mock.calls[1][0].statusCode).toEqual(404);
		expect(next.mock.calls[1][0].message).toEqual('Not found');

		// error should be gateway error if call to pigoen api failed with unexpected error types
		fetch.get(pigeonEndpt, 500, { overwriteRoutes: true });
		const f500 = getCampaign({ headersBuilder, pigeonApi });
		await f500(req, res, next);
		expect(next.mock.calls[2][0].statusCode).toEqual(502);
		expect(next.mock.calls[2][0].message).toMatch(/Bad response from downstream service/i);
	});

	test('for CCAU, should return a campaign, use channel query as header', async () => {
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-customer-scotiacard': cardNumber,
				'x-application': xApplication,
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				channel: 'WAVE',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: { jwt: customerToken },
			},
		});

		const pigeonEndpt = `${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&channel=WAVE`;
		fetch.get(pigeonEndpt, { data: campaign1 });
		const f = getCampaign({ headersBuilder: headersBuilderCCAU, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				// preferred env header field name converted to lowercase by node's req.headers due to:
				// https://datatracker.ietf.org/doc/html/rfc7540#section-8.1.2
				'preferred-environment': preferredEnv,
				'x-language': 'es',
				'x-channel-id': 'WAVE',
				'x-country-code': 'DO',
				'x-originating-appl-code': 'BF94',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.id).toEqual(campaign1.id);
	});

	test('should return a campaign, use language from SSO token', async () => {
		headersBuilder.mockResolvedValue({ 'x-country-code': 'DO' });
		const req = httpMocks.createRequest({
			headers: {
				'Preferred-Environment': preferredEnv,
				'x-language': undefined,
				'x-application': xApplication,
				'x-session-id': sessionId,
			},
			params: {
				ruleId: campaign1.id,
			},
			query: {
				message_id: campaign1.external_ref.message_id,
				application: 'abm',
				channel: 'WAVE',
			},
		});
		const res = httpMocks.createResponse({
			cookie: {},
			cookies: {},
			locals: {
				passport: {
					jwt: customerToken,
					customer: {
						locale: 'es_DO',
					},
				},
			},
		});

		const pigeonEndpt = `${pigeonURL}/v1/campaigns/${campaign1.id}?message_id=${campaign1.external_ref.message_id}&application=abm&channel=WAVE`;
		fetch.get(pigeonEndpt, { data: campaign1 });
		const f = getCampaign({ headersBuilder: headersBuilderCCAU, pigeonApi });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		const fetchOpts = fetch.mock.calls[0][1];
		expect(fetchOpts).toEqual({
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Accept': 'application/json',
				Authorization: `Bearer ${serviceToken}`,
				// preferred env header field name converted to lowercase by node's req.headers due to:
				// https://datatracker.ietf.org/doc/html/rfc7540#section-8.1.2
				'preferred-environment': preferredEnv,
				'x-language': 'es',
				'x-channel-id': 'WAVE',
				'x-session-id': sessionId,
				'x-country-code': 'DO',
				'x-originating-appl-code': 'BF94',
			},
		});
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.id).toEqual(campaign1.id);
	});
});
