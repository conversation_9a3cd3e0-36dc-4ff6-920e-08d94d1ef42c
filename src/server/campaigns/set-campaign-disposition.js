const Joi = require('@hapi/joi');
const { dispositionPathSchema, dispositionBodySchema } = require('../../common/validation');
const HttpError = require('../errors/http-error');
const msgValidationError = 'Validation Error';

const setCampaignDisposition = ({
	headersBuilder,
	pigeonApi,
}) => async (req, res, next) => {
	try {
		let { error, value: opts } = Joi.validate(req.params, dispositionPathSchema, {
			stripUnknown: true,
		});
		if (error) {
			next(
				HttpError.badRequest(
					msgValidationError,
					error.details.map((err) => ({
						path: err.path.join('.'),
						message: err.message,
					})),
				),
			);
			return;
		}
		({ error, value: opts } = Joi.validate(
			{ rule_id: opts.ruleId, ...req.body },
			dispositionBodySchema,
			{ stripUnknown: true },
		));

		if (error) {
			next(
				HttpError.badRequest(
					msgValidationError,
					error.details.map((err) => ({
						path: err.path.join('.'),
						message: err.message,
					})),
				),
			);
			return;
		}
		const headers = await headersBuilder(req, res);
		const result = await pigeonApi.setDisposition(headers, opts);
		res.status(200).json(result);
	} catch (err) {
		next(err);
	}
};

module.exports = setCampaignDisposition;
