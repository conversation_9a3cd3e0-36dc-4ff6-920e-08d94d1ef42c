const { Router } = require('express');
const getCampaigns = require('./get-campaigns');
const getCampaign = require('./get-campaign');
const setCampaignDisposition = require('./set-campaign-disposition');
const getSamlToken = require('./get-saml-token');
const createHeadersBuilder = require('./headers-builder');
const { Region } = require('../utils/constants');

const getMiddleware = (launchDarklyService, authorize) => async (req, res, next) => {
	const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
	if (!isOauth2SsoEnabled) {
		authorize([ 'standard' ])(req, res, next);
	} else {
		next();
	}
};

const createRoutes = ({
	authenticate,
	authorize,
	getServiceToken,
	fetch,
	launchDarklyService,
	logger,
	pigeonApi,
	pigeonURL,
	pigeonAtlasURL,
	sso,
	jwksURI,
	jwksClient,
	jwt,
	tokenPath,
	tokenClaimsPath,
	rateLimitMiddleware,
	campaignsTotalRateLimiter,
	campaignsClientRateLimiter,
	region,
}) => {
	const router = new Router();
	// set authentication middleware
	router.use(async (req, res, next) => {
		const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
		const authFunction = isOauth2SsoEnabled ? sso : authenticate;
		authFunction(req, res, next);
	});
	router.use(rateLimitMiddleware, campaignsTotalRateLimiter, campaignsClientRateLimiter);
	const headersBuilder = createHeadersBuilder({ fetch, launchDarklyService, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath, region, logger });
	// set route handlers
	router.get(
		'/',
		getMiddleware(launchDarklyService, authorize),
		getCampaigns({ headersBuilder, pigeonApi }));
	router.get(
		'/:ruleId',
		getMiddleware(launchDarklyService, authorize),
		getCampaign({ headersBuilder, pigeonApi }));
	if (region === Region.CANADA) {
		router.get(
			'/:ruleId/token',
			getMiddleware(launchDarklyService, authorize),
			getSamlToken({ getServiceToken, fetch, launchDarklyService, logger, pigeonURL, pigeonAtlasURL, jwksURI, jwksClient, jwt, tokenPath, tokenClaimsPath }));
	}
	router.post(
		'/:ruleId/dispositions',
		getMiddleware(launchDarklyService, authorize),
		setCampaignDisposition({ headersBuilder, pigeonApi }));
	return router;
};

module.exports = {
	createRoutes,
	getMiddleware,
};
