const crypto = require('crypto');
const HttpError = require('../errors/http-error');
const { getCCAUHeaders } = require('../utils/get-ccau-headers');
const R = require('ramda');
const { Region } = require('../utils/constants');
const { pickReqHeaders, pickTruthy } = require('../../common');

/**
 * Generates object containing standard request headers for pigeon-api
 * for requests arriving through sso enabled routes
 *
 * @param {Object} options
 * @returns
 */
const createHeadersBuilder = ({
	fetch,
	launchDarklyService,
	jwksURI,
	jwksClient,
	jwt,
	tokenPath,
	tokenClaimsPath,
	region,
	logger,
}) =>
	async (req, res) => {
		const passthroughHeaders = pickReqHeaders(req, [ // passthrough headers
			'preferred-environment',
			'x-language',
			'x-mock-insight',
			'x-channel-id',
			'x-application',
			'Embedded',
			'x-feature-flag-uid',
		]);

		const commonHeaders = {
			...passthroughHeaders,
			'x-originating-appl-code': req.get('x-originating-appl-code') || 'BFB6',
			'x-b3-spanid': req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex'),
			'x-b3-traceid': req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex'),
		};

		if (region === Region.CCAU) {
			if (
			res.locals?.passport?.customer?.locale?.substring(3) !== req.query.country ||
			res.locals?.passport?.customer?.locale?.split('_')[0] !== req.query.language
			) {
				logger.warn({ message: 'Mismatch of country code or language for customer token' });
			}
			return pickTruthy({
				...commonHeaders,
				...getCCAUHeaders(req, res),
			});
		}

		const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
		const customerToken = isOauth2SsoEnabled ? R.path([ 'passport', 'jwt' ], res.locals) : R.path(tokenPath.split('.'), res.locals);
		if (!customerToken) {
			return Promise.reject(HttpError.unauthorized('Unauthorized - Missing token'));
		}

		let customerScotiaCard;
		if (isOauth2SsoEnabled) {
			const jwkResponse = await fetch(jwksURI, { method: 'GET' });
			if (!jwkResponse.ok) {
				return Promise.reject(HttpError.internalServer('Error - Could not fetch jwks'));
			}
			const jwk = await jwkResponse.json();
			const client = jwksClient({ jwksUri: jwksURI });
			const key = await client.getSigningKey(jwk.keys[0].kid);
			const signingKey = key.getPublicKey();
			const decoded = jwt.verify(customerToken, signingKey, { algorithms: 'RS256' });
			customerScotiaCard = decoded.sub;
		} else {
			customerScotiaCard = R.path([ ...tokenClaimsPath.split('.'), 'sub' ], res.locals);
		}

		return pickTruthy({
			...commonHeaders,
			'x-customer-scotiacard': customerScotiaCard,
		});
	};

module.exports = createHeadersBuilder;
