const { getQueryString } = require('../../common');
const getCampaigns = ({	headersBuilder, pigeonApi }) => async (req, res, next) => {
	try {
		const headers = {
			...(await headersBuilder(req, res)),
			'x-session-id': req.get('x-session-id'),
		};
		const queryString = getQueryString(req.query);

		const result = await pigeonApi.getCampaigns(headers, queryString);
		res.status(200).json(result);
	} catch (err) {
		next(err);
	}
};

module.exports = getCampaigns;
