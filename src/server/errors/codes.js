module.exports = {
	'400': 'HTTP_BAD_REQUEST',
	'401': 'HTTP_UNAUTHORIZED',
	'402': 'HTTP_PAYMENT_REQUIRED',
	'403': 'HTTP_FORBIDDEN',
	'404': 'HTTP_NOT_FOUND',
	'405': 'HTTP_METHOD_NOT_ALLOWED',
	'406': 'HTTP_NOT_ACCEPTABLE',
	'407': 'HTTP_PROXY_AUTHENTICATION_REQUIRED',
	'408': 'HTTP_REQUEST_TIMEOUT',
	'409': 'HTTP_CONFLICT',
	'410': 'HTTP_GONE',
	'411': 'HTTP_LENGTH_REQUIRED',
	'412': 'HTTP_PRECONDITION_FAILED',
	'413': 'HTTP_PAYLOAD_TOO_LARGE',
	'414': 'HTTP_URI_TOO_LONG',
	'415': 'HTTP_UNSUPPORTED_MEDIA_TYPE',
	'416': 'HTTP_RANGE_NOT_SATISFIABLE',
	'417': 'HTTP_EXPECTATION_FAILED',
	'418': 'HTTP_I_AM_A_TEAPOT',
	'421': 'HTTP_MISDIRECTED_REQUEST',
	'422': 'HTTP_UNPROCESSABLE_ENTITY',
	'423': 'HTTP_LOCKED',
	'424': 'HTTP_FAILED_DEPENDENCY',
	'426': 'HTTP_UPGRADE_REQUIRED',
	'428': 'HTTP_PRECONDITION_REQUIRED',
	'429': 'HTTP_TOO_MANY_REQUESTS',
	'431': 'HTTP_REQUEST_HEADER_FIELDS_TOO_LARGE',
	'451': 'HTTP_UNAVAILABLE_FOR_LEGAL_REASONS',
	'500': 'HTTP_INTERNAL_SERVER_ERROR',
	'501': 'HTTP_NOT_IMPLEMENTED',
	'502': 'HTTP_BAD_GATEWAY',
	'503': 'HTTP_SERVICE_UNAVAILABLE',
	'504': 'HTTP_GATEWAY_TIMEOUT',
	'505': 'HTTP_VERSION_NOT_SUPPORTED',
	'506': 'HTTP_VARIANT_ALSO_NEGOTIATES',
	'507': 'HTTP_INSUFFICIENT_STORAGE',
	'508': 'HTTP_LOOP_DETECTED',
	'509': 'HTTP_BANDWIDTH_LIMIT_EXCEEDED',
	'510': 'HTTP_NOT_EXTENDED',
	'511': 'HTTP_NETWORK_AUTHENTICATION_REQUIRED',
};
