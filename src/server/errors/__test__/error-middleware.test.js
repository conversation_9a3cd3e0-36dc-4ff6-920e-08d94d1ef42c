const { CerberusError, CerberusErrorCodes } = require('pigeon-cerberus');
const errorMiddleware = require('../error-middleware');
const HttpError = require('../http-error');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = { status: mockStatus };
const mockLogger = {
	info: jest.fn(),
	error: jest.fn(),
};

describe('Error middleware', () => {
	beforeEach(() => {
		mockNext.mockClear();
		mockJson.mockClear();
		mockStatus.mockClear();
		mockStatus.mockReturnValue({ json: mockJson });
		mockLogger.info.mockClear();
		mockLogger.error.mockClear();
	});
	test('should create a middleware', () => {
		const mw = errorMiddleware({});
		expect(typeof mw).toEqual('function');
	});
	test('pass error to next if header was already sent', () => {
		const mw = errorMiddleware({});
		const error = new Error('some error');
		mw(error, {}, { headerSent: true }, mockNext);
		expect(mockNext.mock.calls.length).toEqual(1);
		expect(mockNext.mock.calls[0][0]).toEqual(error);
	});
	test('should transform unknown error to Internal Server Error', () => {
		const message = 'some error';
		const mw = errorMiddleware({});
		const error = new Error(message);
		mw(error, {}, mockRes);
		expect(mockStatus).toBeCalledWith(500);
		expect(mockJson.mock.calls.length).toEqual(1);
		expect(typeof mockJson.mock.calls[0][0]).toEqual('object');
		expect(mockJson.mock.calls[0][0].message.indexOf('Internal Server Error')).not.toBe(-1);
		expect(mockJson.mock.calls[0][0].message.indexOf(message)).toBe(-1);
	});
	test('should send known arror as is', () => {
		const message = 'entity not found';
		const mw = errorMiddleware({});
		const notFound = HttpError.notFound(message);
		mw(notFound, {}, mockRes);
		expect(mockStatus).toBeCalledWith(notFound.statusCode);
		expect(mockJson.mock.calls.length).toEqual(1);
		expect(typeof mockJson.mock.calls[0][0]).toEqual('object');
		expect(mockJson.mock.calls[0][0].message.indexOf(message)).not.toBe(-1);
	});
	test('should send any 500+ as an Internal Server Error', () => {
		const mockMessage = 'bad gateway';
		const err = HttpError.badGateway(mockMessage);
		const mw = errorMiddleware({ logger: mockLogger });
		mw(err, {}, mockRes);
		expect(mockStatus).toBeCalledWith(500);
		expect(mockJson.mock.calls[0][0].message).not.toEqual(mockMessage);
		expect(mockLogger.error).toBeCalled();
	});
	test('should send 401 if not authenticated', () => {
		const err = new CerberusError(CerberusErrorCodes.OpaqueTokenExpired);
		const mw = errorMiddleware({ logger: mockLogger });
		mw(err, {}, mockRes);
		expect(mockStatus).toBeCalledWith(401);
		expect(mockJson.mock.calls[0][0].message).toEqual('Unauthorized');
		expect(mockLogger.info).toBeCalled();
	});
});
