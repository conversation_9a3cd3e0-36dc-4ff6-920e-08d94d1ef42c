const { CerberusErrorCodes } = require('pigeon-cerberus');
const HttpError = require('./http-error');

module.exports = ({ logger = null }) => (err, req, res, next) => {
	if (res.headerSent) {
		next(err);
		return;
	}
	if (err instanceof HttpError && (err.statusCode < 500 || err.statusCode === 501)) {
		// known error
		logger && typeof logger.error === 'function' && logger.error({
			message: err.message,
			metadata: err.metadata,
			code: err.code,
			stack: err.stack,
			timestamp: err.timestamp,
			uuid: err.uuid,
		});
		delete err.metadata;
		res.status(err.statusCode).json(err);
		return;
	}
	if (err.name === 'CerberusError') {
		// auth error
		const authError = [ CerberusErrorCodes.InvalidScope ].includes(err.code)
			? HttpError.forbidden('Forbidden')
			: HttpError.unauthorized('Unauthorized');
		logger.info({ message: 'auth error', err, timestamp: authError.timestamp, uuid: authError.uuid });
		res.status(authError.statusCode).json(authError);
		return;
	}
	const internalError = HttpError.internalServer('Internal Server Error');
	if (Object.prototype.hasOwnProperty.call(err, 'uuid')) {
		internalError.uuid = err.uuid;
	}
	if (Object.prototype.hasOwnProperty.call(err, 'timestamp')) {
		internalError.timestamp = err.timestamp;
	}
	if (logger && typeof logger.error === 'function') {
		logger.error({
			message: err.message,
			metadata: err.metadata,
			code: err.code,
			stack: err.stack,
			timestamp: err.timestamp,
			uuid: internalError.uuid,
		}, 'unknown error');
	}
	res.status(internalError.statusCode).json(internalError);
};
