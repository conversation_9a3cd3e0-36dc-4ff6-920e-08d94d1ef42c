const getCookieDomain = require('./get-cookie-domain');

describe('Correct cookie domain for various hosts', () => {
	it('should return scointnet.net for dms-web-u.scointnet.net', () => {
		expect(getCookieDomain('dms-web-u.scointnet.net')).toEqual('scointnet.net');
	});

	it('should return scotiabank.com for dms-web.scotiabank.com', () => {
		expect(getCookieDomain('dms-web.scotiabank.com')).toEqual('scotiabank.com');
	});

	it('should return pigeon-admin-uat.apps.cloud.bns for pigeon-admin-uat.apps.cloud.bns', () => {
		expect(getCookieDomain('pigeon-admin-uat.apps.cloud.bns')).toEqual('pigeon-admin-uat.apps.cloud.bns');
	});

	it('should return localhost for localhost:8000', () => {
		expect(getCookieDomain('localhost:8000')).toEqual('localhost');
	});

	it('should return 127.0.0.1 for 127.0.0.1:8000', () => {
		expect(getCookieDomain('127.0.0.1:8000')).toEqual('127.0.0.1');
	});

	it('should return localhost for localhost:8000', () => {
		expect(getCookieDomain('localhost:8000')).toEqual('localhost');
	});

	it('should return empty string for empty input/null/undefined', () => {
		expect(getCookieDomain('')).toEqual('');
		expect(getCookieDomain(null)).toEqual('');
		expect(getCookieDomain(undefined)).toEqual('');
	});

	it('should return correct value for edge case', () => {
		expect(getCookieDomain('dms-web-u.scointnet.net:8000')).toEqual('scointnet.net');
	});
});
