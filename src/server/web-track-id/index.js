const { Router } = require('express');
const getWebTrackId = require('./get-web-track-id');

const getMiddleware = (launchDarklyService, authorize) => async (req, res, next) => {
	const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
	if (!isOauth2SsoEnabled) {
		authorize([ 'standard' ])(req, res, next);
	} else {
		next();
	}
};

const createRoutes = ({
	authenticate,
	authorize,
	getServiceToken,
	fetch,
	launchDarklyService,
	jwksURI,
	jwksClient,
	jwt,
	logger,
	marvelURL,
	marvelAtlasURL,
	sso,
	tokenPath,
	tokenClaimsPath,
}) => {
	const router = new Router();
	// setup authentication middleware
	router.use(async (req, res, next) => {
		const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
		const authFunction = isOauth2SsoEnabled ? sso : authenticate;
		authFunction(req, res, next);
	});
	// get web track id via s2s token and /v4 endpoint
	router.get(
		'/', getMiddleware(launchDarklyService, authorize), async (req, res, next) => {
			getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, marvelAtlasURL, tokenPath, tokenClaimsPath })(req, res, next);
		});
	return router;
};

module.exports = {
	createRoutes,
};
