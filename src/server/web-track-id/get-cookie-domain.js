const TOP_LEVEL_DOMAINS = [ 'scointnet.net', 'scotiabank.com' ];

/**
 * Utility function to get a domain for cookie setting (GUID Marvel call). The logic is:
 * drop ports, if any, split reported host into an array by ".",
 * if it ends with scointnet.net or scotiabank.com (dms-web.scotiabank.com etc)
 * return scointnet.net/scotiabank.com, otherwise return original host
 * @returns {string} Domain for which cookie to be set
 */
const getCookieDomain = (host) => {
	if (!host) {
		return '';
	}
	// check if host has port and drop it
	if (host.indexOf(':') !== -1) {
		host = host.split(':')[0];
	}
	const hostParts = host.split('.');
	if (hostParts.length > 2) {
		const targetDomain = `${hostParts[hostParts.length - 2]}.${hostParts[hostParts.length - 1]}`;
		if (TOP_LEVEL_DOMAINS.includes(targetDomain)) {
			return targetDomain;
		} else {
			return host;
		}
	} else {
		return host;
	}
};

module.exports = getCookieDomain;
