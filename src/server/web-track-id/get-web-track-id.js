const Joi = require('@hapi/joi');
const R = require('ramda');
const crypto = require('crypto');
const { pickTruthy } = require('../../common');
const { webTrackIdSchema } = require('../../common/validation');
const getCookieDomain = require('./get-cookie-domain');
const { defaultCookieOpts } = require('../utils/cookie');
const HttpError = require('../errors/http-error');

const getWebTrackId = ({
	getServiceToken,
	fetch,
	launchDarklyService,
	jwksURI,
	jwksClient,
	jwt,
	logger,
	marvelURL,
	marvelAtlasURL,
	tokenPath,
	tokenClaimsPath,
}) => async (req, res, next) => {
	// Get Headers from request
	const preferredEnv = req.get('Preferred-Environment');
	const spanId = req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex'); // get spand id
	const traceId = req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex'); // get trace id
	const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6'; // get x-originating-appl-code
	const country = res.locals.country || 'ca';
	const xApplication = req.get('x-application');
	const sessionId = req.get('x-session-id');
	const xlanguage = req.get('x-language') || 'en';
	const channelId = req.get('x-channel-id') || 'Mobile';

	const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
	const isWebTrackAtlasEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.downstreams.web-track-atlas', false);
	const customerToken = isOauth2SsoEnabled ? R.path([ 'passport', 'jwt' ], res.locals) : R.path(tokenPath.split('.'), res.locals);
	if (!customerToken) {
		return res.status(401).send('Error: unauthorized');
	}
	let xCustomerScotiacard = R.path([ ...tokenClaimsPath.split('.'), 'sub' ], res.locals);
	if (isOauth2SsoEnabled) {
		const jwkResponse = await fetch(jwksURI, { method: 'GET' });
		if (!jwkResponse.ok) {
			next(HttpError.internalServer('Error - Could not fetch jwks'));
			return;
		}
		const jwk = await jwkResponse.json();
		const client = jwksClient({ jwksUri: jwksURI });
		const key = await client.getSigningKey(jwk.keys[0].kid);
		const signingKey = key.getPublicKey();
		const decoded = jwt.verify(customerToken, signingKey, { algorithms: 'RS256' });
		xCustomerScotiacard = decoded.sub;
	}

	if (!xCustomerScotiacard) {
		logger.error({ message: 'Required field missing from headers: x-customer-scotiacard' });
		return res.status(401).send('Error: Required field missing from headers: x-customer-scotiacard');
	}

	// Validate headers
	const { value, error } = Joi.validate({
		'x-b3-traceid': traceId,
		'x-b3-spanid': spanId,
		'x-channel-id': channelId,
		'x-originating-appl-code': xOriginatingApplCode,
		'x-country-code': country,
		'x-application': xApplication,
		'x-session-id': sessionId,
		'x-language': xlanguage,
		'x-customer-scotiacard': xCustomerScotiacard,
	},
	webTrackIdSchema);

	if (error) {
		const errorObj = error.details.map((err) => ({
			path: err.path.join('.'),
			message: err.message,
		}));
		next(
			HttpError.badRequest(
				`Validation Error: ${errorObj[0].message}`,
			),
		);
		return;
	}

	const serviceToken = await getServiceToken();
	try {
		const headers = {
			'Content-Type': 'application/json',
			'Accept': 'application/json',
			Authorization: `Bearer ${serviceToken}`,
			'preferred-environment': preferredEnv,
			...value,
		};
		const webTrackUrl = isWebTrackAtlasEnabled ? marvelAtlasURL : marvelURL;
		const response = await fetch(`${webTrackUrl}/v4/customers/me/web-track-id`, {
			method: 'GET',
			headers: pickTruthy(headers),
		});
		const result = await response.json();
		if (result.error) {
			logger.error({ message: 'get web track id', result });
			return res.status(401).send('Error: guid error');
		} else if (!result.data) {
			logger.error({ message: 'get web track id', result });
			return res.status(401).send('Error: guid error');
		}
		const guid = result.data.web_track_id;
		const host = req.get('host');
		const domain = getCookieDomain(host);
		const maxAge = 2 * 365 * 24 * 3600 * 1000;
		res.cookie('UserTrackingID', guid, { ...defaultCookieOpts(req), domain, maxAge, sameSite: 'lax', httpOnly: true });
		res.cookie('UniqueUser', guid, { ...defaultCookieOpts(req), domain, maxAge, sameSite: 'lax', httpOnly: true });

		// fetch the set cookie and set it as a header
		const regex = new RegExp('^[a-zA-Z0-9_-]+$');
		if (guid && regex.test(guid)) {
			res.setHeader('UserTrackingID', guid);
			res.setHeader('UniqueUser', guid);
		} else {
			logger.error({ message: `invalid web track id: ${guid}` });
			return res.status(401).send('Error: guid error');
		}
		logger.info({ message: `Calling web track ID with s2s token v4. Get web track id from ${webTrackUrl}` });
		res.status(200).json({ data: { web_track_id: guid } });
	} catch (err) {
		logger.error({ message: 'get web track id', err });
		return res.status(401).send('Error: guid error');
	}
};

module.exports = getWebTrackId;
