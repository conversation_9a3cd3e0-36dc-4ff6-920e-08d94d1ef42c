jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const webTrackId = require('../index');

const marvelURL = 'https://marvel.apps.bns';
const tokenPath = 'auth.token';
const isOauth2SsoEnabled = true;
const authorize = () => (req, res, next) => next();
const authenticate = (req, res, next) => next();
const sso = () => (req, res, next) => next();
const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

describe('Get web track id', () => {
	test('should have `createRoutes`', () => {
		expect(webTrackId.createRoutes).toBeInstanceOf(Function);
	});
	test('should successfully call a route', () => {
		const router = webTrackId.createRoutes({
			authenticate,
			authorize,
			fetch,
			isOauth2SsoEnabled,
			logger,
			marvelURL,
			sso,
			tokenPath,
		});
		expect(router).toBeDefined();
	});
});
