jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
const fetch = require('node-fetch');
const httpMocks = require('node-mocks-http');
const getWebTrackId = require('../get-web-track-id');

const marvelURL = 'https://marvel.apps.bns';
const marvelAtlasURL = 'https://marvel.apps.atlas';
const serviceToken = 'sample.service.token';
const token = 'sample.customer.token';
const preferredEnv = 'istgreen';
const guid = '8f806e43-43ce-40ee-b883-4fc93cd1447f';
const defaultCardNumber = '****************';
const jwksURI = 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs';
const jwksClient = jest.fn().mockReturnValue({
	getSigningKey: jest.fn().mockResolvedValue({
		getPublicKey: jest.fn().mockReturnValue('mock-signing-key'),
	}),
});
const jwksResponse = {
	keys: [ {
		kty: 'RSA',
		e: 'AQAB',
		use: 'sig',
		kid: 'QWo32GiQbjJ-8_ciRzJoGGNQG0cCGpdeNu5afhJAAiA',
		alg: 'RS256',
		n: 'way67Wsm3PwL13FFQv8pB-_rPPOXq0ARxTW4fDPtpn3GmnCiY7f1X4Sk5Iye8YfsRLZYBhUtkdRrZ3bPJKjuIcTEC2Iz_th86Y-6jm05EEYERh7iOKZMCLtVslkkRW5x8MNajoTnbvPYlFpaZ9lU7vylbtR0DVg9NhlnBSnLZx0BdMXM8oApoYqS7UxqvrpSS70pQoQqGXjki2iO0fYSfNlosTt6mIBnD6PWESdLpQDb739Qzy1auXOc8T4JQFWznNVgS921-8xbfR7D3yNSPSaduYwXP8hML3o-vcCcPkYglhzywK7vDUpB1bsZoeoEW76RXFiyB-jEaUfJpuGq3Q',
	} ],
};
const jwt = {
	verify: jest.fn().mockReturnValue({ sub: defaultCardNumber }),
};
const tokenPath = 'auth.token';
const tokenClaimsPath = 'auth.claims';

const next = jest.fn();

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

const getServiceToken = jest.fn().mockResolvedValue(serviceToken);

const launchDarklyService = {
	isFeatureEnabled: jest.fn(),
};

const requiredHeaders = {
	'Preferred-Environment': preferredEnv,
	'x-channel-id': 'Mobile',
	'x-application': 'N1',
	'x-session-id': 'test_session_id',
	'x-language': 'en',
};

describe('Get web track id', () => {
	afterEach(() => {
		next.mockClear();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockReset();
	});
	test('should return a web track id', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { data: { web_track_id: guid } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(2);
		expect(res.cookies.UserTrackingID.value).toEqual(guid);
		expect(res.cookies.UniqueUser.value).toEqual(guid);
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.web_track_id).toEqual(guid);
	});
	test('should return an error if customer token is missing', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: {} });
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { data: { web_track_id: guid } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).not.toBeCalled();
		expect(res.statusCode).toEqual(401);
		expect(res.cookies.UserTrackingID).toBeUndefined();
		expect(res.cookies.UniqueUser).toBeUndefined();
	});
	test('should return an error if customer card is missing', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		const jwt = {
			verify: jest.fn().mockReturnValue({ sub: undefined }),
		};
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { data: { web_track_id: guid } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(1);
		expect(res.statusCode).toEqual(401);
	});
	test('should return an error if call to marvel has failed', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { status: 500, body: { error: 'error' } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(2);
		expect(res.statusCode).toEqual(401);
		expect(res.cookies.UserTrackingID).toBeUndefined();
		expect(res.cookies.UniqueUser).toBeUndefined();
	});
	test('should return an error if call to marvel has failed with an exception', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { throws: new Error('timeout') });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(2);
		expect(logger.error).toBeCalledTimes(1);
		expect(res.statusCode).toEqual(401);
		expect(res.cookies.UserTrackingID).toBeUndefined();
		expect(res.cookies.UniqueUser).toBeUndefined();
	});

	test('should return an error header validation fails', async () => {
		const req = httpMocks.createRequest({ headers: { ...requiredHeaders, 'x-channel-id': 'test-channel-id!@#' } });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { data: { web_track_id: guid } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).toBeCalled();
		expect(fetch).toBeCalledTimes(1);
	});

	test('should return an error if guid is invalid', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		const guid = '8f806e43-43ce-40ee-b883-4fc93cd1447f!@!';
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(false);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelURL}/v4/customers/me/web-track-id`, { data: { web_track_id: guid } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(2);
		expect(logger.error).toBeCalledTimes(1);
		expect(res.statusCode).toEqual(401);
	});

	test('should return a web track id atlas', async () => {
		const req = httpMocks.createRequest({ headers: requiredHeaders });
		const res = httpMocks.createResponse({ cookie: {}, cookies: {}, locals: { passport: { jwt: { token } } } });
		launchDarklyService.isFeatureEnabled.mockResolvedValueOnce(true).mockResolvedValueOnce(true);
		fetch.get(jwksURI, jwksResponse);
		fetch.get(`${marvelAtlasURL}/v4/customers/me/web-track-id`, { data: { web_track_id: guid } });
		const f = getWebTrackId({ getServiceToken, fetch, launchDarklyService, jwksURI, jwksClient, jwt, logger, marvelURL, marvelAtlasURL, tokenPath, tokenClaimsPath });
		await f(req, res, next);
		expect(next).not.toBeCalled();
		expect(fetch).toBeCalledTimes(2);
		expect(res.cookies.UserTrackingID.value).toEqual(guid);
		expect(res.cookies.UniqueUser.value).toEqual(guid);
		expect(res.statusCode).toEqual(200);
		const response = res._getJSONData();
		expect(response.data.web_track_id).toEqual(guid);
	});
});
