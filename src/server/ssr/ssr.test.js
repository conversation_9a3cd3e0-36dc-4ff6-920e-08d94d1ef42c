import { FormattedMessage, IntlProvider, addLocaleData } from 'react-intl';
import LogoScotiabankEn from 'canvas-core-react/lib/LogoScotiabankEn';
import LogoScotiabankFr from 'canvas-core-react/lib/LogoScotiabankFr';

import enMessages from '../../messages/en';
import frMessages from '../../messages/fr';
import esMessages from '../../messages/es';

import en from 'react-intl/locale-data/en';
import fr from 'react-intl/locale-data/fr';
import es from 'react-intl/locale-data/es';
const React = require('react');
const ReactDOM = require('react-dom');
const fetch = require('node-fetch');
const utils = require('util');
const config = require('../../config');
const { encodeImage, transformABMContent, getSSRContent } = require('./ssrHelpers');
const { renderSSR } = require('./index');
const { AbmPreview, AbmPaccDetails, AbmOdpDetails } = require('pigeon-pigeon-web-renderer');
jest.mock('node-fetch');
jest.mock('util');

addLocaleData([ ...en, ...fr, ...es ]);

const messages = {
	en: enMessages,
	fr: frMessages,
	es: esMessages,
};

const logger = {
	info: jest.fn(),
	error: jest.fn(),
};

describe('renderSSR', () => {
	beforeEach(() => {
		ReactDOM.hydrate = jest.fn();
		logger.info.mockClear();
		logger.error.mockClear();
		fetch.mockClear();
	});

	it('calls ReactDOM.hydrate if __INITIAL__DATA__ is defined', () => {
		// arrange
		window.__INITIAL__DATA__ = {
			offerHeader: 'test',
			offerDescription: 'test',
			cta1: 'test',
			cta2: 'test',
			isPreview: true,
		};

		// act
		renderSSR();

		// assert
		expect(ReactDOM.hydrate).toHaveBeenCalled();
	});

	it('calls ReactDOM.hydrate with preview data if isPreview is true', () => {
		window.__INITIAL__DATA__ = {
			offerHeader: 'test',
			offerDescription: 'test',
			cta1: 'test',
			cta2: 'test',
			isPreview: true,
		};

		renderSSR();

		expect(ReactDOM.hydrate).toHaveBeenCalledWith(
			<AbmPreview
				offerHeader={window.__INITIAL__DATA__.cta1}
				offerDescription={window.__INITIAL__DATA__.offerDescription}
				cta1={window.__INITIAL__DATA__.cta1}
				cta2={window.__INITIAL__DATA__.cta2}
			/>,
			null,
		);
	});

	it('calls ReactDOM.hydrate with details data if isPreview is false/missing', () => {
		window.__INITIAL__DATA__ = {
			header: 'test',
			image: {
				image: {
					file: {
						url: 'finally',
					},
				},
			},
			cta1: 'test',
			cta2: 'test',
			features: [ { featureLabel: 'ok' } ],
			legalFootnotes: 'test',
			offerDetails: 'test',
			ScotiaLogo: 'LogoScotiabankEn',
			footNotesTitle: 'view foot notes',
		};

		renderSSR();

		expect(ReactDOM.hydrate).toHaveBeenCalledWith(
			<IntlProvider locale={window.__INITIAL__DATA__.language} messages={messages[window.__INITIAL__DATA__.language]}>
				<AbmPaccDetails
					header={window.__INITIAL__DATA__.header}
					cta1={window.__INITIAL__DATA__.cta1}
					cta2={window.__INITIAL__DATA__.cta2}
					imageSrc={window.__INITIAL__DATA__.image.image.file.url}
					features={window.__INITIAL__DATA__.features}
					legalFootnotes={window.__INITIAL__DATA__.legalFootnotes}
					offerDetails={window.__INITIAL__DATA__.offerDetails}
					ScotiaLogo= {window.__INITIAL__DATA__.language === 'fr' ? LogoScotiabankFr : LogoScotiabankEn }
					footNotesTitle={<FormattedMessage id="offers.footNotesTitle"/>}
				/>
			</IntlProvider>,
			null,
		);
	});
});

describe('transformABMContent', () => {
	it('shouldnt change anything if no passed in items have a type of abm__preview', async () => {
		const input = [ { type: 'not abm__preview', content: 'test ' } ];

		const output = await transformABMContent({ fetch, logger }, input);

		expect(output).toEqual(input);
	});

	it('should change the content of items with type abm__preview', async () => {
		const input = [ { type: 'abm__preview',
			content: {
				isPreview: true, test: 'test',
			} } ];

		const output = await transformABMContent({ fetch, logger }, input);

		expect(output[0].content).toContain(`window.__INITIAL__DATA__ = {"isPreview":true,"test":"test"}`);
	});
});

describe('getSSRContent', () => {
	it('should assign given data to window.__INITIAL__DATA in the returned script tag', async () => {
		const input = {
			shouldBePresent: 'okay',
		};

		const output = await getSSRContent({ fetch, logger }, input);

		expect(output).toEqual(expect.stringContaining(`<script>window.__INITIAL__DATA__ = ${JSON.stringify(input)}</script>`));
	});

	it('should set input.isPreview to true if isPreview flag is set', async () => {
		const input = {
			shouldBePresent: 'okay',
		};

		await getSSRContent({ fetch, logger }, input, true);

		expect(input.isPreview).toEqual(true);
	});

	it('should encode image in production if image is present in data', async () => {
		const nodeEnv = config.nodeEnv;

		utils.promisify.mockReturnValue(() => [ 'something' ]);
		fetch.mockReturnValue({
			buffer: () => 'something',
		});

		config.nodeEnv = 'production';
		const input = {
			shouldBePresent: 'okay',
			image: {
				image: {
					file: {
						url: '//images.ctfassets.net/4szkx38resvm/2GorRk3SlamNSE4fxCTpHD/4c482abf2fd5af6138360e5864d03a22/Alpha.png',
						contentType: 'someType',
					},
				},
			},
		};
		const encodedInput = 'c29tZXRoaW5n';

		await getSSRContent({ fetch, logger }, input);

		expect(input.image.image.file.url).toEqual(`data:${input.image.image.file.contentType};base64,${encodedInput}`);
		config.nodeEnv = nodeEnv;
	});

	it('should not mutate input at all if input.image is falsy', async () => {
		const nodeEnv = config.nodeEnv;
		utils.promisify.mockReturnValue(() => [ 'something' ]);
		fetch.mockReturnValue({
			buffer: () => 'something',
		});

		config.nodeEnv = 'production';
		const input = {
			shouldBePresent: 'okay',
			image: false,
		};

		await getSSRContent({ fetch, logger }, input);

		expect(input.image).toEqual(false);
		config.nodeEnv = nodeEnv;
	});
});

describe('encodeImage', () => {
	beforeEach(() => {
		fetch.mockClear();
	});
	it('should append https if URL starts with // and call fetch', async () => {
		fetch.mockReturnValue({
			buffer: () => 'something',
		});
		const input = '//something.com';
		await encodeImage({ fetch, logger }, input);
		expect(fetch).toHaveBeenCalledWith(`https:${input}`, expect.anything());
	});
});

it('calls ReactDOM.hydrate with ODP details data', () => {
	window.__INITIAL__DATA__ = {
		offerHeader: 'odp header',
		headerDescription: ' odp header description',
		image: {
			file: {
				url: 'odpImage',
			},
		},
		cta1: 'odpcta1',
		cta2: 'todpcta2est',
		payPlanCta1: 'payPlanCta1',
		payPlanCta2: 'payPlanCta2',
		payPlanHeader: 'payPlanHeader',
		features: [
			{ payPlanDescription: 'payPlanDescription1',
				payPlanLabel: 'payPlanLabel1',
				payPlanValue: 'payPlanValue1',
				productSheet: {
					'name': 'Bank Account AEM url',
					'url': '/content/experience-fragments/scotiabank/ca/en/conduct/creditcardproductcode/creditcardsubproductcode/master.html',
				},
				appropriateness: '<div></div>',
			},
			{ payPlanDescription: 'payPlanDescription2', payPlanLabel: 'payPlanLabel2', payPlanValue: 'payPlanValue2' },
		],
		offerDetails: 'odp offer details',
		ScotiaLogo: 'LogoScotiabankEn',
		langauge: 'en',
		type: 'abm__odp-details',
		acknowledgement: {
			'name': 'Acklowledgement Waiver',
			'description': 'Based upon the key benefits and risks you have been informed of, you acknowledge that your selection is appropriate for you given your financial needs, circumstances, and goals',
		},
	};

	renderSSR();

	expect(ReactDOM.hydrate).toHaveBeenCalledWith(
		<IntlProvider locale={window.__INITIAL__DATA__.language} messages={messages[window.__INITIAL__DATA__.language]}>
			<AbmOdpDetails
				offerHeader={window.__INITIAL__DATA__.offerHeader}
				headerDescription={window.__INITIAL__DATA__.headerDescription}
				offerDetails={window.__INITIAL__DATA__.offerDetails}
				imageSrc={window.__INITIAL__DATA__.image.file.url}
				cta1={window.__INITIAL__DATA__.cta1}
				cta2={window.__INITIAL__DATA__.cta2}
				payPlanCta1={window.__INITIAL__DATA__.payPlanCta1}
				payPlanCta2={window.__INITIAL__DATA__.payPlanCta2}
				payPlanHeader={window.__INITIAL__DATA__.payPlanHeader}
				features={window.__INITIAL__DATA__.features}
				ScotiaLogo= {window.__INITIAL__DATA__.language === 'fr' ? LogoScotiabankFr : LogoScotiabankEn }
				acknowledgement= {window.__INITIAL__DATA__.acknowledgement}
				selectedText={window.__INITIAL__DATA__.language === 'fr' ? 'sélectionné' : 'Selected'}
				unselectedText={window.__INITIAL__DATA__.language === 'fr' ? 'sélectionnez le plan' : 'Select plan'}
			/>
		</IntlProvider>,
		null,
	);
});
