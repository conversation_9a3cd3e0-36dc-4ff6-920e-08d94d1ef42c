import React from 'react';
import ReactDOM from 'react-dom';
import { AbmPaccDetails, AbmPreview, AbmOdpDetails } from 'pigeon-pigeon-web-renderer';
import { IntlProvider, addLocaleData, FormattedMessage } from 'react-intl';
import LogoScotiabankEn from 'canvas-core-react/lib/LogoScotiabankEn';
import LogoScotiabankFr from 'canvas-core-react/lib/LogoScotiabankFr';
import enMessages from '../../messages/en';
import frMessages from '../../messages/fr';
import esMessages from '../../messages/es';

import en from 'react-intl/locale-data/en';
import fr from 'react-intl/locale-data/fr';
import es from 'react-intl/locale-data/es';

addLocaleData([ ...en, ...fr, ...es ]);

const messages = {
	en: enMessages,
	fr: frMessages,
	es: esMessages,
};

const renderSSR = () => {
	if (window.__INITIAL__DATA__) {
		const initialData = window.__INITIAL__DATA__;
		ReactDOM.hydrate(
			initialData.isPreview ? (
				<AbmPreview
					offerHeader={initialData.offerHeader}
					offerDescription={initialData.offerDescription}
					cta1={initialData.cta1}
					cta2={initialData.cta2}
				/>
			) : (
				<IntlProvider locale={initialData.language} messages={messages[initialData.language]}>
					{ initialData.type === 'abm__odp-details'
						? <AbmOdpDetails
							offerHeader={initialData.offerHeader}
							headerDescription={initialData.headerDescription}
							offerDetails={initialData.offerDetails}
							offerFootNotes={initialData.offerFootNotes}
							imageSrc={initialData.image.file.url}
							cta1={initialData.cta1}
							cta2={initialData.cta2}
							payPlanCta1={initialData.payPlanCta1}
							payPlanCta2={initialData.payPlanCta2}
							payPlanHeader={initialData.payPlanHeader}
							payPlanUltimateAccountNotes={initialData.payPlanUltimateAccountNotes}
							payPlanFootNotes={initialData.payPlanFootNotes}
							features={initialData.features}
							ScotiaLogo= {initialData.language === 'fr' ? LogoScotiabankFr : LogoScotiabankEn }
							acknowledgement= {initialData.acknowledgement}
							selectedText={initialData.language === 'fr' ? 'Sélectionné' : 'Selected'}
							unselectedText={initialData.language === 'fr' ? 'Sélectionnez le plan' : 'Select plan'}
						/>
						: <AbmPaccDetails
							header={initialData.header}
							cta1={initialData.cta1}
							cta2={initialData.cta2}
							imageSrc={initialData.image.image.file.url}
							features={initialData.features}
							legalFootnotes={initialData.legalFootnotes}
							offerDetails={initialData.offerDetails}
							ScotiaLogo= {initialData.language === 'fr' ? LogoScotiabankFr : LogoScotiabankEn }
							footNotesTitle={<FormattedMessage id="offers.footNotesTitle"/>}
							appropriateness= {initialData.appropriateness}
							acknowledgement= {initialData.acknowledgement}
						/>
					}
				</IntlProvider>
			),
			document.getElementById('root'),
		);
	}
};

renderSSR();

export { renderSSR };
