// eslint-disable-next-line no-unused-vars
import * as setup from '../setupTests';

describe('index.jsx', () => {
	it('renders', () => {
		expect(process.env.PCF_ENV).toBe('IST');
		expect(process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY).toBe('TEST');
		expect(process.env.CDP_SECRET_PASSPORT_PUBLIC_KEY).toBe('TEST');
		expect(process.env.PASSPORT_PUBLIC_KEY).toBe('LOCAL_TEST');
		expect(process.env.CDP_SECRET_CARD_PEPPER).toBe('SCOTCH BONNET');
		expect(process.env.CDP_SECRET_SSO_PUBLIC_KEY).toBe('TEST');
		expect(process.env.CDP_SECRET_SSO_PRIVATE_KEY).toBe('TEST');
		expect(process.env.VALID_RETURN_DOMAIN).toBe('.domain');
		expect(process.env.PASSPORT_SSO_ONYX_CLIENT_ID).toBe('TEST');
		expect(process.env.CDP_SECRET_SSO_ONYX_PUBLIC_KEY).toBe('TEST');
		expect(process.env.CDP_SECRET_SSO_ONYX_PRIVATE_KEY).toBe('TEST');
		expect(process.env.FEDERATION_SESSION_SECRET).toBe('TEST');
		expect(process.env.CLIENT_SESSION_TIMEOUT).toBe('3600000');
		expect(process.env.REDIS_TTL).toBe('43100');
	});
});
