const request = require('supertest');
const redisMock = require('redis-mock');

const providerHeimdall = require('nrlw-express-heimdall/dist/fed-initializer');

jest.mock('node-fetch', () => {
	const nodeFetch = jest.requireActual('node-fetch');
	const fetch = require('fetch-mock-jest').sandbox();
	Object.assign(fetch.config, { fetch: nodeFetch });
	return fetch;
});
jest.mock('const-common/lib/server/middleware', () => ({
	__esModule: true,
	csrfToken: jest.fn(() => (req, res, next) => next()),
}));
const fetch = require('node-fetch');
process.env.CDP_SECRET_CARD_PEPPER = 'NWM0N2Q5ZWRmNjg0ZThmNTVmYjAxNzliNDI2ZWJlY2Q5YTY4NzY2NjIyYTA1ZGQ0MGNiMWZmMDFlYTBiOTIyYQ==';
process.env.HTTPS_PROXY = 'http://webproxy.bns:8080';
const { createServer } = require('../server');
const pcfEnv = 'IST';
let nodeEnv = 'production';
const frontEndURL = 'http://localhost:8090';
const clsNamespace = 'pigeon-web-cls';
const pigeonURL = 'https://pigeon.apps.bns';
const marvelURL = 'https://marvel.apps.bns';
const passportExchangeOpaqueTokenURL = 'https://passport.apps.bns';
const tokenPath = 'auth.token';
const tokenClaimsPath = 'auth.claims';
const publicKey = '';
const clientId = 'abc123';
const ignoredLogRoutes = '["/someroute"]';
const ssoClientId = '57de5eb9-f40f-4ad3-ac21-dd99a11234ce';
const ssoPublicKey = process.env.CDP_SECRET_SSO_PUBLIC_KEY;
const ssoPrivateKey = process.env.CDP_SECRET_SSO_PRIVATE_KEY;
const jwksURI = 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs';
const redisClient = redisMock.createClient();

const mockMiddleware = () => (req, res, next) => next();
const [ authorize, authenticate, sso, onyxSso, helmet ] = Array(4).fill(mockMiddleware);
const createCertHandler = () => (req, res, next) => res();
const jwksClient = jest.fn();
const publicKeyToJWK = jest.fn().mockReturnValue('');
const getServiceToken = jest.fn().mockResolvedValue('sample.service.token');
const logger = {
	info: jest.fn(),
	error: jest.fn(),
};
const rateLimitConfigs = {
	window: 1200000,
	client: { campaigns: 5 },
	overall: { campaigns: 50, 'rendered-campaigns': 40 },
};
const launchDarklyService = {
	isFeatureEnabled: jest.fn(),
};

const contentSecurityPolicy = jest.fn();

describe('Server', () => {
	beforeAll(() => {
		providerHeimdall.default = jest.fn();
	});

	test('should initialize server', () => {
		const server = createServer({
			getServiceToken,
			authenticateCampaigns: authenticate,
			authenticateRenderedCampaigns: authenticate,
			authorize,
			createCertHandler,
			fetch,
			helmet,
			jwksURI,
			jwksClient,
			logger,
			launchDarklyService,
			pcfEnv,
			nodeEnv,
			frontEndURL,
			clsNamespace,
			pigeonURL,
			marvelURL,
			passportExchangeOpaqueTokenURL,
			redisClient,
			sso,
			onyxSso,
			ssoClientId,
			ssoPublicKey,
			ssoPrivateKey,
			tokenPath,
			tokenClaimsPath,
			publicKeyToJWK,
			publicKey,
			clientId,
			ignoredLogRoutes,
			rateLimitConfigs,
			contentSecurityPolicy,
		});
		expect(server).toBeDefined();
	});

	test('should initialize server', () => {
		nodeEnv = 'development';
		const server = createServer({
			getServiceToken,
			authenticateCampaigns: authenticate,
			authenticateRenderedCampaigns: authenticate,
			authorize,
			createCertHandler,
			fetch,
			helmet,
			jwksURI,
			jwksClient,
			logger,
			launchDarklyService,
			pcfEnv,
			nodeEnv,
			frontEndURL,
			clsNamespace,
			pigeonURL,
			marvelURL,
			passportExchangeOpaqueTokenURL,
			redisClient,
			sso,
			onyxSso,
			ssoClientId,
			ssoPublicKey,
			ssoPrivateKey,
			tokenPath,
			tokenClaimsPath,
			publicKeyToJWK,
			publicKey,
			clientId,
			ignoredLogRoutes,
			rateLimitConfigs,
			contentSecurityPolicy,
		});
		expect(server).toBeDefined();
	});

	test('should call health development', async () => {
		nodeEnv = 'development';
		const server = createServer({
			getServiceToken,
			authenticateCampaigns: authenticate,
			authenticateRenderedCampaigns: authenticate,
			authorize,
			createCertHandler,
			fetch,
			helmet,
			jwksURI,
			jwksClient,
			logger,
			launchDarklyService,
			pcfEnv,
			nodeEnv,
			frontEndURL,
			clsNamespace,
			pigeonURL,
			marvelURL,
			passportExchangeOpaqueTokenURL,
			redisClient,
			sso,
			onyxSso,
			ssoClientId,
			ssoPublicKey,
			ssoPrivateKey,
			tokenPath,
			tokenClaimsPath,
			publicKeyToJWK,
			publicKey,
			clientId,
			ignoredLogRoutes,
			rateLimitConfigs,
			contentSecurityPolicy,
		});
		expect(server).toBeDefined();
		await request(server).get('/health')
			.set('x-b3-traceid', '123')
			.set('x-b3-spanid', '123')
			.expect(200);
	});

	test('should call health on production', async () => {
		nodeEnv = 'production';
		let pcfEnv = 'prd';
		const server = createServer({
			getServiceToken,
			authenticateCampaigns: authenticate,
			authenticateRenderedCampaigns: authenticate,
			authorize,
			createCertHandler,
			fetch,
			helmet,
			jwksURI,
			jwksClient,
			logger,
			launchDarklyService,
			pcfEnv,
			nodeEnv,
			frontEndURL,
			clsNamespace,
			pigeonURL,
			marvelURL,
			passportExchangeOpaqueTokenURL,
			redisClient,
			sso,
			onyxSso,
			ssoClientId,
			ssoPublicKey,
			ssoPrivateKey,
			tokenPath,
			tokenClaimsPath,
			publicKeyToJWK,
			publicKey,
			clientId,
			ignoredLogRoutes,
			rateLimitConfigs,
			contentSecurityPolicy,
		});
		await request(server).get('/health')
			.expect(200);
	});
});
