@import '~pigeon-pigeon-web-renderer/dist/main.css';

@import 'variables.scss';
@import 'containers/errors/errors.scss';
@import 'components/offers/offerPreviews.scss';
@import 'containers/loading/loading.scss';

// React dark theme - https://confluence.agile.bns/display/CANVAS/Dark+Theme
$dark-bg-base: #121212;
$dark-bg-primary: #1e1e1e;
$dark-bg-secondary: #2c2c2c;
$dark-text-high: #e0e0e0;
$dark-text-medium: #a0a0a0;

// dark mode background for react, android
:root[data-dark-mode='true'] {
  background: $dark-bg-base;
}

// dark mode background for ios (currently only used by starburst)
:root[dark-mode-ios] {
  background: black;
}
html,
body,
body>div,
body>div>div {
  height: 100%;
}