// Polyfill Node with `Intl` that has data for all locales.
// See: https://formatjs.io/guides/runtime-environments/#server
const IntlPolyfill = require('intl');
Intl.NumberFormat = IntlPolyfill.NumberFormat;
Intl.DateTimeFormat = IntlPolyfill.DateTimeFormat;

const path = require('path');
const express = require('express');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const connectRedis = require('connect-redis');
const csurf = require('csurf');
const { omit } = require('lodash');
const Joi = require('@hapi/joi');
const webTrackId = require('./server/web-track-id');
const campaigns = require('./server/campaigns');
const renderedCampaigns = require('./server/rendered-campaigns');
const { PigeonApi: createPigeonApiClient } = require('./server/service-clients');
const { errorMiddleware } = require('./server/errors');
const { loggerMiddleware, reqStartTimeMiddleware } = require('nrlw-express-scribe');
const httpContext = require('express-http-context');
const { setTraceId, setSpanId, setPreferredEnvironment, contextMiddleware } = require('./httpContext');
const jwt = require('jsonwebtoken');
const { csrfToken } = require('const-common/lib/server/middleware');
const {
	setHeadersFromCookies,
	setCookiesMiddleware,
	setQueryParamsFromCookies,
	getTemplateCookies,
} = require('./server/utils/cookie');
const { campaignsTotalRateLimiter, campaignsClientRateLimiter, renderedCampaignsTotalRateLimiter } = require('./server/rate-limit/rate-limiters');
const rateLimitMiddleware = require('./server/rate-limit/rate-limit-middleware');
const helmet = require('helmet');
const fs = require('fs');
const { Region } = require('./server/utils/constants');
const { xssMiddleware } = require('./server/middlewares/xssMiddleware');
const { name: appName, version: appVersion } = require('../package.json');
const { allowedCookiesSchema } = require('../src/common/validation');

const createServer = ({
	getServiceToken,
	authenticateCampaigns,
	authenticateRenderedCampaigns,
	authorize,
	contentSecurityPolicy,
	createCertHandler,
	fetch,
	jwksURI,
	jwksClient,
	logger,
	launchDarklyService,
	pcfEnv,
	nodeEnv,
	frontEndURL,
	clsNamespace,
	pigeonURL,
	pigeonAtlasURL,
	hostingEnv,
	marvelURL,
	marvelAtlasURL,
	tokenPath,
	tokenClaimsPath,
	pem2jwk,
	publicKeyToJWK,
	publicKey,
	clientId,
	ignoredLogRoutes,
	redisClient,
	sso,
	onyxSso,
	ssoClientId,
	ssoPublicKey,
	ssoPrivateKey,
	validReturnDomain,
	rateLimitConfigs,
}) => {
	const server = express();
	server.use(contextMiddleware);
	const level = (req, res) => ((res.statusCode >= 400 && res.statusCode !== 422) ? 'warn' : 'info');
	// whitelist the routes from logging to save some space
	const ignoreLogging = (req, res) => (ignoredLogRoutes.includes(req.url));
	// setup server
	server.use(compression());
	server.use(
		helmet({
			contentSecurityPolicy,
			hsts: {
				maxAge: 31536000,
				includeSubDomains: true,
			},
		}),
	);
	server.use(cookieParser(ssoPrivateKey));
	server.use(reqStartTimeMiddleware());
	server.use(loggerMiddleware({
		logger,
		level,
		maskedReqHeaders: [ 'Authorization', 'cookie', 'csrf-token', 'x-customer-scotiacard' ],
		maskedResHeaders: [ 'set-cookie' ],
		getContext: (req, res) => ({ cardNumber: req.get('x-customer-scotiacard') }),
		ignoreLogging,
	}));
	server.use(xssMiddleware);
	// remove preferred-environment header in production
	const getHeaderKey = (key, obj) => Object.keys(obj).find((k) => k.toLowerCase() === key);
	server.use((req, _, next) => {
		const pcfEnvironment = pcfEnv.toLowerCase();
		if (pcfEnvironment === 'prd') {
			delete req.headers[getHeaderKey('preferred-environment', req.headers)];
		}
		next();
	});

	// setting the context for every request that goes through
	server.use((req, res, next) => {
		httpContext.ns.bindEmitter(req);
		httpContext.ns.bindEmitter(res);

		// Fetching the required headers from request
		const traceId = req.get('x-b3-traceid');
		const spanId = req.get('x-b3-spanid');
		const preferredEnvironment = req.get('preferred-environment');

		// Setting these required properties to the context globally and can be accessed using the getter fn of httpContext function
		if (traceId) setTraceId(traceId);
		if (spanId) setSpanId(spanId);
		const pcfEnvironment = pcfEnv.toLowerCase();
		if (pcfEnvironment !== 'prd') {
			setPreferredEnvironment(preferredEnvironment);
		}
		next();
	});

	server.use(express.json());

	const ssoCookieMiddleware = [
		csurf({ cookie: { httpOnly: true, secure: true, domain: validReturnDomain } }),
		csrfToken,
	];

	// a function that runs middleware on sso flag being true
	const runMiddlewareWhenSsoIsEnabled = middleware => async (req, res, next) => {
		const isOauth2SsoEnabled = await launchDarklyService.isFeatureEnabled('pigeon-web.features.oauth-sso', true);
		if (isOauth2SsoEnabled) {
			middleware(req, res, next);
		} else {
			next();
		}
	};

	const RedisStore = connectRedis(session);
	const redisStore = new RedisStore({ client: redisClient });
	server.use(/^(?!\/ccau).*$/, session({
		name: 'sessionID',
		store: redisStore,
		proxy: true,
		cookie: { secure: nodeEnv === 'production', maxAge: 24 * 3600 * 1000, domain: validReturnDomain },
		secret: ssoPrivateKey,
		resave: false,
		saveUninitialized: false,
		unset: 'destroy',
	}));

	// handle health
	server.get('/health', (req, res, next) => res.status(200).json({ status: 'UP', serviceId: appName, version: appVersion }));

	ssoCookieMiddleware.forEach(middlewareFunc => server.use(/^((?!\/v1\/rendered-campaigns).)*$/, runMiddlewareWhenSsoIsEnabled(middlewareFunc)));

	// jwks url
	server.get('/.well-known/jwks.json', (req, res) => {
		const pubKey = Buffer.from(publicKey, 'base64').toString();
		const jwks = publicKeyToJWK([ {
			id: clientId,
			pem: pubKey,
		} ]);
		res.status(200).json({ jwks });
	});

	// service clients used by route handlers
	const pigeonApi = createPigeonApiClient({ fetch, pigeonURL, pigeonAtlasURL, getServiceToken, launchDarklyService });
	// handle /v1/rendered-campaigns
	server.use('/v1/rendered-campaigns', renderedCampaigns.createRoutes({
		authenticate: authenticateRenderedCampaigns,
		authorize,
		getServiceToken,
		fetch,
		logger,
		launchDarklyService,
		pigeonApi,
		tokenPath,
		tokenClaimsPath,
		rateLimitMiddleware: rateLimitMiddleware(launchDarklyService, logger, omit(rateLimitConfigs, [ 'window' ]), false),
		renderedCampaignsTotalRateLimiter: renderedCampaignsTotalRateLimiter(rateLimitConfigs, logger),
	}));

	// convert headers into cookies
	server.use(/^\/(?!(v1|health)).*/, setCookiesMiddleware({ pcfEnv, logger }));

	// set cookies values to headers for API endpoints
	server.use(/^\/v1\/.*/, setHeadersFromCookies(launchDarklyService));

	// handle /v1/web-track-id
	server.use('/v1/web-track-id', webTrackId.createRoutes({
		authenticate: authenticateCampaigns,
		authorize,
		getServiceToken,
		fetch,
		launchDarklyService,
		jwksURI,
		jwksClient,
		jwt,
		logger,
		marvelURL,
		marvelAtlasURL,
		sso,
		tokenPath,
		tokenClaimsPath,
	}));

	// handle /v1/campaigns
	server.use('/auth/certs', runMiddlewareWhenSsoIsEnabled(createCertHandler({ ssoClientId, ssoPublicKey, pem2jwk })));
	server.use('/auth', runMiddlewareWhenSsoIsEnabled(sso));
	server.use('/campaigns', runMiddlewareWhenSsoIsEnabled(sso));
	server.use('/v1/campaigns', setQueryParamsFromCookies, campaigns.createRoutes({
		authenticate: authenticateCampaigns,
		authorize,
		getServiceToken,
		fetch,
		launchDarklyService,
		logger,
		pigeonApi,
		pigeonURL,
		pigeonAtlasURL,
		sso,
		jwksURI,
		jwksClient,
		jwt,
		tokenPath,
		tokenClaimsPath,
		rateLimitMiddleware: rateLimitMiddleware(launchDarklyService, logger, omit(rateLimitConfigs, [ 'window' ]), true),
		campaignsTotalRateLimiter: campaignsTotalRateLimiter(rateLimitConfigs, logger),
		campaignsClientRateLimiter: campaignsClientRateLimiter(rateLimitConfigs, logger),
		region: Region.CANADA,
	}));

	// Onyx Authentication routes
	server.use('/ccau/auth', onyxSso);
	server.use('/ccau/campaigns', onyxSso);
	server.use('/ccau/v1/campaigns',
		setQueryParamsFromCookies,
		campaigns.createRoutes({
			authenticate: authenticateCampaigns,
			authorize,
			getServiceToken,
			fetch,
			launchDarklyService,
			logger,
			pigeonApi,
			pigeonURL,
			pigeonAtlasURL,
			sso: onyxSso,
			jwksURI,
			jwksClient,
			jwt,
			tokenPath,
			tokenClaimsPath,
			rateLimitMiddleware: rateLimitMiddleware(launchDarklyService, logger, omit(rateLimitConfigs, [ 'window' ]), true),
			campaignsTotalRateLimiter: campaignsTotalRateLimiter(rateLimitConfigs, logger),
			campaignsClientRateLimiter: campaignsClientRateLimiter(rateLimitConfigs, logger),
			region: Region.CCAU,
		}));

	// static assets
	if (nodeEnv === 'development') {
		const { createProxyMiddleware, responseInterceptor } = require('http-proxy-middleware');
		server.use(/^\/(?!(v1|health)).*/, createProxyMiddleware({
			target: frontEndURL,
			selfHandleResponse: true,
			onProxyRes: responseInterceptor(async (responseBuffer, proxyRes, req, res) => {
				const contentType = proxyRes.headers['content-type'];
				if (contentType && contentType.includes('text/html')) {
					const allowedCookies = getTemplateCookies(req);
					const { value, error } = Joi.validate(allowedCookies,
						allowedCookiesSchema);
					const headers = JSON.stringify(value);
					const response = responseBuffer.toString('utf8'); // convert buffer to string
					if (error) {
						return response;
					} else {
						return response.replace('window.headers = {};', `window.headers = '${headers}';`); // manipulate response and return the result
					}
				} else {
					return responseBuffer;
				}
			}),
		}));
	} else {
		server.use('/static', express.static(path.join(__dirname, '..', 'dist'), {
			maxAge: Infinity,
			immutable: true,
		}));

		server.use(/^\/(?!(api|health|assets)).*/, (req, res) => {
			// if a static asset fell through the /static middleware and ended up here we respond with 404 status on the index.html file
			if (req.originalUrl.includes('/static')) {
				res.status(404);
			}
			const allowedCookies = getTemplateCookies(req);
			const { value, error } = Joi.validate(allowedCookies,
				allowedCookiesSchema);
			const headers = JSON.stringify(value);
			logger.info({ message: `Allowed cookies to be sent to client as headers ${headers}`, value });
			if (error) {
				logger.error({ message: `Error validating the cookies ${JSON.stringify(error)}`, err: error.details });
				res.sendFile(path.join(__dirname, '..', 'dist', 'index.html'));
			} else {
				const page = fs.readFileSync(path.join(__dirname, '..', 'dist', 'index.html'), { encoding: 'utf-8' }).replace('window.headers = {};', `window.headers = '${headers}';`);
				res.send(page);
			}
		});
	}

	server.use(errorMiddleware({ logger }));

	return server;
};

module.exports = {
	createServer,
};
