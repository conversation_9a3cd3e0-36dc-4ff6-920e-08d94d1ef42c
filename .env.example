NODE_ENV=development
EPM=BFB6
HOSTING_ENV=PCF
PIGEON_API=https://pigeon-ist.apps.cloud.bns
PIGEON_API_ATLAS='https://pigeon-uat.nonp.atlas.bns'
PASSPORT_API=https://passport-oauth-ist.apps.cloud.bns
MARVEL_API=https://cdb-int-customer-ist.apps.stg.azr-cc-pcf.cloud.bns
MARVEL_API_ATLAS=https://cdb-int-customer-ist.nonp.atlas.bns
NODE_TLS_REJECT_UNAUTHORIZED="0"
API_TIMEOUT_THRESHOLD=10000
ANALYTICS_LIB_URL=//assets.adobedtm.com/27c34d6e7144/12980dea3830/launch-8ef2174a917f-staging.min.js
ANALYTICS_ENV=pigeon-ist
HTTPS_PROXY=http://webproxy.bns:8080
NO_PROXY='passport-oauth-ist.apps.cloud.bns'
CDP_SECRET_PASSPORT_PRIVATE_KEY=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
AUTH_S2S_CLIENTID='d25053e8-3a5e-42cc-bc57-8389df4401f1'
AUTH_S2S_CLAIM_EXPIRESIN='1h'
AUTH_S2S_CLAIM_NOTBEFORE='-10s'
AUTH_S2S_CLAIM_SCOPE='ca:baas:campaign-rules:read ca:baas:dispositions:write customer.customer.bdms.webTrackId.read'
AUTH_S2S_PRIVATEKEY_ALGO='RS256'
AUTH_S2S_TOKEN_PATH='auth.token'
AUTH_S2S_CLAIMS_PATH='auth.claims'
AUTH_ACCESS_TOKEN_URI='https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/token'
AUTH_OPAQUE_EXCHANGE_URI='https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/tokeninfo'
AUTH_S2S_JWKS_URI='https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/certs'
AUTH_S2S_JWKS_BACKUP='{"keys":[{"kty":"RSA","e":"AQAB","use":"sig","kid":"fKF4jR1cBHVbVUSk-0BdlNfzznPpOy7wQV6mg_EUICE","alg":"RS256","n":"lUhd8BGbRtIt86gAxXOrW1ZeMgXVkfAFRE8S7sqD31hNTxI4p6CXyVRjyefwbOpa-dQZ4JscSMh4G9yZI44UK_22oQ8jzRrqGhpy0GuM_phowdEbkOqz4dV6vOUWlVTu9G2q6rWvqJXdVjWDzdkHuTewRT5D6IGkyaGF9brC07F8XKaQ3-F7pWgn4YVLxnG9QOTRULmNk5nnMelpi8Z-RMdBCAgwYhHQotNV-Wu5b-ZBrfeyvfAOYO3EJXhI8vOlRi4oTXzC7OmQTyqxWlzY5-87om3OyKqC1BCUvUvu2p0H5b-ZpuxKcBOoQFJ51GEo2VL4g9xlo2EXPWVZ4lP0RQ"}]}'
CDP_SECRET_CARD_PEPPER='NWM0N2Q5ZWRmNjg0ZThmNTVmYjAxNzliNDI2ZWJlY2Q5YTY4NzY2NjIyYTA1ZGQ0MGNiMWZmMDFlYTBiOTIyYQ=='
LOG_NAME=pigeon-web-ist
LOG_OBFUSCATE=1
LOG_COLORIZE=0
LOG_PRETTY_PRINT=0
LOG_SIEM=0
LAUNCH_DARKLY_USER_ID='0f9deeb3-a978-4f47-abf6-71edd44a6764'
LAUNCH_DARKLY_CLIENT_SIDE_ID='5e74d53a00cad60764a4f12a'

# SSO
PASSPORT_SSO_CLIENT_ID='57de5eb9-f40f-4ad3-ac21-dd99a11234ce'
PASSPORT_AUTHORIZE_URI='https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/authorize'
REGISTERED_REDIRECT_URI='http://localhost:8080/auth/authorization'
VALID_RETURN_DOMAIN='localhost'
REDIS_URL='redis://localhost:6379'
CDP_SECRET_SSO_PUBLIC_KEY=
CDP_SECRET_SSO_PRIVATE_KEY=

# SSO Onyx
PASSPORT_SSO_ONYX_CLIENT_ID='3bca452e-d186-4af1-8f25-1df21b967c0e'
FEDERATION_SESSION_SECRET='1a426bb0-1660-11ec-9621-0242ac130002'
SERVICE_PASSPORT_REVOKE='https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/revoke'
SERVICE_PASSPORT_JWK='https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs'
CLIENT_SESSION_TIMEOUT=3600000
SERVICE_PASSPORT_TOKENINFO='https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/tokeninfo'

# content security policy
CSP_IMAGE_SRC='["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","data:"]'
CSP_FRAME_SRC='["https://*.demdex.net"]'
CSP_SCRIPT_SRC='["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'
CSP_CONNECT_SRC='["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","http://localhost:8090","ws://localhost:8090","wss://localhost:8090"]'

# SOLEndpoints
SOL_ENDPOINTS = '{"istred": "https://devredprodapp.scotiaonline.pp.scotiabank.com:4135/prodserv", "ist_green": "https://devgreenprodapp.scotiaonline.pp.scotiabank.com:4135/prodserv", "istblack": "https://devblackprodapp.scotiaonline.pp.scotiabank.com:4135/prodserv"}'
SCOTIAHOME_ENDPOINTS = '{"ist": "https://mtg.nonprod.scotiabank.com:9100/mrlc-ui/authentication"}'
SCRL_ENDPOINTS = '{"istred": "http://************:8553/SmallCompaniesOnboardC-proxy/rest/singleSignOn", "ist": "http://************:8553/SmallCompaniesOnboardC-proxy/rest/singleSignOn", "istgreen": "http://************:8553/SmallCompaniesOnboardC-proxy/rest/singleSignOn"}'

# rate limiting
RATE_LIMIT_CLIENT_CAMPAIGNS_MAX=30
RATE_LIMIT_OVERALL_CAMPAIGNS_MAX=500
RATE_LIMIT_OVERALL_RENDERED_CAMPAIGNS_MAX=400
RATE_LIMIT_CDP_TRUSTED_IP='***********/23'