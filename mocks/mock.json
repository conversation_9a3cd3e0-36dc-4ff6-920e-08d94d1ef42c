{"campaignList": [{"id": "accounts", "data": {"items": [{"id": "1", "name": "rule name 1", "type": "targetedCampaignPreview", "container": "offers-and-programs", "external_ref": {"message_id": "a"}, "content": {"title": "<PERSON>ck Offer 1", "description": "Standing Campaign Template 1 Mock offer", "ctaLink": {"linkText": "Go to offer", "linkAction": {"url": "pigeon-web"}}, "image": {"name": "CLI", "image": {"title": "CLI", "file": {"url": "//images.ctfassets.net/4szkx38resvm/4AzgIEb6H6qA8s0SeUqa6U/c74da9cba4d8f767cdf44d15de6ef9b5/Momentum_Visa_Infinite_ENG.png", "details": {"size": 63909, "image": {"width": 270, "height": 163}}, "fileName": "CLI.PNG", "contentType": "image/png"}}, "altText": "CLI"}}}, {"id": "2", "name": "rule name 2", "type": "targetedCampaignPreview", "container": "offers-and-programs", "external_ref": {"message_id": "b"}, "content": {"title": "<PERSON><PERSON> Offer 2", "description": "Standing Campaign Template 2 Mock offer", "ctaLink": {"linkText": "Go to offer", "linkAction": {"url": "pigeon-web"}}}}, {"id": "3", "type": "targetedCampaignPreview", "container": "offers-and-programs", "external_ref": {"message_id": "c"}, "content": {"title": "<PERSON><PERSON> Offer 3", "description": "Standing Campaign Template 3 Mock offer", "ctaLink": {"linkText": "Go to offer", "linkAction": {"url": "pigeon-web"}}}}, {"id": "4", "type": "targetedCampaignPreview", "container": "offers-and-programs", "external_ref": {"message_id": "d"}, "content": {"title": "<PERSON><PERSON> 4", "description": "Targeted Campaign Template 1 Mock offer", "ctaLink": {"linkText": "Go to offer", "linkAction": {"url": "pigeon-web"}}}}, {"id": "eySejHN8U8JR", "type": "targetedCampaignPreview", "container": "offers-and-programs", "external_ref": {"message_id": "D0O00O0OOOOSAML2"}, "content": {"title": "<PERSON><PERSON> 5", "description": "Targeted Campaign Template 2 Mock offer", "ctaLink": {"linkText": "Go to offer", "linkAction": {"url": "pigeon-web"}}}}]}, "notifications": []}], "campaignDetail": [{"data": {"id": "1", "name": "rule name", "type": "standingCampaignTemplate1Details", "external_ref": {"campaign_id": "E0110", "message_id": "a", "message_source": "KT"}, "content": {"name": "Mike-<PERSON>", "title": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "subtitle": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Tellus in metus vulputate eu.", "details": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Est pellentesque elit ullamcorper dignissim cras tincidunt lobortis feugiat. Diam maecenas sed enim ut. Done<PERSON> et odio pellentesque diam volutpat commodo sed. Tortor condimentum lacinia quis vel eros donec ac. Ullamcorper eget nulla facilisi etiam dignissim. Sit amet nisl suscipit adipiscing bibendum. Tristique et egestas quis ipsum suspendisse ultrices gravida dictum. Tristique nulla aliquet enim tortor at auctor. Tortor condimentum lacinia quis vel eros donec ac. Dui sapien eget mi proin sed libero enim sed. Bibendum at varius vel pharetra vel turpis nunc eget lorem.\n\nDui nunc mattis [**************](tel:**************) ut tellus elementum sagittis vitae. Pellentesque habitant morbi tristique senectus et netus. In ornare quam viverra orci sagittis eu volutpat odio facilisis. Rutrum quisque non tellus orci. Consectetur purus ut faucibus pulvinar. Turpis cursus in hac habitasse platea dictumst quisque. Ut venenatis tellus in metus vulputate eu. Accumsan tortor posuere ac ut consequat semper viverra nam libero. Netus et malesuada fames ac turpis egestas integer eget. Egestas quis ipsum suspendisse ultrices gravida dictum fusce ut placerat.\n\nA pellentesque sit amet porttitor eget dolor. Ipsum a arcu cursus vitae congue mauris. Adipiscing tristique risus nec feugiat in fermentum posuere urna. Eu lobortis elementum nibh tellus molestie nunc non blandit massa. Pellentesque pulvinar pellentesque habitant morbi tristique senectus. Lectus nulla at volutpat diam ut venenatis. Erat pellentesque adipiscing commodo elit at imperdiet dui accumsan. Ornare quam viverra orci sagittis. Sed nisi lacus sed viverra tellus in. Cum sociis natoque penatibus et. Facilisis leo vel fringilla est ullamcorper eget. Scelerisque in dictum non consectetur a erat nam at lectus. Amet mauris commodo quis imperdiet massa. Dictumst vestibulum rhoncus est pellentesque elit ullamcorper dignissim. Est velit egestas dui id ornare arcu odio. Vestibulum rhoncus est pellentesque elit ullamcorper dignissim cras tincidunt lobortis. Dignissim suspendisse in est ante in nibh. Donec massa sapien faucibus et molestie ac feugiat. Quam vulputate dignissim suspendisse in est ante in nibh mauris. Ultrices neque ornare aenean euismod elementum nisi quis eleifend.", "image": {"name": "Scotiabank Infinite Visa", "image": {"title": "Momentum Visa Infinite ENG", "file": {"url": "//images.ctfassets.net/4szkx38resvm/4AzgIEb6H6qA8s0SeUqa6U/c74da9cba4d8f767cdf44d15de6ef9b5/Momentum_Visa_Infinite_ENG.png", "details": {"size": 176584, "image": {"width": 1011, "height": 636}}, "fileName": "Momentum_Visa_Infinite_ENG.png", "contentType": "image/png"}}, "altText": "Some alt text"}, "ctaLink": {"name": "Activate Online", "linkText": "Activate Online", "linkAction": {"name": "Pigeon Web", "url": "https://www.scotiabank.com"}}, "signature": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\nSincerely\n\n__<PERSON>__\nFraud Analyst\nScotiabank<sup>™</sup>"}}}, {"data": {"id": "2", "name": "rule name 2", "type": "standingCampaignTemplate2Details", "external_ref": {"campaign_id": "E0110", "message_id": "b", "message_source": "KT"}, "content": {"name": "Mike-<PERSON>", "title": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "subtitle": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. At quis risus sed vulputate.", "image": {"name": "Sample Photo", "image": {"title": "Promo Image", "description": "An image of gift in a parachute in the sky.", "file": {"url": "//images.ctfassets.net/4szkx38resvm/GjBbfViRcAkyucUIqwKuG/77f9c73cb575ae2f6f555de21ec738d1/PROMO.svg", "details": {"size": 9319, "image": {"width": 163, "height": 136}}, "fileName": "PROMO.svg", "contentType": "image/svg+xml"}}, "altText": "Image of a balloon"}, "details": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Non curabitur gravida arcu ac tortor dignissim convallis aenean. Massa tincidunt dui ut ornare lectus. Dolor sed viverra ipsum nunc. Sit amet dictum sit amet justo donec. Lectus proin nibh nisl condimentum id venenatis. Non odio euismod lacinia at. Vulputate mi sit amet mauris commodo quis imperdiet. Consectetur libero id faucibus nisl. Imperdiet dui accumsan sit amet nulla facilisi morbi tempus. Nulla malesuada pellentesque elit eget. Neque egestas congue quisque egestas diam in.\n\n__Eget__ duis at tellus at urna condimentum mattis pellentesque id. Nibh sed pulvinar proin gravida. Tempus iaculis urna id volutpat lacus laoreet non curabitur. Pellentesque adipiscing commodo elit at imperdiet dui accumsan. Quis commodo odio aenean sed adipiscing diam. Iaculis urna id volutpat lacus laoreet non curabitur gravida arcu. Tincidunt eget nullam non nisi est sit amet facilisis. Mi eget mauris pharetra et ultrices neque ornare. Neque volutpat ac tincidunt vitae semper quis lectus nulla at. At volutpat diam ut venenatis tellus in metus vulputate eu. Nunc aliquet bibendum enim facilisis gravida neque convallis. Sed turpis tincidunt id aliquet risus. Amet consectetur adipiscing elit ut aliquam purus sit amet.\n\n## Mauris a diam maecenas sed enim.\n- Auctor neque vitae tempus quam. Pellentesque nec nam aliquam sem et.\n- Malesuada fames ac turpis egestas maecenas. Netus et malesuada fames ac turpis egestas. Cursus in hac habitasse platea.\n- Integer feugiat scelerisque varius morbi enim nunc faucibus a. Nunc consequat interdum varius sit.\n- [Tellus] rutrum tellus pellentesque eu tincidunt. Laoreet id donec ultrices tincidunt arcu non sodales neque. Eu volutpat odio facilisis mauris sit. Duis tristique sollicitudin nibh sit amet.\nProin fermentum leo vel orci porta non pulvinar neque laoreet.", "ctaLink": {"name": "ODP-Apr19-V5-Details-CTA", "linkText": "Accept now", "linkAction": {"name": "ODP-Apr19-V5-Details-CTA-URL", "url": "https://www.scotiabank.com/ca/en/personal.html"}, "accessibilityText": "Accept now / Accepter test"}, "cardAdditionalDetails": "hello there", "moreDetails": "## Other recommended accounts:\n\n### [Basic Banking Plan]()\n- Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n- Montes nascetur ridiculus mus mauris vitae ultricies. Nec tincidunt praesent semper feugiat. Enim nulla aliquet porttitor lacus luctus accumsan tortor. Elementum facilisis leo vel fringilla est ullamcorper.\n- [Duis at tellus] at urna condimentum mattis. Cras ornare arcu dui vivamus arcu felis. Fusce ut placerat orci nulla pellentesque dignissim enim sit. Nibh tortor id aliquet lectus. Rutrum quisque non tellus orci ac auctor augue.\n- Enim neque volutpat ac tincidunt vitae semper. Et netus et malesuada fames ac turpis egestas maecenas pharetra. Et netus et malesuada fames ac turpis egestas integer eget. Facilisi cras fermentum odio eu feugiat pretium nibh. Purus semper eget duis at tellus at urna condimentum mattis.\n\n---\n\n### [Scotia® One<sup>™</sup> Account]()\n- Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n- Montes nascetur ridiculus mus mauris vitae ultricies. Nec tincidunt praesent semper feugiat. Enim nulla aliquet porttitor lacus luctus accumsan tortor. Elementum facilisis leo vel fringilla est ullamcorper.\n- [Duis at tellus] at urna condimentum mattis. Cras ornare arcu dui vivamus arcu felis. Fusce ut placerat orci nulla pellentesque dignissim enim sit. Nibh tortor id aliquet lectus. Rutrum quisque non tellus orci ac auctor augue.\n- Enim neque volutpat ac tincidunt vitae semper. Et netus et malesuada fames ac turpis egestas maecenas pharetra. Et netus et malesuada fames ac turpis egestas integer eget. Facilisi cras fermentum odio eu feugiat pretium nibh. Purus semper eget duis at tellus at urna condimentum mattis.", "moreDetailsTip": {"name": "pr standing template tip2", "title": "pr standing template tip2", "description": "pr standing template tip2", "icon": {"title": "Estatement image PR", "description": "Estatement image PR", "file": {"url": "//images.ctfassets.net/4szkx38resvm/71q6zDDxPatRybfzcUPPO7/50145631f1ab65699cca29d5f0c8504e/Estatements_image.jpg", "details": {"size": 9689, "image": {"width": 210, "height": 240}}, "fileName": "Estatements image.jpg", "contentType": "image/jpeg"}}}, "footer": "® Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n®<sup>*</sup> Montes nascetur ridiculus mus mauris vitae ultricies. Nec tincidunt praesent semper feugiat. Enim nulla aliquet porttitor lacus luctus accumsan tortor.\n\\* Elementum facilisis leo vel fringilla est ullamcorper. Duis at tellus at urna condimentum mattis. Cras ornare arcu dui vivamus arcu felis. Fusce ut placerat orci nulla pellentesque dignissim enim sit. Nibh tortor id aliquet lectus. Rutrum quisque non tellus orci ac auctor augue.\nEnim neque volutpat ac tincidunt vitae semper. Et netus et malesuada fames ac turpis egestas maecenas pharetra. Et netus et malesuada fames ac turpis egestas integer eget. Facilisi cras fermentum odio eu feugiat pretium nibh. Purus semper eget duis at tellus at urna condimentum mattis."}}}, {"data": {"id": "3", "type": "standingCampaignTemplate3Details", "name": "rule name 3", "external_ref": {"campaign_id": "E0110", "message_id": "c", "message_source": "KT"}, "content": {"name": "Mike-<PERSON>", "title": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "subtitle": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Vivamus arcu felis bibendum ut tristique et egestas quis ipsum.", "details": "__Old Payee name__\nimperdiet nulla malesuada\n__Old account nunmber__\nimperdiet nulla malesuada\n\n---\n\n__New Payee name__\nimperdiet nulla malesuada\n__New account nunmber__\nimperdiet nulla malesuada\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Dolor purus non enim praesent. Euismod in pellentesque massa placerat duis. Imperdiet dui accumsan sit amet nulla facilisi morbi. Tincidunt id aliquet risus feugiat in ante metus. Nulla malesuada pellentesque elit eget. Cras ornare arcu dui vivamus arcu felis bibendum ut tristique. Nullam vehicula ipsum a arcu cursus vitae congue. Morbi tincidunt ornare massa eget egestas purus viverra accumsan in. Augue neque gravida in fermentum et. Laoreet non curabitur gravida arcu ac tortor. Leo a diam sollicitudin tempor id eu. Quis ipsum suspendisse ultrices gravida dictum fusce ut. Platea dictumst vestibulum rhoncus est pellentesque elit ullamcorper dignissim cras. Nibh tellus molestie nunc non blandit massa. Platea dictumst vestibulum rhoncus est pellentesque elit ullamcorper dignissim.", "ctaLink": {"name": "GIC-Renewal", "linkText": "Learn More", "linkAction": {"name": "GIC_Renewal", "url": "pigeon-web"}}, "badgeIcon": "loans"}}}, {"data": {"id": "4", "name": "rule name 4", "type": "targetedCampaignTemplate1Details", "external_ref": {"campaign_id": "E0110", "message_id": "d", "message_source": "KT"}, "content": {"name": "Mike-<PERSON>", "title": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "subtitle": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. At lectus urna duis convallis convallis tellus.", "details": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Congue mauris rhoncus aenean vel elit scelerisque mauris. Diam quam nulla porttitor massa id. Commodo odio aenean sed adipiscing diam donec adipiscing tristique. Urna neque viverra justo nec ultrices. Ut etiam sit amet nisl purus in mollis. Eget mauris pharetra et ultrices. Eu consequat ac felis donec et odio pellentesque diam volutpat. Aenean vel elit scelerisque mauris. Orci nulla pellentesque dignissim enim sit amet venenatis urna cursus. At risus viverra adipiscing at in tellus integer feugiat. Risus viverra adipiscing at in tellus integer feugiat scelerisque. Eget mauris pharetra et ultrices neque. Sed egestas egestas fringilla phasellus faucibus scelerisque eleifend. At volutpat diam ut venenatis tellus in metus vulputate eu. Cursus risus at ultrices mi tempus. Facilisi morbi tempus iaculis urna id volutpat.\n\nMagna fermentum iaculis eu non diam. Tellus rutrum tellus pellentesque eu tincidunt tortor. Quis vel eros donec ac. Adipiscing commodo elit at imperdiet. Ipsum dolor sit amet consectetur adipiscing elit duis. Mauris sit amet massa vitae tortor condimentum. Sed risus pretium quam vulputate dignissim. Scelerisque felis imperdiet proin fermentum leo vel. Est ullamcorper eget nulla facilisi etiam dignissim diam. Morbi quis commodo odio aenean sed adipiscing diam donec adipiscing. Integer quis auctor elit sed vulputate mi sit. Mattis ullamcorper velit sed ullamcorper morbi tincidunt ornare massa.\n\nTincidunt augue interdum velit euismod in pellentesque. Sit amet justo donec enim diam. Suscipit tellus mauris a diam. Fringilla urna porttitor rhoncus dolor. Morbi enim nunc faucibus a pellentesque. Faucibus in ornare quam viverra orci sagittis. Amet risus nullam eget felis eget. A cras semper auctor neque vitae tempus quam pellentesque nec. Amet commodo nulla facilisi nullam vehicula ipsum. Vitae auctor eu augue ut lectus arcu bibendum. Maecenas sed enim ut sem viverra.", "image": {"name": "Scotiabank Infinite Visa", "image": {"title": "Momentum Visa Infinite ENG", "file": {"url": "//images.ctfassets.net/4szkx38resvm/4AzgIEb6H6qA8s0SeUqa6U/c74da9cba4d8f767cdf44d15de6ef9b5/Momentum_Visa_Infinite_ENG.png", "details": {"size": 176584, "image": {"width": 1011, "height": 636}}, "fileName": "Momentum_Visa_Infinite_ENG.png", "contentType": "image/png"}}, "altText": "Some alt text"}, "ctaLink": {"name": "Accept Offer Test DO NOT CHANGE OR DELETE.", "linkText": "Accept offer", "linkAction": {"name": "Test Link DO NOT CHANGE OR DELETE", "url": "http://www.amazon.ca"}}, "footer": "® Registered trademarks of The Bank of Nova Scotia\n®<sup>*</sup> Registered trademark of SCENE IP LP, used under license.\n<sup>*</sup> The account features and fees described in this letter are accurate as of SOLUI_OTHER3_END.\n®<sup>^</sup> Registered trademark of Cineplex Entertainment, used under license.\n<sup>*^</sup> SCENE members can redeem 1,000 SCENE points to receive a free general admission movie ticket.\n<sup>^^</sup> SCENE members can redeem 1,000 SCENE points to receive $10.00 off their bill at these restaurants (excluding taxes, delivery charges, alcohol purchases, and gratuity). Please vist scene.ca/dining for full terms and conditions and a list of participating restaurants"}}}, {"data": {"id": "eySejHN8U8JR", "name": "rule name 5", "type": "targetedCampaignTemplate2Details", "external_ref": {"campaign_id": "E0110", "message_id": "D0O00O0OOOOSAML2", "message_source": "KT"}, "content": {"title": "SOLUI_NAME_END, you're pre-approved for a SCENE® *Visa* card. And there's no annual fee.", "cards": [{"name": "VISA SCENE PA Acq", "heading": "Turn your everyday purchases into FREE movies and more with the SCENE® *Visa* card.", "icon": "credit-card", "image": {"name": "SCENE Visa Card", "image": {"title": "SCENE Visa Card", "file": {"url": "//images.ctfassets.net/4szkx38resvm/6gNhKg0tkWKHXR3O0mHpcT/8c250d6431db859f70d269622ee8d1ea/Scotia_Scene_Eng_2017.jpg", "details": {"size": 19356, "image": {"width": 300, "height": 189}}, "fileName": "Scotia_Scene_Eng_2017.jpg", "contentType": "image/jpeg"}}, "altText": "SCENE Visa Card"}, "description": "- **1 SCENE point for every $1 you spend on everyday purchases**<sup>3</sup>\n- 5 SCENE points for every $1 you spend on purchases at participating Cineplex Entertainment<sup>®</sup>^ theatres and online at cineplex.com<sup>4</sup>.\n\nThat's on top of the points you're earning with your black SCENE membership card.\n\n### Credit Limit:\n$SOLUI_CREDIT_END\n\n### Bonus Points:\n__2,000 bonus SCENE points__ when you spend $500 on everyday purchases in the first three months – that's enough to redeem for 2 FREE movies<sup>5</sup>.\n\n### Accept by:\nSOLUI_DATE3_END\n\nTerms & Conditions\n\n*By selecting __\"Accept Card\"__ below you agree to continue with the processing of your pre-approved application.", "listItems": [{"heading": "No fees", "image": {"name": "PR- Money", "image": {"title": "<PERSON><PERSON> <PERSON> <PERSON>", "description": "<PERSON><PERSON> <PERSON> <PERSON>", "file": {"url": "//images.ctfassets.net/4szkx38resvm/6nv5cc3AU9GiYlbFjAD4Bw/e196b6fb624285f2c64e8faddd9e21ed/Loan-Vecors.png", "details": {"size": 17314, "image": {"width": 512, "height": 512}}, "fileName": "Loan-Vecors.png", "contentType": "image/png"}}, "altText": "Loan"}}, {"heading": "Earning<PERSON>", "image": {"name": "PA CC", "image": {"title": "PACC", "description": "PA Credit Card", "file": {"url": "//images.ctfassets.net/4szkx38resvm/1ogtsOrMEwOn8FsXlWt9DT/de3686fec23a2b83856bb6ed8caef170/AP.PNG", "details": {"size": 126273, "image": {"width": 336, "height": 236}}, "fileName": "AP.PNG", "contentType": "image/png"}}, "altText": "Pre-Approved Credit Card"}}]}, {"description": "The <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pair was superb last spring against the Bruins (63 percent expected goals for percentage), though it’s also fair to wonder what version of <PERSON> the Leafs are going to get after such a long layoff, as well an injury that can pop up again at any moment.\n\n<PERSON>, remember, sat out the final two games before the All-Star break and bye week with back spasms before returning when the official second half started in Detroit on Feb. 1.\n\nHe played the next 13 games, and played quite well, but then was unexpectedly forced out again when troubles with his back re-emerged. He hasn’t played since and will get only a two-game tuneup before the post-season.", "ctaLink": {"name": "Accept Offer Test DO NOT CHANGE OR DELETE.", "linkText": "Accept Scotiahome!!", "linkAction": {"name": "Something preapproved", "url": "scotiahome:verify.bns"}}}]}}}]}