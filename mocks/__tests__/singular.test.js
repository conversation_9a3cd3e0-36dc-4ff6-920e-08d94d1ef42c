import singular from '../singular';

describe('singular', () => {
	const mockSend = jest.fn();
	const mockReq = { url: '/campaignDetail?data.id=test&external_ref.message_id=test&singular=1' };
	const mockRes = { send: mockSend };
	const mockNext = jest.fn();

	it('singular with one item', () => {
		singular(mockReq, mockRes, mockNext);
		mockRes.send(JSON.stringify([ 'one' ]));
		expect(mockSend.mock.calls[0][0]).toBe(JSON.stringify('one'));
	});

	it('singular with no items', () => {
		singular(mockReq, mockRes, mockNext);
		mockRes.send(JSON.stringify([]));
		expect(mockSend.mock.calls[1][0]).toBe('{}')
	});

	it('send normal body without singular query', () => {
		mockReq.url.replace('&singular=1', '');
		singular(mockReq, mockRes, mockNext);
		mockRes.send(JSON.stringify([ 0, 1, 2 ]));
		expect(mockSend.mock.calls[2][0]).toBe(JSON.stringify([ 0, 1, 2 ]));
	});
});

