'use strict'
const url = require('url');

/**
 * Middleware for json-server: when supplied with `singular=1` in the request url
 * this middleware will return the value of the first item in an array whose length is 1
 * e.g. when json-server gets a request for /items with only one result it would normally return `[item0]`...
 * ... when a query parameter of `singular=1` is supplied this would instead return `item0`
 *
 * Inspired by https://github.com/typicode/json-server/issues/541
 */
module.exports = (req, res, next) => {
	const _send = res.send;
	res.send = function(body) {
		if (url.parse(req.url, true).query['singular']) {
			try {
				const json = JSON.parse(body);
				if (Array.isArray(json)) {
					if (json.length === 1) {
						return _send.call(this, JSON.stringify(json[0]));
					} else if (json.length === 0) {
						return _send.call(this, '{}', 404);
					}
				}
			} catch (e) {}
		}
		return _send.call(this, body);
	}
	next();
};
