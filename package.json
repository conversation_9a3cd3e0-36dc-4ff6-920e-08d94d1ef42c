{"name": "pigeon-pigeon-web", "version": "1.27.0", "description": "A web app to display offers and information supplied by the pigeon content service", "repository": "https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web/browse", "main": "app.jsx", "private": false, "engines": {"node": "20.15.1", "npm": "^10.7.0"}, "scripts": {"lint": "eslint ./src/", "lint:fix": "eslint ./src/ --fix", "build": "webpack --config webpack.prod.js", "analyze": "webpack --config webpack.analyze.js", "start": "npm run setEnvironmentVariables && npm run start:server", "start:dev": "concurrently \"npm run start:dev:frontend\" \"npm run start:dev:server\"", "start:dev:frontend": "webpack-dev-server --config webpack.dev.js", "start:dev:server": "nodemon src/start.js --watch src", "start:mock": "concurrently \"npm run start:dev:frontend\" \"npm run start:mock:server\" \"npm run start:mock:api\"", "start:mock:api": "json-server --no-cors --watch  mocks/mock.json --routes mocks/routes.json --middlewares mocks/singular.js", "start:mock:server": "MOCK_PIGEON=true nodemon src/start.js --watch src", "start:server": "node src/start.js", "test": "PIGEON_API=https://pigeon-ist.apps.cloud.bns SCOTIAHOME_URL=https://mtg.uat.scotiabank.com:9100/mrlc-ui/authentication SOL_PRD='https://app.scotiaonline.scotiabank.com/prodserv' jest", "test:coverage": "npm run test -- --coverage", "postinstall": "if [ \"$NODE_ENV\" == \"production\" ]; then echo \"offline=true\noptional=false\" > .npmrc; fi", "check": "npm run lint && npm run test:coverage && npm run build", "setEnvironmentVariables": "node setEnvironmentVariables.js", "dev:pwr-copy": "rm -rf node_modules/pigeon-pigeon-web-renderer && rsync -ax --exclude node_modules --exclude .git ../pigeon-web-renderer/ node_modules/pigeon-pigeon-web-renderer", "bumpVersion": "AF_VERSION=$(npm view $npm_package_name version); LATEST_VERSION=$(semver $AF_VERSION $npm_package_version | tail -1); if [ $LATEST_VERSION != $npm_package_version ] || [ $AF_VERSION = $npm_package_version ]; then npm version $AF_VERSION --force --no-git-tag-version --allow-same-version && npm version patch --force --no-git-tag-version; fi", "release": "standard-version"}, "keywords": ["pigeon", "pigeon-web", "react"], "author": "Scotiabank", "license": "ISC", "dependencies": {"@adobe/redux-saga-promise": "^1.1.1", "@hapi/joi": "^15.1.1", "async-lock": "^1.2.4", "axios": "^1.8.4", "canvas-core-react": "^13.11.0", "canvas-core-tokens": "^3.5.2", "classnames": "2.2.6", "compression": "1.7.4", "connect-redis": "^4.0.2", "const-common": "^7.2.0", "cookie": "0.4.0", "cookie-parser": "1.4.4", "dot-prop": "^5.2.0", "dotenv": "8.0.0", "express": "4.21.2", "express-http-context": "^1.2.4", "express-http-proxy": "1.5.1", "express-rate-limit": "^6.7.0", "express-session": "^1.17.1", "helmet": "^4.6.0", "https-proxy-agent": "^5.0.0", "intl": "1.2.5", "jsonwebtoken": "9.0.2", "jwks-rsa": "^2.0.4", "launchdarkly-node-server-sdk": "^7.0.3", "launchdarkly-react-client-sdk": "^2.22.2", "lru-cache": "^6.0.0", "node-fetch": "2.6.7", "node-polyfill-webpack-plugin": "^3.0.0", "nrlw-express-heimdall": "^5.60.0", "nrlw-express-scribe": "^1.10.1", "pem-jwk": "^2.0.0", "pigeon-cerberus": "4.0.1", "pigeon-pigeon-pack": "0.1.14", "pigeon-pigeon-web-renderer": "^4.0.8", "pino": "5.12.6", "pino-noir": "2.2.1", "prop-types": "15.7.2", "qs": "^6.9.7", "query-string": "6.8.1", "ramda": "^0.27.2", "range_check": "^2.0.4", "react": "^16.8.6", "react-device-detect": "^2.2.2", "react-dom": "^16.8.6", "react-intl": "2.9.0", "react-redux": "7.1.0", "react-router": "5.1.2", "react-router-dom": "5.1.2", "react-router-redux": "4.0.8", "redis": "^3.0.2", "redux": "4.0.1", "redux-actions": "2.6.5", "redux-saga": "1.0.5", "reselect": "4.0.0", "styled-components": "^5.3.1", "uuid": "3.3.3"}, "devDependencies": {"@babel/core": "7.8.0", "@babel/plugin-proposal-class-properties": "7.5.0", "@babel/plugin-proposal-decorators": "7.4.4", "@babel/plugin-syntax-dynamic-import": "7.2.0", "@babel/plugin-transform-runtime": "7.5.0", "@babel/preset-env": "7.5.0", "@babel/preset-react": "7.0.0", "@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@scotia/eslint-config-scotiabank": "1.0.1", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "12.1.5", "@testing-library/user-event": "^14.5.2", "acorn": "7.0.0", "axios-mock-adapter": "1.17.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.2", "babel-jest": "^27.5.1", "babel-loader": "8.0.6", "babel-plugin-transform-define": "1.3.1", "clean-webpack-plugin": "3.0.0", "concurrently": "^8.2.1", "conventional-changelog-eslint": "^3.0.9", "css-loader": "6.9.0", "enzyme": "3.10.0", "enzyme-adapter-react-16": "1.14.0", "enzyme-to-json": "3.3.5", "eslint": "6.8.0", "eslint-plugin-import": "2.18.0", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.14.2", "fetch-mock-jest": "1.3.0", "file-loader": "6.2.0", "html-webpack-plugin": "^5.0.0", "http-proxy-middleware": "^2.0.7", "husky": "4.3.0", "identity-obj-proxy": "3.0.0", "jest": "27.0.0", "jest-fetch-mock": "2.1.2", "json-server": "0.17.3", "mini-css-extract-plugin": "2.7.6", "node-mocks-http": "1.9.0", "nodemon": "^2.0.20", "pino-pretty": "^4.1.0", "redis-mock": "^0.56.3", "redux-saga-test-plan": "4.0.0-beta.3", "redux-test-utils": "0.3.0", "sass": "^1.66.1", "sass-loader": "13.3.2", "standard-version": "^9.2.0", "style-loader": "0.23.1", "supertest": "4.0.2", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.3.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.13.2", "webpack-merge": "4.2.1"}, "overrides": {"axios": "^1.8.4", "jsonwebtoken": "9.0.2", "cross-spawn": "7.0.6", "semver": "7.7.1", "brace-expansion": "2.0.2", "elliptic": "6.6.1", "semver-regex": "^3.1.4"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "npm run check", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "properties": {"artifactory_contextUrl": "https://af.cds.bns:8081/artifactory", "artifactory_projectRepoKey": "local-npm-bns", "artifactory_user": "", "artifactory_password": "", "artifactory_npm_repo": "virtual-npm-bns", "pcf_app_url": "", "cdp_vault_name": "PIGEON", "cdp_environment_name": "", "cdp_region_name": "", "cdp_vault_clientId": "", "cdp_vault_clientSecret": "", "pipeline_plugin_version": "1.2.1", "nodejs_tool_name": "nodejs-16.16.0", "sonar_host_url": "https://sonar.agile.bns/", "sonar_sources": "src", "sonar_exclusions": "src/static/js,src/analytics", "sonar_tests": "", "sonar_report_paths": "coverage/lcov.info", "sonar_language": "js"}, "settings": {"rootProject.name": "pigeon-web"}}