module.exports = {
	automock: false,
	verbose: false,
	testEnvironment: 'jest-environment-jsdom',
	moduleNameMapper: {
		'\\.(css|scss|jpg|png|ico)$': 'identity-obj-proxy',
		// Axios version 1.x, which has switched from CommonJS to ECMAScript modules (ESM)
		// The line below is to configure <PERSON><PERSON> to use the CommonJS version of Axios
		'^axios$': 'axios/dist/node/axios.cjs',
	},
	collectCoverageFrom: [
		'src/**/*.{js,jsx}',
	],
	moduleDirectories: [
		'node_modules',
		'src',
	],
	setupFilesAfterEnv: [
		'<rootDir>/src/setupTests.js',
	],
	snapshotSerializers: [
		'enzyme-to-json/serializer',
	],
	globals: {
		window: true,
	},
	coverageThreshold: {
		global: {
			branches: 80,
			functions: 80,
			lines: 80,
			statements: 80,
		},
	},
	coveragePathIgnorePatterns: [
		'server/rate-limit/rate-limiters.js',
		'server.js',
	],
};
