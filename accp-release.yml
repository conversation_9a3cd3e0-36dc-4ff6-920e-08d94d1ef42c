build:
  template: 'npm:generic'
  substitutions:
    _VERSION_STRATEGY: DEV_TEAM
    _ARTIFACTORY_PATH: 'pigeon-pigeon-web'
    _SONAR_SCAN_STRATEGY: 'DEFAULT'
    _FORTIFY_SCAN_STRATEGY: 'RUN_NOT_FAIL'
    _FORTIFY_EXCLUDE: 'dist node_modules src/**/*.test.js src/**/__tests__/**/*'
    _BLACKDUCK_SCAN_STRATEGY: 'TRIGGER_ONLY'
    _BLACKDUCK_URL: 'https://blackduck.appsec.bns'
    _BLACKDUCK_PROJECT_VERSION: 'BRANCH-release'
    _NODEJS_VERSION: 20.15.1
  steps:
    - name: 'af.cds.bns:5002/accp/tdp-node:1.0.0'
      workdir: '/workspace'
      env:
        - "_IS_PUBLISHING_ARTIFACT=${_IS_PUBLISHING_ARTIFACT}"
        - "ARTIFACTORY_USERNAME_READONLY=${_ACCP_SECRET_ENV_ARTIFACTORY_USERNAME_READONLY}"
        - "ARTIFACTORY_PASSWORD_READONLY=${_ACCP_SECRET_ENV_ARTIFACTORY_PASSWORD_READONLY}"
      args:
        - sh
        - -c
        - -e
        - |
          '
          set -eo pipefail;
          AUTH=`echo -n "${ARTIFACTORY_USERNAME_READONLY}:${ARTIFACTORY_PASSWORD_READONLY}" | base64`;
          echo "
          //af.cds.bns/artifactory/api/npm/virtual-npm-bns/:_auth=$AUTH
          always-auth=true
          lockfile-version=2
          email=<EMAIL>
          registry=https://af.cds.bns/artifactory/api/npm/virtual-npm-bns
          loglevel=verbose
          cafile=/etc/ssl/certs/root.cer
          " > /root/.npmrc;
          export PATH=/root/node/bin:$PATH;
          npm run check;
          [ ${_IS_PUBLISHING_ARTIFACT} == 0 ] || npm run bumpVersion;
          '