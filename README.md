# Pigeon-Web

A React web application to render offers for a given user from Pigeon.

# Environment File

In order to run the project you will need to create an environment file at the
root of the project (`/.env`). There is an `.env.example` file that you can copy
and rename to `.env`).

### Installing

`npm install`

### Running the project

`npm run start`

### Mock Service

If running the project locally run `npm run start:mock`. This will spin up a
mock server that will mock Pigeon's campaigns for offers and programs. To see
those offers and programs go to
http://localhost:8080/campaigns?page=accounts&platform=ios

To modify the mocked responses, you can change the JSON file in
`mocks/mock.json`. Make sure you re-run the server after making any changes.

### Running tests

`npm run test` or if you want to see coverage `npm run test:coverage`

### Running linter

`npm run lint`

### Viewing campaigns targeted for you

The Pigeon API requires certain query parameters in order to display offers and
thereby they are also required by Pigeon-Web. These are `platform`, `limit`, and
`page`.

A typical url for listing standing campaigns looks like
http://localhost:8080/campaigns?page=activities&platform=ios&limit=3

Alternatively the url for listing targeted campaigns looks like this
http://localhost:8080/campaigns?page=accounts&platform=ios&limit=3

Without an authorization you will be told by Pigeon-Web that you are
unauthorized to view the page. To authenticate, obtain an opaque token with
supported offers and pass that Pigeon-Web in the request header. It should look
something like `Authorization: Bearer {opaqueToken...}`

### Pigeon Web vs Pigeon Web Renderer

In many components of Pigeon Web, you would see a lot of imports from
[pigeon-web-renderer](https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-web-renderer/browse).
Pigeon Web Renderer is a UI library that provides UI components for marketing
purposes (Cards, CTA buttons, badges). PWR used to be a part of Pigeon Web, but
now is a project of its own for reusability purposes.

### Updating Pigeon Web Rendere

- View all packages of pigeon-web-renderer
  `npm view pigeon-pigeon-web-renderer versions`
- Install specfic exact version of pigeon-web-renderer
  `npm i pigeon-pigeon-web-renderer@<version> --save --save-exact`
