---
  applications:
    - name: pigeon-web-prd
      memory: 1024mb
      instances: 3
      buildpacks:
        - nodejs_buildpack-v1_8_27
      stack: cflinuxfs4
      command: npm run start
      services:
        - pigeon-cache
      routes:
        - route: pigeon-web.apps.cloud.bns
        - route: pigeon-web-prd.apps.prd.azr-use2-pcf.cloud.bns
        - route: dms-web.scotiabank.com
      health-check-type: http
      health-check-http-endpoint: /health
      env:
        PCF_ENV: PRD
        EPM: BFB6
        NODE_ENV: production
        SERVER_HOST: 0.0.0.0
        SERVER_PORT: 8080
        SERVER_GRACEFUL_TIMEOUT: 10000
        HOSTING_ENV: PCF
        PIGEON_API_ATLAS: https://pigeon.atlas.bns
        PIGEON_API: https://pigeon-prd.apps.prd.azr-use2-pcf.cloud.bns
        SOL_DEFAULT_ENVIRONMENT: prd
        NODE_TLS_REJECT_UNAUTHORIZED: 0
        MARVEL_API: https://cdb-int-customer-prd.apps.cloud.bns
        MARVEL_API_ATLAS: https://cdb-int-customer-prd.atlas.bns
        PASSPORT_API: https://passport-oauth.apps.cloud.bns
        API_TIMEOUT_THRESHOLD: 10000
        ANALYTICS_LIB_URL: //dmtags.scotiabank.com/launch/pigeon/27c34d6e7144/12980dea3830/launch-2cabc81e03db.min.js
        ANALYTICS_ENV: pigeon-prod
        HTTPS_PROXY: http://webproxy.bns:8080
        NO_PROXY: passport-oauth.apps.cloud.bns

        # Service to service authentication & sso
        AUTH_S2S_CLIENTID: 9d5ee90f-4595-4df6-948b-0ccb5a23cc57
        PASSPORT_SSO_CLIENT_ID: 57de5eb9-f40f-4ad3-ac21-dd99a11234ce
        AUTH_S2S_CLAIM_EXPIRESIN: 1h
        AUTH_S2S_CLAIM_NOTBEFORE: -10s
        AUTH_S2S_CLAIM_SCOPE: ca:baas:campaigns:read ca:baas:campaign-rules:read ca:baas:dispositions:write customer.customer.bdms.webTrackId.read
        AUTH_S2S_PRIVATEKEY_ALGO: RS256
        AUTH_S2S_TOKEN_PATH: 'auth.token'
        AUTH_S2S_CLAIMS_PATH: 'auth.claims'
        AUTH_ACCESS_TOKEN_URI: 'https://passport-oauth.apps.cloud.bns/oauth2/v1/token'
        AUTH_OPAQUE_EXCHANGE_URI: 'https://passport-oauth.apps.cloud.bns/oauth2/v1/tokeninfo'
        AUTH_S2S_JWKS_URI: 'https://passport-oauth.apps.cloud.bns/oauth2/v1/certs'
        # Backup public JWKS used for fallback mechanism: https://jira.agile.bns/browse/PIGEON-5538
        # This backup JWKS could be fetched at: https://passport-oauth.apps.cloud.bns/oauth2/v1/certs
        AUTH_S2S_JWKS_BACKUP: '{"keys":[{"kty":"RSA","e":"AQAB","use":"sig","kid":"kPUjvSaOnFYECjJc2eu1ro4lgoeEyrIokBmMh_pbesU","alg":"RS256","n":"yZnO21gBHy2wkhJvZxuNbAPwfnsMaIn-Y_XsuMAZfYt7swYGmXcU_3dNPZDKfTPOGzWvNlGHYeAmYUXZPpaQy1eKm69RHMt4ULOln-Kvq3hRjxRK2EYqk_VYtbOpIQDW-w-anG5M1pOSnY-3VB7_2VRolR_XVRbAP2IZgSk2udZLFfwEIUtAAyyPZbLPdMwsh0qqG_2kZK5jbwSVo-hpbzixcqbLKFO9b9gmpCAmiNuyeypjVysXtgPnf9fsbOsDnTNptDcwLysgZRbw_MS7EVxMvrizyI7NEy92WCL3xWOSdtVD2b8BCkLrCSVvwig2Dnt09mevaEoFgDBzqtsuKQ"}]}'
        PASSPORT_AUTHORIZE_URI: 'https://passport-oauth.apps.cloud.bns/oauth2/v1/authorize'
        REGISTERED_REDIRECT_URI: 'https://dms-web.scotiabank.com/auth/authorization'
        PASSPORT_PUBLIC_KEY: 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFreDUwaUhMbzM2YlYrSklJSTFzRAo0VGgvbG5jRzRIclBmWGFIaTR5U1pLMTh6TmIwclV5R1p6b1d4RkpPTHVWT0lLMFh1SS81dTFsMnFVUTFtdDNEClJzK0dCWlZhdUM1UTd5by96U1ZZQlBDa1VPZFE1aFdXWC9TYTJxYlg3SGVUVzQ0VjJTM3p4U0FLWEU4UUU0Y28KeVNSMlk0bHFwZTN2RE1sYUlyd3dFK2JTKzlJWEptOXROaTlDSTFWU1ozQWJETFVZRHdnMXFzd0tZWjJMazVDaQp5VUg5blF3c3dsb0JVTVh3azI4bFBRK0t4WVJDNlI4alZuOUdDL1lRQ2l1R2FydnNNeGlML2VWcWcxMTBNRmxpCkUrRitZaTN2NStIMW1vNG93dnI1TUl6R3I4OW83RDEweE1JRi9YUm9WNEl3Wi9rdnRxN3NFWlJUeldLeFBucTQKMHdJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t'

        # Onyx SSO
        PASSPORT_SSO_ONYX_CLIENT_ID: 3bca452e-d186-4af1-8f25-1df21b967c0e
        FEDERATION_SESSION_SECRET: 1a426bb0-1660-11ec-9621-0242ac130002
        ONYX_REGISTERED_REDIRECT_URI: https://dms-web.scotiabank.com/ccau/auth/authorization
        SERVICE_PASSPORT_REVOKE: 'https://passport-oauth.apps.cloud.bns/oauth2/v1/revoke'
        CLIENT_SESSION_TIMEOUT: 3600000

        # Launch Darkly
        LAUNCH_DARKLY_USER_ID: '0f9deeb3-a978-4f47-abf6-71edd44a6764'
        LAUNCH_DARKLY_CLIENT_SIDE_ID: '5e74d51800cad60764a4f124'

        # content security policy
        CSP_IMAGE_SRC: '["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","data:"]'
        CSP_FRAME_SRC: '["https://*.demdex.net"]'
        CSP_SCRIPT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'
        CSP_CONNECT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'

        # logging options
        LOG_NAME: pigeon-web-prd
        LOG_OBFUSCATE: 1
        LOG_COLORIZE: 0
        LOG_PRETTY_PRINT: 0
        LOG_SIEM: 1
        IGNORED_ROUTES_FOR_LOGGING: '["/health"]'

        # image caching for ABM expiry time
        IMAGE_CACHE_EXPIRY_TIME: 900000

        DEPLOY_REGION: USE2

        # SOLEndpoints
        SOL_ENDPOINTS: '{"prd": "https://app.scotiaonline.scotiabank.com/prodserv"}'

        # FulfillableTargetEndpoints
        SCOTIAHOME_ENDPOINTS: '{"prd": "https://www.mtg.scotiabank.com/mrlc-ui/authentication"}'

        SCRL_ENDPOINTS: '{"prd": "https://www.appssc.scotiabank.com/SmallCompaniesOnboardC-proxy/rest/singleSignOn"}'

        RATE_LIMIT_CLIENT_CAMPAIGNS_MAX: 30
        RATE_LIMIT_OVERALL_CAMPAIGNS_MAX: 500
        RATE_LIMIT_OVERALL_RENDERED_CAMPAIGNS_MAX: 400
        RATE_LIMIT_CDP_TRUSTED_IP: '**********/23'