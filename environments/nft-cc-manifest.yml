---
  applications:
    - name: pigeon-web-nft
      memory: 1024mb
      instances: 3
      buildpacks:
        - nodejs_buildpack-v1_8_27
      stack: cflinuxfs4
      command: npm run start
      services:
        - pigeon-cache
      routes:
        - route: pigeon-web-nft.apps.cloud.bns
        - route: pigeon-web-nft.apps.stg.azr-cc-pcf.cloud.bns
      health-check-type: http
      health-check-http-endpoint: /health
      env:
        PCF_ENV: NFT
        EPM: BFB6
        NODE_ENV: production
        SERVER_HOST: 0.0.0.0
        SERVER_PORT: 8080
        SERVER_GRACEFUL_TIMEOUT: 10000
        HOSTING_ENV: PCF
        PIGEON_API_ATLAS: https://pigeon-nft.nonp.atlas.bns
        PIGEON_API: https://pigeon-nft.apps.stg.azr-cc-pcf.cloud.bns
        SOL_DEFAULT_ENVIRONMENT: prd
        NODE_TLS_REJECT_UNAUTHORIZED: 0
        MARVEL_API: https://cdb-int-customer-nft.apps.stg.azr-cc-pcf.cloud.bns
        MARVEL_API_ATLAS: https://cdb-int-customer-nft.nonp.atlas.bns
        PASSPORT_API: https://passport-oauth-nft.apps.stg.azr-cc-pcf.cloud.bns
        SCOTIAHOME_URL: https://www.mtg.nft.scotiabank.com/mrlc-ui/authentication
        API_TIMEOUT_THRESHOLD: 10000
        ANALYTICS_LIB_URL: //assets.adobedtm.com/27c34d6e7144/12980dea3830/launch-8ef2174a917f-staging.min.js
        ANALYTICS_ENV: pigeon-nft
        HTTPS_PROXY: http://pp-webproxy.bns:8080
        NO_PROXY: passport-oauth-nft.apps.cloud.bns

        # Service to service authentication & sso
        AUTH_S2S_CLIENTID: 9d5ee90f-4595-4df6-948b-0ccb5a23cc57
        PASSPORT_SSO_CLIENT_ID: 57de5eb9-f40f-4ad3-ac21-dd99a11234ce
        AUTH_S2S_CLAIM_EXPIRESIN: 1h
        AUTH_S2S_CLAIM_NOTBEFORE: -10s
        AUTH_S2S_CLAIM_SCOPE: ca:baas:campaigns:read ca:baas:campaign-rules:read ca:baas:dispositions:write customer.customer.bdms.webTrackId.read
        AUTH_S2S_PRIVATEKEY_ALGO: RS256
        AUTH_S2S_TOKEN_PATH: 'auth.token'
        AUTH_S2S_CLAIMS_PATH: 'auth.claims'
        AUTH_ACCESS_TOKEN_URI: 'https://passport-oauth-nft.apps.cloud.bns/oauth2/v1/token'
        AUTH_OPAQUE_EXCHANGE_URI: 'https://passport-oauth-nft.apps.cloud.bns/oauth2/v1/tokeninfo'
        AUTH_S2S_JWKS_URI: 'https://passport-oauth-nft.apps.cloud.bns/oauth2/v1/certs'
        # Backup public JWKS used for fallback mechanism: https://jira.agile.bns/browse/PIGEON-5538
         # This backup JWKS could be fetched at: https://passport-oauth-nft.apps.cloud.bns/oauth2/v1/certs
        AUTH_S2S_JWKS_BACKUP: '{"keys":[{"kty":"RSA","e":"AQAB","use":"sig","kid":"4gePOEKUb4MplPieffrwcctRPUWIFF5iLt5OzWg5274","alg":"RS256","n":"lxF7PxnYgY80BpHXGNKU5iCgBqOa58BDcLAR3VvziG9B1ObnGlunpqUfmpFoKWaB_JZxckrUoQFS7-kJa61jPv1QY2wLDXYKgHvuX6_I8tXXV3PmqHHFT3jKG7vACrDh-TM3auwcuOKL6-nGyw2PawC6B8Hmd7KUnDF2b59N_DpW1_f11iVMOyK0q4Wsu6Lo_ERKxJFb6JqhckyV3S3mnn9YavZt8LlFBhCyN7V-z40KIGgq1S1Is4QSGH9Lyz74P7NIedOA5Cclw-AhmYt1Oh2rdrvc-iFpbzPQwI4iBeOJFqRulnF2hXxhLVOU3gQft5u7H-c5Cpmq87JNDqBSaQ"}]}'
        PASSPORT_AUTHORIZE_URI: 'https://passport-oauth-nft.apps.cloud.bns/oauth2/v1/authorize'
        REGISTERED_REDIRECT_URI: 'https://pigeon-web-nft.apps.stg.azr-cc-pcf.cloud.bns/auth/authorization'
        PASSPORT_PUBLIC_KEY: 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3em14WDhxK2c1WitxVC9PWEVWVwpHa2pwWXc1cE0zOFlQR3JsUUVFR1NlMTVycnVScVkrSGJhSUF3WThmclJoVmliVURKM3pod0xxNHcrdXBETk5yCmxoU3V5clJ3SXhXNWtIMTRUMzlCbUtDN0pJZmdrSHdHZ2owNTZYNkVFanJjbjk2UVJpTUJ0MUQ0eXdLS2ZSeTAKYUZiS01RR2JtQk1pZnVreUljc3JwSkl1eU1WSHB6blNnWXJYZmRGSDlOYzcvZmRhUjRISGw3Vk1XQmdQSk93VApxSTVvbVoycjh2N2RXcWVuUFRqUmlGN2FXajFmeGJXVDU0SkhRbmF2ZWVLVDNJY2gxSjVVTnNlWG5RNCtmME9YCndLbHh5cHllSVhpalJ1dkR2QWt5V3dnbkx6Z3E2SVFmbUJXQUYxSFBPOEFDVERuVHZPVm1wODg5MWpIR2JxTVYKdFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t'
        VALID_RETURN_DOMAIN: '.cloud.bns'

        # Onyx SSO
        PASSPORT_SSO_ONYX_CLIENT_ID: 3bca452e-d186-4af1-8f25-1df21b967c0e
        FEDERATION_SESSION_SECRET: 1a426bb0-1660-11ec-9621-0242ac130002
        ONYX_REGISTERED_REDIRECT_URI: https://pigeon-web-nft.apps.stg.azr-cc-pcf.cloud.bns/ccau/auth/authorization
        SERVICE_PASSPORT_REVOKE: 'https://passport-oauth-nft.apps.cloud.bns/oauth2/v1/revoke'
        CLIENT_SESSION_TIMEOUT: 3600000

        # Launch Darkly
        LAUNCH_DARKLY_USER_ID: '0f9deeb3-a978-4f47-abf6-71edd44a6764'
        LAUNCH_DARKLY_CLIENT_SIDE_ID: '5ea866475340080a593fb32c'

        # content security policy
        CSP_IMAGE_SRC: '["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","data:"]'
        CSP_FRAME_SRC: '["https://*.demdex.net"]'
        CSP_SCRIPT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'
        CSP_CONNECT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'

        # logging options
        LOG_NAME: pigeon-web-nft
        LOG_OBFUSCATE: 1
        LOG_COLORIZE: 0
        LOG_PRETTY_PRINT: 0
        LOG_SIEM: 1
        IGNORED_ROUTES_FOR_LOGGING: '["/health"]'

        # image caching for ABM expiry time
        IMAGE_CACHE_EXPIRY_TIME: 900000

        DEPLOY_REGION: CC

        # SOLEndpoints
        SOL_ENDPOINTS: '{}'

        # FulfillableTargetEndpoints
        SCOTIAHOME_ENDPOINTS: '{"nft": "https://www.mtg.nft.scotiabank.com/mrlc-ui/authentication"}'

        SCRL_ENDPOINTS: '{}'

        # rate limiting
        RATE_LIMIT_CLIENT_CAMPAIGNS_MAX: 30
        RATE_LIMIT_OVERALL_CAMPAIGNS_MAX: 500
        RATE_LIMIT_OVERALL_RENDERED_CAMPAIGNS_MAX: 400
        RATE_LIMIT_CDP_TRUSTED_IP: '***********/23'
