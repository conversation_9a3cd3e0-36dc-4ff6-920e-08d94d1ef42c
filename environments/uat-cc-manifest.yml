---
  applications:
    - name: pigeon-web-uat
      memory: 1024mb
      instances: 2
      buildpacks:
        - nodejs_buildpack-v1_8_27
      stack: cflinuxfs4
      command: npm run start
      services:
        - pigeon-cache
      routes:
        - route: pigeon-web-uat.apps.cloud.bns
        - route: pigeon-web-uat.apps.stg.azr-cc-pcf.cloud.bns
        - route: dms-web-u.scointnet.net
      health-check-type: http
      health-check-http-endpoint: /health
      env:
        PCF_ENV: UAT
        EPM: BFB6
        NODE_ENV: production
        SERVER_HOST: 0.0.0.0
        SERVER_PORT: 8080
        SERVER_GRACEFUL_TIMEOUT: 10000
        HOSTING_ENV: PCF
        PIGEON_API_ATLAS: https://pigeon-uat.nonp.atlas.bns
        PIGEON_API: https://pigeon-uat.apps.stg.azr-cc-pcf.cloud.bns
        SOL_DEFAULT_ENVIRONMENT: uatgold
        NODE_TLS_REJECT_UNAUTHORIZED: 0
        MARVEL_API: https://cdb-int-customer-uat.apps.stg.azr-cc-pcf.cloud.bns
        MARVEL_API_ATLAS: https://cdb-int-customer-uat.nonp.atlas.bns
        PASSPORT_API: https://passport-oauth-uat.apps.stg.azr-cc-pcf.cloud.bns
        API_TIMEOUT_THRESHOLD: 10000
        ANALYTICS_LIB_URL: //assets.adobedtm.com/27c34d6e7144/12980dea3830/launch-8ef2174a917f-staging.min.js
        ANALYTICS_ENV: pigeon-uat
        HTTPS_PROXY: http://pp-webproxy.bns:8080
        NO_PROXY: passport-oauth-uat.apps.cloud.bns

        # Service to service authentication & sso
        AUTH_S2S_CLIENTID: 9d5ee90f-4595-4df6-948b-0ccb5a23cc57
        PASSPORT_SSO_CLIENT_ID: 57de5eb9-f40f-4ad3-ac21-dd99a11234ce
        AUTH_S2S_CLAIM_EXPIRESIN: 1h
        AUTH_S2S_CLAIM_NOTBEFORE: -10s
        AUTH_S2S_CLAIM_SCOPE: ca:baas:campaigns:read ca:baas:campaign-rules:read ca:baas:dispositions:write customer.customer.bdms.webTrackId.read
        AUTH_S2S_PRIVATEKEY_ALGO: RS256
        AUTH_S2S_TOKEN_PATH: 'auth.token'
        AUTH_S2S_CLAIMS_PATH: 'auth.claims'
        AUTH_ACCESS_TOKEN_URI: 'https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/token'
        AUTH_OPAQUE_EXCHANGE_URI: 'https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/tokeninfo'
        AUTH_S2S_JWKS_URI: 'https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/certs'
        # Backup public JWKS used for fallback mechanism: https://jira.agile.bns/browse/PIGEON-5538
        # This backup JWKS could be fetched at: https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/certs
        AUTH_S2S_JWKS_BACKUP: '{"keys":[{"kty":"RSA","e":"AQAB","use":"sig","kid":"fKF4jR1cBHVbVUSk-0BdlNfzznPpOy7wQV6mg_EUICE","alg":"RS256","n":"lUhd8BGbRtIt86gAxXOrW1ZeMgXVkfAFRE8S7sqD31hNTxI4p6CXyVRjyefwbOpa-dQZ4JscSMh4G9yZI44UK_22oQ8jzRrqGhpy0GuM_phowdEbkOqz4dV6vOUWlVTu9G2q6rWvqJXdVjWDzdkHuTewRT5D6IGkyaGF9brC07F8XKaQ3-F7pWgn4YVLxnG9QOTRULmNk5nnMelpi8Z-RMdBCAgwYhHQotNV-Wu5b-ZBrfeyvfAOYO3EJXhI8vOlRi4oTXzC7OmQTyqxWlzY5-87om3OyKqC1BCUvUvu2p0H5b-ZpuxKcBOoQFJ51GEo2VL4g9xlo2EXPWVZ4lP0RQ"}]}'
        PASSPORT_AUTHORIZE_URI: 'https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/authorize'
        REGISTERED_REDIRECT_URI: 'https://dms-web-u.scointnet.net/auth/authorization'
        PASSPORT_PUBLIC_KEY: 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFzODlVTSt5czJVbXFCeW5WbHlsZwpBWFA0WHZHb09FZGpiMmNsS2l6dkRCWkg3U0JucHc3Qjl4dlczai9ISUVlSXBBRUxQZE1OY3pBOXQ5ZlBZaldkCjVkdXRhSjF5VXRUMW9iWTR6ZTBrdERkNFY2THBWMDFRTU42K3lGeHgxQXFTR2piekZJVVVGOWR1M2c0OGZidU0KdTZtNXBwOXRPTU5GVFljREE0Y2d6VDYrSkljSXV1NzlGNmYxc3JxamFlc1dNWnFRTnlBc21GN3dENGpROGJLMgo5QmFCNExUNG12RS95WTFYTE1ZTVM1MGlMbFlkSUM1d2pvV25raTROTFhoUStmYkZKYTVtV1Rxazk5T1RVZ0oyCjZzQUNhVjhVZGFjLzFidkpubVVvUjZyUnRueFJlQWx0R0ppMWZld0RIcUllb3ZadWpkcVkwWHlvajloMUdzakcKTndJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t'

        # Onyx SSO
        PASSPORT_SSO_ONYX_CLIENT_ID: 3bca452e-d186-4af1-8f25-1df21b967c0e
        FEDERATION_SESSION_SECRET: 1a426bb0-1660-11ec-9621-0242ac130002
        ONYX_REGISTERED_REDIRECT_URI: 'https://dms-web-u.scointnet.net/ccau/auth/authorization'
        SERVICE_PASSPORT_REVOKE: 'https://passport-oauth-uat.apps.cloud.bns/oauth2/v1/revoke'
        CLIENT_SESSION_TIMEOUT: 3600000

        # Launch Darkly
        LAUNCH_DARKLY_USER_ID: '0f9deeb3-a978-4f47-abf6-71edd44a6764'
        LAUNCH_DARKLY_CLIENT_SIDE_ID: '5e74d59f00cad60764a4f139'

        # content security policy
        CSP_IMAGE_SRC: '["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","data:"]'
        CSP_FRAME_SRC: '["https://*.demdex.net"]'
        CSP_SCRIPT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'
        CSP_CONNECT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'

        # logging options
        LOG_NAME: pigeon-web-uat
        LOG_OBFUSCATE: 1
        LOG_COLORIZE: 0
        LOG_PRETTY_PRINT: 0
        LOG_SIEM: 1
        IGNORED_ROUTES_FOR_LOGGING: '["/health"]'

        # temporary until proper secret is put into the Key Vault
        CDP_SECRET_CARD_PEPPER: 'NWM0N2Q5ZWRmNjg0ZThmNTVmYjAxNzliNDI2ZWJlY2Q5YTY4NzY2NjIyYTA1ZGQ0MGNiMWZmMDFlYTBiOTIyYQ=='

        # image caching for ABM expiry time
        IMAGE_CACHE_EXPIRY_TIME: 900000

        DEPLOY_REGION: CC

        # SOLEndpoints
        SOL_ENDPOINTS: '{"uatred": "https://uc13-red-uat-exp.sol.bns/prodserv", "uatgreen": "https://ue43-green-uat-exp.sol.bns/prodserv", "uatblack": "https://ue43-black-uat-exp.sol.bns/prodserv", "uatpink": "https://pink.uat.exp.sol.bns/prodserv", "uatgold": "https://app.uat.scotiaonline.scotiabank.com/prodserv"}'

        # FulfillableTargetEndpoints
        SCOTIAHOME_ENDPOINTS: '{"uat": "https://mtg.uat.scotiabank.com:9100/mrlc-ui/authentication"}'

        SCRL_ENDPOINTS: '{"uatgreen": "https://*************:8551/SmallCompaniesOnboardC-proxy/rest/singleSignOn", "uatred": "https://**************:8551/SmallCompaniesOnboardC-proxy/rest/singleSignOn", "uat": "https://*************:8551/SmallCompaniesOnboardC-proxy/rest/singleSignOn"}'

        # rate limiting
        RATE_LIMIT_CLIENT_CAMPAIGNS_MAX: 30
        RATE_LIMIT_OVERALL_CAMPAIGNS_MAX: 500
        RATE_LIMIT_OVERALL_RENDERED_CAMPAIGNS_MAX: 400
        RATE_LIMIT_CDP_TRUSTED_IP: '***********/23'
