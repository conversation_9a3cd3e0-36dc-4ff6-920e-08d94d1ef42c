---
  applications:
    - name: pigeon-web-ist
      memory: 1024mb
      instances: 1
      buildpacks:
        - nodejs_buildpack-v1_8_27
      stack: cflinuxfs4
      command: npm run start
      services:
        - pigeon-cache
      routes:
        - route: pigeon-web-ist.apps.cloud.bns
        - route: pigeon-web-ist.apps.stg.azr-use2-pcf.cloud.bns
      health-check-type: http
      health-check-http-endpoint: /health
      env:
        PCF_ENV: IST
        EPM: BFB6
        NODE_ENV: production
        SERVER_HOST: 0.0.0.0
        SERVER_PORT: 8080
        SERVER_GRACEFUL_TIMEOUT: 10000
        HOSTING_ENV: PCF
        PIGEON_API_ATLAS: https://pigeon-ist.nonp.atlas.bns
        PIGEON_API: https://pigeon-ist.apps.stg.azr-cc-pcf.cloud.bns
        SOL_DEFAULT_ENVIRONMENT: istgreen
        NODE_TLS_REJECT_UNAUTHORIZED: 0
        MARVEL_API: https://cdb-int-customer-ist.apps.stg.azr-use2-pcf.cloud.bns
        MARVEL_API_ATLAS: https://cdb-int-customer-ist.nonp.atlas.bns
        PASSPORT_API: https://passport-oauth-ist.apps.stg.azr-use2-pcf.cloud.bns
        API_TIMEOUT_THRESHOLD: 10000
        ANALYTICS_LIB_URL: //assets.adobedtm.com/27c34d6e7144/12980dea3830/launch-8ef2174a917f-staging.min.js
        ANALYTICS_ENV: pigeon-ist
        HTTPS_PROXY: http://pp-webproxy.bns:8080
        NO_PROXY: passport-oauth-ist.apps.cloud.bns

        # Service to service authentication
        AUTH_S2S_CLIENTID: 9d5ee90f-4595-4df6-948b-0ccb5a23cc57
        PASSPORT_SSO_CLIENT_ID: 57de5eb9-f40f-4ad3-ac21-dd99a11234ce
        AUTH_S2S_CLAIM_EXPIRESIN: 1h
        AUTH_S2S_CLAIM_NOTBEFORE: -10s
        AUTH_S2S_CLAIM_SCOPE: ca:baas:campaigns:read ca:baas:campaign-rules:read ca:baas:dispositions:write customer.customer.bdms.webTrackId.read
        AUTH_S2S_PRIVATEKEY_ALGO: RS256
        AUTH_S2S_TOKEN_PATH: 'auth.token'
        AUTH_S2S_CLAIMS_PATH: 'auth.claims'
        AUTH_ACCESS_TOKEN_URI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/token'
        AUTH_OPAQUE_EXCHANGE_URI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/tokeninfo'
        AUTH_S2S_JWKS_URI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs'
        # Backup public JWKS used for fallback mechanism: https://jira.agile.bns/browse/PIGEON-5538
        # This backup JWKS could be fetched at: https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs
        AUTH_S2S_JWKS_BACKUP: '{"keys":[{"kty":"RSA","e":"AQAB","use":"sig","kid":"QWo32GiQbjJ-8_ciRzJoGGNQG0cCGpdeNu5afhJAAiA","alg":"RS256","n":"way67Wsm3PwL13FFQv8pB-_rPPOXq0ARxTW4fDPtpn3GmnCiY7f1X4Sk5Iye8YfsRLZYBhUtkdRrZ3bPJKjuIcTEC2Iz_th86Y-6jm05EEYERh7iOKZMCLtVslkkRW5x8MNajoTnbvPYlFpaZ9lU7vylbtR0DVg9NhlnBSnLZx0BdMXM8oApoYqS7UxqvrpSS70pQoQqGXjki2iO0fYSfNlosTt6mIBnD6PWESdLpQDb739Qzy1auXOc8T4JQFWznNVgS921-8xbfR7D3yNSPSaduYwXP8hML3o-vcCcPkYglhzywK7vDUpB1bsZoeoEW76RXFiyB-jEaUfJpuGq3Q"}]}'
        PASSPORT_AUTHORIZE_URI: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/authorize'
        REGISTERED_REDIRECT_URI: 'https://pigeon-web-ist.apps.stg.azr-use2-pcf.cloud.bns/auth/authorization'
        PASSPORT_PUBLIC_KEY: 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFudlVuVDd2UHVTSWpkM3luT0JCQgpqT1JlTnI3NGFFTDNYZUNsQ3ZXOEtBcDlLTFY2bXMvVElHS0hLT0ZUZWNHOUlYUU9zVlJwajY4R05TV1gwWFI2CkxaTkdiMDJsc1FXam5sNWpCaWZ0ajhWblRJazlxdm5qZlg3cDY0M3JMeEZEQXlMQ0g1NmxidGhpeWIzOGQzK3gKWWoybTAwWGJXZ2JRaWtjVDRnQ0R3ZkkvcXZ1YWRteU5sVHB5ZFRqQ0ZEUWQ3L1VHUUZYZzB1dk01MkZUUGxLZgpmQ0VQNGtlYjlHRFdOS0J5dC8xOTlYZUJ3YTRNWnJHRDJPU081RXlGc0xOWCtVOHdXcm44M3FpWWtxSUNvWVhtCjVGMW4ySEFQaG1DRFFDbXMwUGdtYVBUY0lkVjltVWR6U0hxazBPU25Tb1dxVWRDQU5QQ3ZESVVRTEF2UVVueTQKdlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t'
        VALID_RETURN_DOMAIN: '.cloud.bns'

        # Onyx SSO
        PASSPORT_SSO_ONYX_CLIENT_ID: 3bca452e-d186-4af1-8f25-1df21b967c0e
        FEDERATION_SESSION_SECRET: 1a426bb0-1660-11ec-9621-0242ac130002
        ONYX_REGISTERED_REDIRECT_URI: https://pigeon-web-ist.apps.stg.azr-use2-pcf.cloud.bns/ccau/auth/authorization
        SERVICE_PASSPORT_REVOKE: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/revoke'
        CLIENT_SESSION_TIMEOUT: 3600000

        # Launch Darkly
        LAUNCH_DARKLY_USER_ID: '0f9deeb3-a978-4f47-abf6-71edd44a6764'
        LAUNCH_DARKLY_CLIENT_SIDE_ID: '5e74d56d00cad60764a4f12f'

        # content security policy
        CSP_IMAGE_SRC: '["https://*.ctfassets.net","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net","data:"]'
        CSP_FRAME_SRC: '["https://*.demdex.net"]'
        CSP_SCRIPT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'
        CSP_CONNECT_SRC: '["https://*.launchdarkly.com","https://assets.adobedtm.com","https://*.demdex.net","https://cm.everesttech.net"]'

        # logging options
        LOG_NAME: pigeon-web-ist
        LOG_OBFUSCATE: 1
        LOG_COLORIZE: 0
        LOG_PRETTY_PRINT: 0
        LOG_SIEM: 1
        IGNORED_ROUTES_FOR_LOGGING: '["/health"]'

        # image caching for ABM expiry time
        IMAGE_CACHE_EXPIRY_TIME: 900000

        DEPLOY_REGION: USE2

        # SOLEndpoints
        SOL_ENDPOINTS: '{"istred": "https://devredprodapp.scotiaonline.pp.scotiabank.com:4135/prodserv", "ist_green": "https://devgreenprodapp.scotiaonline.pp.scotiabank.com:4135/prodserv", "istblack": "https://devblackprodapp.scotiaonline.pp.scotiabank.com:4135/prodserv"}'

        # FulfillableTargetEndpoints
        SCOTIAHOME_ENDPOINTS: '{"ist": "https://mtg.nonprod.scotiabank.com:9100/mrlc-ui/authentication"}'

        SCRL_ENDPOINTS: '{"istred": "http://************:8553/SmallCompaniesOnboardC-proxy/rest/singleSignOn", "ist": "http://************:8553/SmallCompaniesOnboardC-proxy/rest/singleSignOn", "istgreen": "http://************:8553/SmallCompaniesOnboardC-proxy/rest/singleSignOn"}'

        # rate limiting
        RATE_LIMIT_CLIENT_CAMPAIGNS_MAX: 30
        RATE_LIMIT_OVERALL_CAMPAIGNS_MAX: 500
        RATE_LIMIT_OVERALL_RENDERED_CAMPAIGNS_MAX: 400
        RATE_LIMIT_CDP_TRUSTED_IP: '***********/23'
